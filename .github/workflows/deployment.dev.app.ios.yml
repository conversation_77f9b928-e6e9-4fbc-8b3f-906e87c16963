name: deployment.dev.app.ios

on:
  push:
    branches: ["develop"]
    paths:
      - "packages/as-app/**"
      - "!**.md"
  workflow_dispatch:

permissions:
  contents: write

env:
  FLUTTER_VERSION: 3.29.2
  APP_VERSION_NAME: 0.1.0
        
jobs:
  build_ios:
    runs-on: macos-15

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      # Version Xcode
      - name: Select Xcode version
        run: sudo xcode-select -s '/Applications/Xcode_16.2.app/Contents/Developer'

      - name: Set mobileapp build number
        run: |         
          BUILD_NUMBER=$(date "+%y%m%d%H%M")
          echo "APP_BUILD_NUMBER=$BUILD_NUMBER" >> $GITHUB_ENV
      
      - name: Set mobileapp tag prefix
        run: |          
          APP_VERSION=$(echo "$VERSION_NAME+$BUILD_NUMBER")
          echo "APP_VERSION=$APP_VERSION" >> $GITHUB_ENV
        env:
          BUILD_NUMBER: ${{ env.APP_BUILD_NUMBER }}
          VERSION_NAME: ${{ env.APP_VERSION_NAME }}

      - name: Install Apple Certificate
        uses: apple-actions/import-codesign-certs@v3
        with:
          p12-file-base64: ${{ secrets.P12_BASE64 }}
          p12-password: ${{ secrets.P12_PASSWORD }}

      - name: Install the provisioning profile
        env:
          PROVISIONING_PROFILE_BASE64: ${{ secrets.PROVISIONING_PROFILE_BASE64 }}
        run: |
          # create variables
          PP_PATH=$RUNNER_TEMP/build_pp.mobileprovision

          # import provisioning profile from secrets
          echo -n "$PROVISIONING_PROFILE_BASE64" | base64 --decode -o $PP_PATH

          # apply provisioning profile
          mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles
          cp $PP_PATH ~/Library/MobileDevice/Provisioning\ Profiles

      - name: Install and set Flutter version
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true
          cache-key: flutter-${{ env.FLUTTER_VERSION }}
          cache-path: ${{ runner.tool_cache }}/flutter-${{ env.FLUTTER_VERSION }}
          architecture: x64

      - name: Config app version
        run: |
          sed -i '' "s/^version: .*/version: ${{ env.APP_VERSION }}/" packages/as-app/pubspec.yaml

      - name: Update Info.plist
        run: |
          cd packages/as-app/
          /usr/libexec/PlistBuddy -c "Add ITSAppUsesNonExemptEncryption bool false" ios/Runner/Info.plist
          /usr/libexec/Plistbuddy -c "Set CFBundleVersion ${{ env.APP_BUILD_NUMBER }}" "ios/Runner/Info.plist"

      - name: Restore packages
        run: |
          cd packages/as-app/
          flutter clean
          flutter pub get

      - name: Build Flutter app
        run: |
          cd packages/as-app/
          echo '<debug>'
          cat pubspec.yaml
          echo '</debug>'
          flutter build ios -t lib/main.dart --release --no-codesign

      - name: Build resolve Swift dependencies
        run: xcodebuild -resolvePackageDependencies -workspace packages/as-app/ios/Runner.xcworkspace -scheme Runner -configuration Release
      
      - name: Build xArchive
        run: |
          cd packages/as-app/
          xcodebuild  -workspace ios/Runner.xcworkspace \
                      -scheme Runner \
                      -configuration Release \
                      -sdk iphoneos \
                      -archivePath ios/Runner.xcarchive \
                      DEVELOPMENT_TEAM=${{ secrets.DEVELOPMENT_TEAM }} \
                      PROVISIONING_PROFILE_SPECIFIER=${{ secrets.PROVISIONING_PROFILE_SPECIFIER }} \
                      AD_HOC_CODE_SIGNING_ALLOWED=NO \
                      CODE_SIGN_STYLE=Manual \
                      clean archive

      - name: Generate ExportOptions.plist
        run: |
          if [ -f packages/as-app/ios/ExportOptions.plist ]; then
            rm packages/as-app/ios/ExportOptions.plist
            echo "File ExportOptions.plist removed successfully"
          fi

          cat <<EOF > packages/as-app/ios/ExportOptions.plist
          <?xml version="1.0" encoding="UTF-8"?>
          <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
          <plist version="1.0">
          <dict>
            <key>method</key>
            <string>app-store</string>
            <key>teamID</key>
            <string>${{ secrets.DEVELOPMENT_TEAM }}</string>
            <key>signingStyle</key>
            <string>manual</string>
            <key>provisioningProfiles</key>
            <dict>
              <key>${{ secrets.APP_BUNDLE_ID }}</key>
              <string>${{ secrets.PROVISIONING_PROFILE_SPECIFIER }}</string>
            </dict>
          </dict>
          </plist>
          EOF

          echo '<debug>'
          cat packages/as-app/ios/ExportOptions.plist
          echo '</debug>'

      - name: Export Archive
        run: |
          cd packages/as-app/
          xcodebuild -exportArchive \
                    -archivePath ios/Runner.xcarchive \
                    -exportPath ios/Runner.ipa \
                    -exportOptionsPlist ios/ExportOptions.plist \
                    AD_HOC_CODE_SIGNING_ALLOWED=NO

      - name: Setup Ruby environment
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.0'
          bundler-cache: true

      - name: Install Bundle && Fastlane
        working-directory: packages/as-app/ios
        run: |
          gem install bundler:1.17.2
          bundle install

      - name: Upload to TestFlight
        working-directory: packages/as-app/ios
        run: |
          export FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD=${{ secrets.FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD }}
          
          bundle exec fastlane pilot upload \
                    --ipa Runner.ipa/as_dev.ipa \
                    --apple_id ${{ secrets.APPLE_ID }} \
                    --skip_submission true \
                    --skip_waiting_for_build_processing true \
                    --distribute_external false \
                    --notify_external_testers false
        env: 
          APP_IDENTIFIER: ${{ secrets.APP_BUNDLE_ID }}
          APPLE_ID: ${{ secrets.APPLE_ACCOUNT_ID }}
          ITC_TEAM_ID: ${{ secrets.ITC_TEAM_ID }}
          TEAM_ID: ${{ secrets.DEVELOPMENT_TEAM }}
    
      - name: Push mobileapp build number tag
        uses: actions/github-script@v6
        with:
          script: |
            github.rest.git.createRef({
              owner: context.repo.owner,
              repo: context.repo.repo,
              ref: 'refs/tags/ios-release-b${{ env.APP_BUILD_NUMBER }}',
              sha: context.sha
            })
