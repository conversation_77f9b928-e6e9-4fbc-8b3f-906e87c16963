# This workflow will build & test a golang project
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-go

name: test.as-api

on:
  pull_request:
    branches-ignore:
      - 'feature/release-staging'
    paths:
      - "packages/as-api/**"
      - "!**.md"
  workflow_dispatch:

env:
  ENV: test
  GIT_REVISION: test
  API_FOLDER: packages/as-api

jobs:
  golangci:
    name: linter.as-api
    runs-on: ubuntu-latest
    environment: test
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.23'
          cache-dependency-path: ${{env.API_FOLDER}}/go.sum

      - name: Go Lint
        uses: golangci/golangci-lint-action@v6
        with:
          version: v1.62.2
          working-directory: ${{env.API_FOLDER}}
          args: --config=.golangci.yml --timeout=5m
  
  test:
    # This name will be displayed on GitHub branch configuration.
    # Set root.name for consistency.
    name: test.as-api
    runs-on: ubuntu-latest
    environment: test

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.23'
          cache-dependency-path: ${{env.API_FOLDER}}/go.sum

      - name: Build
        run: cd ${{env.API_FOLDER}} && make build

      - name: Test
        run: cd ${{env.API_FOLDER}} && make test
