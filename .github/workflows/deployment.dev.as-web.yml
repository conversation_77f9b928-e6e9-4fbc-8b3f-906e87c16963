name: deployment.dev.as-web

on:
  push:
    branches:
      - 'develop'
    paths:
      - "packages/as-web/**"
      - "!**.md"
  workflow_dispatch:

env:
  WEBAPP_FOLDER: packages/as-web
  ENV: dev
  AWS_REGION: ap-northeast-1
  OIDC_ROLE: arn:aws:iam::448049819763:role/github_oidc
  AWS_S3_PATH: s3://as-web-user-dev

permissions:
  contents: read

# Deployment flow:
jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Clone repository
        uses: actions/checkout@v4

      - name: Configure A<PERSON> credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.OIDC_ROLE }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Deploy to S3
        run: |
          aws s3 sync ${{env.WEBAPP_FOLDER}}/html ${{ env.AWS_S3_PATH }}
          aws s3 ls ${{ env.AWS_S3_PATH }}
          echo "🍏 This job's status is ${{ job.status }}."
