# This name will be displayed on GitHub Actions sidebar.
# Set filename without extension for consistency.
name: test.as-app

on:
  pull_request:
    paths:
      - "packages/as-app/**"
      - "!**.md"
  workflow_dispatch:

env:
  AS_APP_FOLDER: packages/as-app
  FLUTTER_VERSION: 3.29.2

jobs:
  test:
    # This name will be displayed on GitHub branch configuration.
    # Set root.name for consistency.
    name: test.as-app
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{env.FLUTTER_VERSION}}
          channel: 'stable'
          cache: true
      - run: flutter --version

      - name: Test
        run: cd ${{env.AS_APP_FOLDER}} && make test

      - name: Build
        run: cd ${{env.AS_APP_FOLDER}} && make build
