name: deployment.dev.as-worker

on:
  push:
    branches:
      - 'develop'
    paths:
      - "packages/as-api/**"
      - "!**.md"
  workflow_dispatch:

env:
  API_FOLDER: packages/as-api
  ENV: dev
  AWS_REGION: ap-northeast-1
  OIDC_ROLE: arn:aws:iam::448049819763:role/github_oidc

permissions:
  id-token: write
  contents: read

# Deployment flow:
jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    environment: dev
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.OIDC_ROLE }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: ${{ github.sha }}
          ECR_REPOSITORY: as-${{ env.ENV }}-worker
        run: |
          # Build a docker container and
          # push it to ECR so that it can
          # be deployed to ECS.
          cd ${{ env.API_FOLDER }}
          docker build -f Dockerfile.worker.release -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT

      - name: Download task definition
        env:
          TASK_NAME: as-${{env.ENV}}-api
        run: |
          aws ecs describe-task-definition --task-definition $TASK_NAME \
          --query taskDefinition > task-definition.json

      - name: Fill in the new image ID in the Amazon ECS task definition
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: task-definition.json
          container-name: as-${{ env.ENV }}-api
          image: ${{ steps.build-image.outputs.image }}

      - name: Deploy Amazon ECS task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v1
        with:
          task-definition: ${{ steps.task-def.outputs.task-definition }}
          service: as-${{ env.ENV }}-worker
          cluster: as-${{ env.ENV }}-ecs-cluster
          wait-for-service-stability: true
