name: deployment.dev.as-admin

on:
  push:
    branches:
      - 'develop'
    paths:
      - "packages/as-admin/**"
      - "!**.md"
  workflow_dispatch:

env:
  WEBAPP_FOLDER: packages/as-admin
  SCHEMA_FOLDER: packages/as-schema
  ENV: dev
  AWS_REGION: ap-northeast-1
  OIDC_ROLE: arn:aws:iam::448049819763:role/github_oidc
  AWS_S3_PATH: s3://as-web-admin-dev
  VITE_APP_API_URL: https://api.dev.dtnewec.com/admin/v1
  VITE_APP_ENABLE_API_MOCKING: false
  CLOUDFRONT_DISTRIBUTION_ID: EKQF56GK20DB6
  VITE_APP_CDN_URL: https://cdn.dev.dtnewec.com
  VITE_APP_APP_LINK: https://dev.dtnewec.com

permissions:
  contents: read

jobs:
  install-cache:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Commit
        uses: actions/checkout@v4
        
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: lts/*

      - name: Cache dependencies
        uses: actions/cache@v4
        id: cache-dependencies
        with:
          path: ${{ env.WEBAPP_FOLDER }}/node_modules
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: ${{ runner.os }}-yarn-

      - name: Install all related dependencies
        if: steps.cache-dependencies.outputs.cache-hit != 'true'
        run: cd ${{env.WEBAPP_FOLDER}} && yarn install --force --non-interactive
        
      - name: Set up schema environment
        run: |
          cd ${{ env.SCHEMA_FOLDER }} && make install
          
      - name: Build schema
        run: |
          cd ${{ env.SCHEMA_FOLDER }}
          mkdir -p build
          make build-admin
          
      - name: Cache schema build
        uses: actions/cache@v4
        with:
          path: ${{ env.SCHEMA_FOLDER }}/build
          key: ${{ runner.os }}-schema-${{ github.sha }}
          
      - name: Bundle schema to admin
        run: cd ${{ env.WEBAPP_FOLDER }} && make bundle
  
  lint-format:
    runs-on: ubuntu-latest
    needs: install-cache
    steps:
      - name: Checkout Commit
        uses: actions/checkout@v4
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: lts/*
      - name: Restore install dependencies
        uses: actions/cache@v4
        id: cache-dependencies
        with:
          path: ${{env.WEBAPP_FOLDER}}/node_modules
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: ${{ runner.os }}-yarn-
          
      - name: Restore schema build
        uses: actions/cache@v4
        with:
          path: ${{ env.SCHEMA_FOLDER }}/build
          key: ${{ runner.os }}-schema-${{ github.sha }}
          
      - name: Bundle schema to admin
        run: cd ${{env.WEBAPP_FOLDER}} && make bundle
          
      - name: Run lint
        run: cd ${{env.WEBAPP_FOLDER}} && yarn lint
      - name: Run prettier
        run: cd ${{env.WEBAPP_FOLDER}} && yarn format
        
  type-check:
    runs-on: ubuntu-latest
    needs: install-cache
    steps:
      - name: Checkout Commit
        uses: actions/checkout@v4
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: lts/*
      - name: Restore install dependencies
        uses: actions/cache@v4
        id: cache-dependencies
        with:
          path: ${{env.WEBAPP_FOLDER}}/node_modules
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: ${{ runner.os }}-yarn-
          
      - name: Restore schema build
        uses: actions/cache@v4
        with:
          path: ${{ env.SCHEMA_FOLDER }}/build
          key: ${{ runner.os }}-schema-${{ github.sha }}
          
      - name: Bundle schema to admin
        run: cd ${{env.WEBAPP_FOLDER}} && make bundle
          
      - name: Check types
        run: cd ${{env.WEBAPP_FOLDER}} && yarn check-types

  # unit-test:
  #   runs-on: ubuntu-latest
  #   needs: install-cache
  #   steps:
  #     - name: Checkout Commit
  #       uses: actions/checkout@v4
  #     - name: Setup Node
  #       uses: actions/setup-node@v4
  #       with:
  #         node-version: lts/*
  #     - name: Set environment variables
  #       run: |
  #         echo "VITE_APP_API_URL=${{ env.VITE_APP_API_URL }}" > .env
  #         echo "VITE_APP_ENABLE_API_MOCKING=${{ env.VITE_APP_ENABLE_API_MOCKING }}" > .env
  #     - name: Restore install dependencies
  #       uses: actions/cache@v4
  #       id: cache-dependencies
  #       with:
  #         path: ${{env.WEBAPP_FOLDER}}/node_modules
  #         key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
  #         restore-keys: ${{ runner.os }}-yarn-
  #     - name: Run unit test
  #       run: cd ${{env.WEBAPP_FOLDER}} && yarn test

  build:
    runs-on: ubuntu-latest
    needs: ['lint-format', 'type-check']
    steps:
      - name: Checkout Commit
        uses: actions/checkout@v4
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: lts/*
      - name: Set environment variables
        run: |
          echo "VITE_APP_API_URL=${{ env.VITE_APP_API_URL }}" > .env
          echo "VITE_APP_ENABLE_API_MOCKING=${{ env.VITE_APP_ENABLE_API_MOCKING }}" > .env
          echo "VITE_APP_CDN_URL=${{ env.VITE_APP_CDN_URL }}" > .env
          echo "VITE_APP_APP_LINK=${{ env.VITE_APP_APP_LINK }}" > .env
      - name: Restore install dependencies
        uses: actions/cache@v4
        id: cache-dependencies
        with:
          path: ${{env.WEBAPP_FOLDER}}/node_modules
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: ${{ runner.os }}-yarn-
          
      - name: Restore schema build
        uses: actions/cache@v4
        with:
          path: ${{ env.SCHEMA_FOLDER }}/build
          key: ${{ runner.os }}-schema-${{ github.sha }}
          
      - name: Bundle schema to admin
        run: cd ${{env.WEBAPP_FOLDER}} && make bundle
        
      - name: Run build application
        run: |
          cd ${{env.WEBAPP_FOLDER}} && yarn build
      - uses: actions/upload-artifact@v4
        with:
          name: web
          path: ${{env.WEBAPP_FOLDER}}/dist

  deploy:
    name: "upload artifact to aws s3"
    needs: build
    if: |
      !failure() && !cancelled() && needs.build.result == 'success'
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Download a single artifact
        # https://github.com/actions/download-artifact
        uses: actions/download-artifact@v4
        id: download-artifact
        with:
          name: web
          path: ${{env.WEBAPP_FOLDER}}/dist

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.OIDC_ROLE }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Copy files to the test website with the AWS CLI
        run: |
          ls -lrt ${{steps.download-artifact.outputs.download-path}}
          aws s3 sync ${{steps.download-artifact.outputs.download-path}} ${{ env.AWS_S3_PATH }}
          aws s3 ls ${{ env.AWS_S3_PATH }}
          echo "🍏 This job's status is ${{ job.status }}."

      - name: Invalidate index.html in CloudFront
        run: |
          aws cloudfront create-invalidation --distribution-id ${{ env.CLOUDFRONT_DISTRIBUTION_ID }} --paths "/index.html"
          echo "✅ CloudFront invalidation created for /index.html"
