variable "sns_topic_arn" {
  description = "sns topic cloudwatch arn"
}

variable "rds_id" {
  description = "RDS Identify"
}

variable "statistic_period" {
  description = "The number of seconds that make each statistic period."
  type        = string
  default     = "60"
}

variable "evaluation_period" {
  description = "The evaluation period over which to use when triggering alarms."
  type        = string
  default     = "5"
}

variable "rds_freeable_memory_threshold" {
  description = "The minimum amount of available random access memory in Byte."
  type        = number
  default     = 102400000
  # 0.1 Gb in Byte
  # Default instance is db.t3.micro (2vCPUs and 1GiB RAM)
}

variable "rds_free_storage_space_threshold" {
  description = "The minimum amount of available storage space in Byte."
  type        = number
  default     = 8589934592

  # 8 Gigabyte in Byte
  # Default Allocated storage is 20GB
}
