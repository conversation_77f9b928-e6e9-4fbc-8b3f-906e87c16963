variable "sns_topic_arn" {
  description = "sns topic cloudwatch arn"
}

variable "ecs_service_name" {
  description = "ECS service name"
}

variable "ecs_cluster_name" {
  description = "ECS cluster name"
}

### CloudWatch
variable "statistic_period" {
  description = "The number of seconds that make each statistic period."
  type        = string
  default     = "60"
}

variable "evaluation_period" {
  description = "The evaluation period over which to use when triggering alarms."
  type        = string
  default     = "5"
}
