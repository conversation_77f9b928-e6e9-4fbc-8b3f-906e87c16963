### CPU utilization - Alert when 80%+ in 5 minutes interval
resource "aws_cloudwatch_metric_alarm" "ecs_cpu_utilization_high" {
  alarm_name          = "ecs_${var.ecs_service_name}_cpu_utilization_high"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = var.evaluation_period
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ECS"
  period              = var.statistic_period
  statistic           = "Average"
  threshold           = 80
  alarm_description   = "Average ECS API CPU utilization over last 5 minutes too high"
  alarm_actions       = var.sns_topic_arn

  dimensions = {
    "ServiceName" = var.ecs_service_name
    "ClusterName" = var.ecs_cluster_name
  }
}

## Memory utilization - Alert when 80%+ in 5 minutes interval
resource "aws_cloudwatch_metric_alarm" "ecs_memory_utilization_high" {
  alarm_name          = "ecs_${var.ecs_service_name}_memory_utilization_high"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = var.evaluation_period
  metric_name         = "MemoryUtilization"
  namespace           = "AWS/ECS"
  period              = var.statistic_period
  statistic           = "Average"
  threshold           = 80
  alarm_description   = "Average ECS API Memory utilization over last 5 minutes too high"
  alarm_actions       = var.sns_topic_arn

  dimensions = {
    "ServiceName" = var.ecs_service_name
    "ClusterName" = var.ecs_cluster_name
  }
}

### Disk utilization - Alert when 60%+ in 5 minutes interval
resource "aws_cloudwatch_metric_alarm" "ecs_disk_utilization_high" {
  alarm_name          = "ecs_${var.ecs_service_name}_disk_utilization_high"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = var.evaluation_period
  metric_name         = "EphemeralStorageUtilized"
  namespace           = "ECS/ContainerInsights"
  period              = var.statistic_period
  statistic           = "Average"
  threshold           = 12
  alarm_description   = "Average ECS Webapp Memory utilization over last 5 minutes too high"
  alarm_actions       = var.sns_topic_arn

  dimensions = {
    "ServiceName" = var.ecs_service_name
    "ClusterName" = var.ecs_cluster_name
  }
}
