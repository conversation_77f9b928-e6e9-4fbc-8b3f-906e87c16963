# create security group
resource "aws_security_group" "sg" {
  name        = var.sg_name
  description = "Security group"
  vpc_id      = var.vpc_id
}

resource "aws_security_group_rule" "egress" {
  count             = length(var.egress_ports)
  type              = "egress"
  from_port         = var.egress_ports[count.index]
  to_port           = var.egress_ports[count.index]
  protocol          = var.egress_protocol
  cidr_blocks       = var.egress_cidr_blocks
  security_group_id = aws_security_group.sg.id
}

resource "aws_security_group_rule" "ingress" {
  count                    = length(var.ingress_ports)
  type                     = "ingress"
  from_port                = var.ingress_ports[count.index]
  to_port                  = var.ingress_ports[count.index]
  protocol                 = var.ingress_protocol
  security_group_id        = aws_security_group.sg.id
  cidr_blocks              = length(var.ingress_cidr_blocks) > 0 ? var.ingress_cidr_blocks : null
  source_security_group_id = length(var.prefix_list_ids) > 0 ? var.prefix_list_ids[count.index] : null
}
