provider "aws" {
  alias  = "useast1"
  region = "us-east-1" // To work with CloudFront, you must also specify the region us-east-1 (N. Virginia) on the AWS provider
}

resource "aws_wafv2_web_acl" "cloudfront" {
  name        = "${local.name}-WebACL-cloudfront"
  description = "for CLOUDFRONT"
  scope       = "CLOUDFRONT"
  provider    = aws.useast1

  default_action {
    allow {}
  }

  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "${local.name}-WebACL-cloudfront"
    sampled_requests_enabled   = true
  }

  # ---------------------------------------------
  # 通常ボットやその他の脅威に関連付けられている IP アドレスをブロックする場合に役立ちます。 ここの IP アドレスは AWS 管理です。
  rule {
    name     = "AWS-AWSManagedRulesAmazonIpReputationList"
    priority = 210

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesAmazonIpReputationList"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWS-AWSManagedRulesAmazonIpReputationList"
      sampled_requests_enabled   = true
    }
  }
  # -----------------------------------------------
  # Web アプリケーション防御の一般的なルールが含まれています。 OWASP 出版物や CVE で解説されている脆弱性を含んでいます。 AWS 管理のルールです。
  rule {
    name     = "AWS-AWSManagedRulesCommonRuleSet"
    priority = 220

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesCommonRuleSet"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWS-AWSManagedRulesCommonRuleSet"
      sampled_requests_enabled   = true
    }
  }

  # ------------------------------------------------
  # Linux関連
  # https://dev.classmethod.jp/articles/awswaf-logcheck/
  rule {
    name     = "AWS-AWSManagedRulesLinuxRuleSet"
    priority = 230

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesLinuxRuleSet"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWS-AWSManagedRulesLinuxRuleSet"
      sampled_requests_enabled   = true
    }
  }

  # ------------------------------------------------
  # 既知の不正な入力マネージドルールグループ
  rule {
    name     = "AWS-AWSManagedRulesKnownBadInputsRuleSet"
    priority = 240

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesKnownBadInputsRuleSet"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWS-AWSManagedRulesKnownBadInputsRuleSet"
      sampled_requests_enabled   = true
    }
  }

  # ------------------------------------------------
  # 管理者保護マネージドルールグループ
  rule {
    name     = "AWS-AWSManagedRulesAdminProtectionRuleSet"
    priority = 260

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesAdminProtectionRuleSet"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWS-AWSManagedRulesAdminProtectionRuleSet"
      sampled_requests_enabled   = true
    }
  }
}
