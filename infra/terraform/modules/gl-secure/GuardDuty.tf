# ===============================================
# GuardDuty
# ===============================================   

# Resource to create GuardDuty detector if it doesn't exist
resource "aws_guardduty_detector" "this" {
  count  = var.enable_guardduty ? 1 : 0
  enable = var.enable_guardduty
}

# Resource to create GuardDuty filter
resource "aws_guardduty_filter" "this" {
  count       = var.enable_guardduty ? 1 : 0
  detector_id = aws_guardduty_detector.this[0].id
  name        = "${local.name}-filters"
  action      = "ARCHIVE"
  rank        = 1

  finding_criteria {
    criterion {
      field = "type"
      equals = [
        "UnauthorizedAccess:IAMUser/TorIPCaller",
        "UnauthorizedAccess:IAMUser/MaliciousIPCaller.Custom",
        "UnauthorizedAccess:IAMUser/ConsoleLoginSuccess.B",
        "UnauthorizedAccess:IAMUser/MaliciousIPCaller",
        "UnauthorizedAccess:IAMUser/UnusualASNCaller",
        "PenTest:IAMUser/KaliLinux",
        "UnauthorizedAccess:IAMUser/InstanceCredentialExfiltration",
        "UnauthorizedAccess:IAMUser/ConsoleLogin",
        "Stealth:IAMUser/PasswordPolicyChange",
        "Stealth:IAMUser/CloudTrailLoggingDisabled",
        "Stealth:IAMUser/LoggingConfigurationModified",
        "ResourceConsumption:IAMUser/ComputeResources",
        "Recon:IAMUser/TorIPCaller",
        "Recon:IAMUser/MaliciousIPCaller.Custom",
        "Recon:IAMUser/MaliciousIPCaller",
        "Recon:IAMUser/NetworkPermissions",
        "Recon:IAMUser/ResourcePermissions",
        "Recon:IAMUser/UserPermissions",
        "Persistence:IAMUser/NetworkPermissions",
        "Persistence:IAMUser/ResourcePermissions",
      ]
    }
  }
}
