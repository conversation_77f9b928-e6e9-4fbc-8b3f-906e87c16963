# CloudWatch Logs
variable "log_group_name" {
  type        = string
  description = "The name of the log group"
}

variable "log_stream_name" {
  type        = string
  description = "The name of the log stream"
  default     = "ecs"
}

# TaskDefinition
variable "task_family" {
  type        = string
  description = "A unique name for your task definition"
}

variable "cpu" {
  type        = string
  description = "Number of cpu units used by the task"
}

variable "memory" {
  type        = string
  description = "Amount (in MiB) of memory used by the task"
}

variable "execution_role_arn" {
  type        = string
  description = "ARN of the task execution role that the Amazon ECS container agent and the Docker daemon can assume"
}

variable "task_role_arn" {
  type        = string
  description = "ARN of IAM role that allows your Amazon ECS container task to make calls to other AWS services"
}

variable "aws_region" {
  type        = string
  description = "AWS region, default is Tokyo"
  default     = "ap-northeast-1"
}

variable "environment" {
  type        = string
  description = "Environment definition"
  default     = "dev"
}

# ECS
variable "container_name" {
  type        = string
  description = "Name of the container definition"
}

variable "container_port" {
  type        = number
  description = "Port of the container definition"
}

variable "ecr_url" {
  type        = string
  description = "The URL of the ECR repository"
}

variable "ecs_service_name" {
  type        = string
  description = "Name of the service"
}

variable "cluster_id" {
  type        = string
  description = "ARN of an ECS cluster"
}

variable "desired_count" {
  type        = number
  description = "Number of instances of the task definition to place and keep running"
}

variable "vpc_private_subnet_ids" {
  type        = list(string)
  description = "Subnets associated with the task or service"
}

variable "fargate_security_group_id" {
  type        = string
  description = "Security groups associated with the task or service"
}

variable "alb_group_arn" {
  type        = string
  description = "ARN of the Load Balancer target group to associate with the service"
}
