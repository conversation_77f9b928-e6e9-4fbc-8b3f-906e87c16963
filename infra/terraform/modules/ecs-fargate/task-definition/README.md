# AWS ECS Task Definition Module

This Terraform module manages a revision of an ECS Task Definition to be used in `aws_ecs_service`

## Usage

```hcl
module "task_def" {
  source = "./modules/ecs-fargate/task-definition"

  # Specify input variables here, if any
}
```

## Inputs
- `task_family`: A unique name for your task definition
- `execution_role_arn`: ARN of the task execution role that the Amazon ECS container agent and the Docker daemon can assume
- `task_role_arn`: ARN of IAM role that allows your Amazon ECS container task to make calls to other AWS services
- `ecr_url`: The URL of the ECR repository
- `container_name`: Name of the container definition
- `container_port`: Port of the container definition
- `log_group`: Name of the Log Group in CloudWatch Logs
- `log_region`: AWS region where the Log Group and Log Streams will be created
- `log_stream_prefix`: Prefix for the names of the Log Streams in CloudWatch Logs
- `environment`: Environment definition

## Outputs
- `task_def_arn`: Full ARN of the Task Definition (including both family and revision)
