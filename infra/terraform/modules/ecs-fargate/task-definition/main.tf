# Task Definition
resource "aws_ecs_task_definition" "task_def" {
  family                   = var.task_family
  requires_compatibilities = ["FARGATE"]
  network_mode             = "awsvpc"
  memory                   = var.memory
  cpu                      = var.cpu
  execution_role_arn       = var.execution_role_arn
  task_role_arn            = var.task_role_arn

  container_definitions = jsonencode([
    {
      name : var.container_name,
      logConfiguration = {
        logDriver = "awslogs",
        options = {
          awslogs-group         = var.log_group,
          awslogs-region        = var.log_region,
          awslogs-stream-prefix = var.log_stream_prefix
        }
      },
      network_mode = "awsvpc",
      portMappings = var.container_port != 0 ? [
        {
          containerPort = var.container_port
        }
      ] : [],
      image     = "${var.ecr_url}:latest",
      essential = true,
      environment = [
        {
          name  = "ENV",
          value = var.environment
        }
      ]
    }
  ])
}
