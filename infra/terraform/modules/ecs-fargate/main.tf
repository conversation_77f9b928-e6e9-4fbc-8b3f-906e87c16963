# CloudWatch Log
module "ecs_log" {
  source = "./cloudwatch-log"

  log_group_name  = var.log_group_name
  log_stream_name = var.log_stream_name
}

# TaskDefinition
module "task_definition" {
  source = "./task-definition"

  task_family        = var.task_family
  cpu                = var.cpu
  memory             = var.memory
  execution_role_arn = var.execution_role_arn
  task_role_arn      = var.task_role_arn

  ecr_url        = var.ecr_url
  container_name = var.container_name
  container_port = var.container_port

  log_group         = module.ecs_log.log_group_name
  log_stream_prefix = module.ecs_log.log_stream_name
  log_region        = var.aws_region

  environment = var.environment
}

# ECS Service
module "fargate" {
  source = "./fargate"

  ecs_service_name          = var.ecs_service_name
  cluster_id                = var.cluster_id
  task_def_arn              = module.task_definition.task_def_arn
  desired_count             = var.desired_count
  vpc_private_subnet_ids    = var.vpc_private_subnet_ids
  fargate_security_group_id = var.fargate_security_group_id
  alb_group_arn             = var.alb_group_arn
  container_name            = var.container_name
  container_port            = var.container_port
}
