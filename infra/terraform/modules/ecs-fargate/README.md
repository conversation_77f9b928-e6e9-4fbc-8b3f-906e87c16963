# AWS ECS Fargate Module

This Terraform module creates an ECS Fargate (including CloudWatch Logs) in AWS

## Usage

```hcl
module "fargate" {
  source = "../modules/ecs-fargate"

  # Specify input variables here, if any
}
```

## Inputs
- `log_group_name`: The name of the log group
- `log_stream_name`: The name of the log stream
- `task_family`: A unique name for your task definition
- `execution_role_arn`: ARN of the task execution role that the Amazon ECS container agent and the Docker daemon can assume
- `task_role_arn`: ARN of IAM role that allows your Amazon ECS container task to make calls to other AWS services
- `aws_region`: AWS region, default is Tokyo
- `environment`: Environment definition
- `container_name`: Name of the container definition
- `container_port`: Port of the container definition
- `ecr_url`: The URL of the ECR repository
- `ecs_service_name`: Name of the service
- `cluster_id`: ARN of an ECS cluster
- `desired_count`: Number of instances of the task definition to place and keep running
- `vpc_private_subnet_ids`: Subnets associated with the task or service
- `fargate_security_group_id`: Security groups associated with the task or service
- `alb_group_arn`: ARN of the Load Balancer target group to associate with the service

## Outputs
- (None)
