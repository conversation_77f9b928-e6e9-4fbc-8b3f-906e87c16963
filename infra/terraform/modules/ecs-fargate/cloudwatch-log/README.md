# AWS CloudWatch Logs Module

This Terraform module creates a CloudWatch Logs (including Log Group and Log Stream) in AWS

## Usage

```hcl
module "ecs_log" {
  source = "../modules/ecs-fargate/cloudwatch-log"

  # Specify input variables here, if any
}
```

## Inputs
- `log_group_name`: The name of the log group
- `log_stream_name`: The name of the log stream

## Outputs
- `log_group_name`: The name of the log group
- `log_stream_name`: The name of the log stream
