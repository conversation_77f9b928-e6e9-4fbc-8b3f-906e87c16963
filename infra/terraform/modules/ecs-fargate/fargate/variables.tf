variable "ecs_service_name" {
  type        = string
  description = "Name of the service"
}

variable "cluster_id" {
  type        = string
  description = "ARN of an ECS cluster"
}

variable "task_def_arn" {
  type        = string
  description = "Full ARN of the task definition that you want to run in your service"
}

variable "desired_count" {
  type        = number
  description = "Number of instances of the task definition to place and keep running"
}

variable "vpc_private_subnet_ids" {
  type        = list(string)
  description = "Subnets associated with the task or service"
}

variable "fargate_security_group_id" {
  type        = string
  description = "Security groups associated with the task or service"
}

variable "alb_group_arn" {
  type        = string
  description = "ARN of the Load Balancer target group to associate with the service"
}

variable "container_name" {
  type        = string
  description = "Name of the container to associate with the load balancer"
}

variable "container_port" {
  type        = number
  description = "Port on the container to associate with the load balancer"
}
