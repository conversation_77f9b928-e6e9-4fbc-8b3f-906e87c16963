# Subnet group
resource "aws_db_subnet_group" "rds" {
  name       = "${var.rds_name}-subnet-group"
  subnet_ids = var.private_rds_subnet_ids
}

# Random password
resource "random_password" "password" {
  length  = 16
  special = false
}

# Security group for RDS
resource "aws_security_group" "rds" {
  name   = "${var.rds_name}-sg"
  vpc_id = var.vpc_id

  /*
  ingress {
    protocol        = "tcp"
    from_port       = var.rds_port
    to_port         = var.rds_port
    security_groups = var.ingress_security_group_ids
  }
  */
  /*
  ingress {
    protocol    = "tcp"
    from_port   = var.rds_port
    to_port     = var.rds_port
    cidr_blocks = ["0.0.0.0/0"]
  }
  */
  egress {
    protocol    = "-1"
    from_port   = 0
    to_port     = 0
    cidr_blocks = ["0.0.0.0/0"]
  }
}
# for -bastion-host-sg
resource "aws_security_group_rule" "ingress_security_group" {
  count                    = length(var.ingress_security_group_ids)
  security_group_id        = aws_security_group.rds.id
  protocol                 = "tcp"
  type                     = "ingress"
  from_port                = var.rds_port
  to_port                  = var.rds_port
  source_security_group_id = var.ingress_security_group_ids[count.index]
}

# for -api-fargate-sg
resource "aws_security_group_rule" "inbound_sg_ids" {
  count                    = length(var.allow_inbound_sg_ids)
  security_group_id        = aws_security_group.rds.id
  protocol                 = "tcp"
  type                     = "ingress"
  from_port                = var.rds_port
  to_port                  = var.rds_port
  source_security_group_id = var.allow_inbound_sg_ids[count.index]
}

# RDS
resource "aws_db_instance" "postgres" {
  identifier                      = "${var.rds_name}-postgres"
  db_name                         = var.db_name
  username                        = var.db_username
  password                        = random_password.password.result
  port                            = var.rds_port
  engine                          = "postgres"
  engine_version                  = var.engine_version
  instance_class                  = var.rds_instance
  allocated_storage               = "20" ## default
  storage_encrypted               = true
  vpc_security_group_ids          = [aws_security_group.rds.id]
  db_subnet_group_name            = aws_db_subnet_group.rds.name
  multi_az                        = var.multi_az
  storage_type                    = "gp2"
  publicly_accessible             = false
  allow_major_version_upgrade     = false
  auto_minor_version_upgrade      = false
  apply_immediately               = true
  maintenance_window              = "sun:17:00-sun:19:00"
  skip_final_snapshot             = true
  copy_tags_to_snapshot           = true
  backup_retention_period         = 7
  backup_window                   = "19:00-21:00"
  final_snapshot_identifier       = "${var.rds_name}-rds-${md5(timestamp())}"
  enabled_cloudwatch_logs_exports = ["postgresql"]
  license_model                   = "postgresql-license"

  // To prevent accident destroy
  delete_automated_backups = false
  deletion_protection      = true

  lifecycle {
    ignore_changes = [final_snapshot_identifier]
  }
}
