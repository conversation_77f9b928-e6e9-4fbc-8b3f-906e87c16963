# Elastic-IP (eip) for EC2
resource "aws_eip" "ec2_eip" {
  domain = "vpc"
}

resource "aws_security_group" "bastion_host" {
  name        = "${var.instance_name}-sg"
  description = "Allow SSH"
  vpc_id      = var.vpc_id

  ingress {
    description = "SSH from VPC"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = var.cidr_blocks
  }
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

# Get latest Ubuntu Linux Focal Fossa 20.04 AMI
data "aws_ami" "ubuntu-linux-2004" {
  most_recent = true
  owners      = ["099720109477"] # Canonical of Ubuntu linux

  filter {
    name   = "name"
    values = ["ubuntu/images/hvm-ssd/ubuntu-focal-20.04-amd64-server-20240426"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }
}

resource "aws_instance" "rds_bastion" {
  ami                         = data.aws_ami.ubuntu-linux-2004.id
  instance_type               = "t2.micro"
  key_name                    = var.key_name
  subnet_id                   = var.vpc_public_subnet_id
  security_groups             = [aws_security_group.bastion_host.id]
  associate_public_ip_address = true
  user_data                   = file("${path.module}/ec2-userdata/userdata.sh")

  root_block_device {
    encrypted = true
  }

  tags = {
    Name = var.instance_name
  }

  lifecycle {
    ignore_changes = [security_groups]
  }
}

resource "aws_eip_association" "bastion_association" {
  instance_id   = aws_instance.rds_bastion.id
  allocation_id = aws_eip.ec2_eip.id
}
