variable "environment" {
  type        = string
  description = "Environment"
  default     = "dev"
}

variable "db_name" {
  type        = string
  description = "The name of the database to create when the DB instance is created"
}

variable "db_username" {
  type        = string
  description = "Username for the master DB user"
}

variable "db_address" {
  type        = string
  description = "The hostname of the RDS instance"
}

variable "db_password" {
  type        = string
  description = "value"
}

variable "bastion_ip" {
  type        = string
  description = "Public IP address assigned to the instance"
}

variable "private_key_pem" {
  type        = string
  description = "Private key data in PEM (RFC 1421) format"
}
