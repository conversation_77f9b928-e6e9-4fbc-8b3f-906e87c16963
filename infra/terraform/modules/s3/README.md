# AWS S3 Bucket Module

This Terraform module creates an S3 bucket with optional website configuration, ACL, ownership controls, and bucket policy.

## Usage

```hcl
module "s3_bucket" {
  source = "../modules/s3_bucket"

  # Specify input variables here, if any
}
```

## Inputs
- `bucket_name`: The name of the S3 bucket
- `acl`: The ACL for the S3 bucket
- `website`: (Optional) Website configuration for the S3 bucket, including:
  - `index_document`: The index document for the website (string)
  - `error_document`: The error document for the website (string)
- `tags`: Tags to associate with the S3 bucket
- `policy`: (Optional) Policy JSON for the bucket

## Outputs
- `bucket_arn`: The ARN of the S3 bucket
- `bucket_domain`: The regional domain name of the S3 bucket
