

resource "aws_cloudwatch_log_group" "trail" {
  name = "/aws/cloudtrail"
}

resource "aws_iam_role" "cloudtrail_role" {
  name = "CloudTrailServiceRole"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = "sts:AssumeRole",
        Effect = "Allow",
        Principal = {
          Service = "cloudtrail.amazonaws.com",
        },
      },
    ],
  })
}

resource "aws_iam_policy" "cloudtrail_policy" {
  name        = "trail-policy-log-group"
  description = "policy for trail to send events to cloudwatch log groups"
  policy      = <<-EOF
    {
      "Version": "2012-10-17",
      "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "logs:CreateLogStream"
            ],
            "Resource": [
              "${aws_cloudwatch_log_group.trail.arn}:*"
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "logs:PutLogEvents"
            ],
            "Resource": [
              "${aws_cloudwatch_log_group.trail.arn}:*"
            ]
        }
      ]
    }
  EOF
}

resource "aws_iam_role_policy_attachment" "cloudtrail_roles_policies" {
  role       = aws_iam_role.cloudtrail_role.name
  policy_arn = aws_iam_policy.cloudtrail_policy.arn
}

resource "aws_cloudtrail" "trails" {
  name                          = var.trail_name
  s3_bucket_name                = aws_s3_bucket.cloudtrail_bucket.id
  s3_key_prefix                 = "trails"
  include_global_service_events = true
  cloud_watch_logs_role_arn     = aws_iam_role.cloudtrail_role.arn
  cloud_watch_logs_group_arn    = "${aws_cloudwatch_log_group.trail.arn}:*"
  depends_on                    = [aws_s3_bucket_policy.cloudtrail_bucket_policy]
}
