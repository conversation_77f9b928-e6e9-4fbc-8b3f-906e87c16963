# AWS CloudFront Distribution Module

This Terraform module creates a CloudFront distribution that serves content from an S3 bucket.

## Usage

```hcl
module "cloudfront_distribution" {
  source = "../modules/cloudfront_distribution"

  # Specify input variables here, if any
}
```

## Inputs
- `bucket_arn`: The ARN of the S3 bucket
- `bucket_domain`: The domain name of the S3 bucket
- `default_ttl`: The default TTL for CloudFront
- `max_ttl`: The maximum TTL for CloudFront
- `min_ttl`: The minimum TTL for CloudFront
- `tags`: Tags to associate with the CloudFront distribution
- `price_class`: The price class for the CloudFront distribution (e.g. PriceClass_All, PriceClass_200, PriceClass_100)
- `restrictions`: Restrictions for the CloudFront distribution, including:
  - `restriction_type`: The type of restriction (string)
  - `locations`: List of locations for geo restrictions

## Outputs
- `domain_name`: The domain name of the CloudFront distribution
- `hosted_zone_id`: The hosted zone ID of the CloudFront distribution

