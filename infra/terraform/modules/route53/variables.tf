variable "zone_id" {
  type        = string
  description = "The ID of the Route53 hosted zone"
}

variable "domain" {
  type        = string
  description = "Domain name for which the certificate should be issued by ACM"
}

variable "records" {
  type = list(object({
    name = string
    type = string
    alias = object({
      name                   = string
      zone_id                = string
      evaluate_target_health = bool
    })
  }))
  description = "List of Route53 records to create"
}
