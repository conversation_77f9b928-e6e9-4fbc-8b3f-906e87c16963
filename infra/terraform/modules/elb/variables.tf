# ACM
variable "domain" {
  type        = string
  description = "Domain name for which the certificate should be issued by ACM"
}

# Route53 record
variable "zone_id" {
  type        = string
  description = "The ID of the hosted zone to contain this record"
}

variable "route53_record_name" {
  type        = string
  description = "The name of the record alias ELB"
}

# ELB
variable "lb_name" {
  type        = string
  description = "Name of the LB"
}

variable "lb_type" {
  type        = string
  description = "Type of load balancer to create. Possible values are application, gateway, or network"
  default     = "application"
}

variable "public_subnet_ids" {
  type        = list(string)
  description = "List of subnet IDs to attach to the LB"
}

variable "security_group_ids" {
  type        = list(string)
  description = "List of security group IDs to assign to the LB"
}

variable "lb_target_group_name" {
  type        = string
  description = "Name of the target group"
}

variable "lb_target_port" {
  type        = number
  description = "Port on which targets receive traffic, unless overridden when registering a specific target"
}

variable "vpc_id" {
  type        = string
  description = "Identifier of the VPC in which to create the target group"
}
