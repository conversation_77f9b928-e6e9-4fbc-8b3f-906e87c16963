# AWS ELB Module

This Terraform module creates an Elastic Load Balancing (ELB) (including ACM) in AWS

## Usage

```hcl
module "alb" {
  source = "../modules/elb"

  # Specify input variables here, if any
}
```

## Inputs
- `domain`: Domain name for which the certificate should be issued by ACM
- `zone_id`: The ID of the hosted zone to contain this record
- `route53_record_name`: The name of the record alias ELB
- `lb_name`: Name of the LB
- `lb_type`: Type of load balancer to create. Possible values are application, gateway, or network
- `public_subnet_ids`: List of subnet IDs to attach to the LB
- `security_group_ids`: List of security group IDs to assign to the LB
- `lb_target_group_name`: Name of the target group
- `lb_target_port`: Port on which targets receive traffic, unless overridden when registering a specific target
- `vpc_id`: Identifier of the VPC in which to create the target group

## Outputs
- `alb_target_group_arn`: ARN of the Target Group
