# ECS cluster
module "ecs_cluster" {
  source = "../../modules/ecs-cluster"

  cluster_name = "${var.project_name}-${var.environment}-${var.ecs_cluster}"
}

# IAM role for ECS task execution
module "assume_iam" {
  source = "../../modules/iam/assume-iam"

  policy_arns   = ["arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"]
  service_name  = "ecs-tasks.amazonaws.com"
  iam_role_name = "${var.project_name}-${var.environment}-ecsTaskExecutionRole"
}

# IAM role for ECS task API
module "api_task_role_iam" {
  source = "../../modules/iam/assume-iam"

  policy_arns   = [module.sm_readonly.policy_arn, module.s3_user_upload_file.policy_arn]
  service_name  = "ecs-tasks.amazonaws.com"
  iam_role_name = "${var.project_name}-${var.environment}-api-ecsTaskRole"
}

# Security Group for API
module "api_fargate_security_group" {
  source = "../../modules/security-group"

  ingress_ports       = [var.api_port]
  ingress_protocol    = "tcp"
  egress_ports        = ["0"]
  egress_protocol     = "-1"
  vpc_id              = module.vpc.vpc_id
  sg_name             = "${var.project_name}-${var.environment}-api-fargate-sg"
  egress_cidr_blocks  = ["0.0.0.0/0"]
  ingress_cidr_blocks = []
  prefix_list_ids     = [module.alb_api_security_group.security_group_id]
}

# ECS service for API
module "api_fargate" {
  source = "../../modules/ecs-fargate"

  aws_region                = var.aws_region
  environment               = var.environment
  alb_group_arn             = module.alb_api.alb_target_group_arn
  container_port            = var.api_port
  log_group_name            = "${var.project_name}-${var.environment}-${var.api_log_group_name}"
  log_stream_name           = "${var.project_name}-${var.environment}-${var.log_stream_name}"
  container_name            = "${var.project_name}-${var.environment}-${var.api_container_name}"
  task_family               = "${var.project_name}-${var.environment}-${var.api_task_family}"
  ecs_service_name          = "${var.project_name}-${var.environment}-${var.api_ecs_service}"
  cpu                       = var.api_cpu
  memory                    = var.api_memory
  vpc_private_subnet_ids    = module.vpc.private_subnet_ids
  desired_count             = var.api_desired_count
  ecr_url                   = module.api_ecr.ecr_url
  cluster_id                = module.ecs_cluster.cluster_id
  execution_role_arn        = module.assume_iam.iam_role_arn
  task_role_arn             = module.api_task_role_iam.iam_role_arn
  fargate_security_group_id = module.api_fargate_security_group.security_group_id
}

# worker worker task
# IAM role for ECS task API
module "worker_task_role_iam" {
  source = "../../modules/iam/assume-iam"

  policy_arns   = [module.sm_readonly.policy_arn]
  service_name  = "ecs-tasks.amazonaws.com"
  iam_role_name = "${var.project_name}-${var.environment}-worker-ecsTaskRole"
}

# Security Group for API
module "worker_fargate_security_group" {
  source = "../../modules/security-group"

  ingress_ports       = []
  ingress_protocol    = "tcp"
  egress_ports        = ["0"]
  egress_protocol     = "-1"
  vpc_id              = module.vpc.vpc_id
  sg_name             = "${var.project_name}-${var.environment}-worker-fargate-sg"
  egress_cidr_blocks  = ["0.0.0.0/0"]
  ingress_cidr_blocks = []
  prefix_list_ids     = []
}

# ECS service for API
module "worker_fargate" {
  source = "../../modules/ecs-fargate"

  aws_region                = var.aws_region
  environment               = var.environment
  alb_group_arn             = ""
  container_port            = 0
  log_group_name            = "${var.project_name}-${var.environment}-${var.worker_log_group_name}"
  log_stream_name           = "${var.project_name}-${var.environment}-${var.log_stream_name}"
  container_name            = "${var.project_name}-${var.environment}-${var.worker_container_name}"
  task_family               = "${var.project_name}-${var.environment}-${var.worker_task_family}"
  ecs_service_name          = "${var.project_name}-${var.environment}-${var.worker_ecs_service}"
  cpu                       = var.worker_cpu
  memory                    = var.worker_memory
  vpc_private_subnet_ids    = module.vpc.private_subnet_ids
  desired_count             = var.worker_desired_count
  ecr_url                   = module.worker_ecr.ecr_url
  cluster_id                = module.ecs_cluster.cluster_id
  execution_role_arn        = module.assume_iam.iam_role_arn
  task_role_arn             = module.worker_task_role_iam.iam_role_arn
  fargate_security_group_id = module.worker_fargate_security_group.security_group_id
}
