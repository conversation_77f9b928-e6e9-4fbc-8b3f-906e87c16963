{"Version": "2012-10-17", "Statement": [{"Action": "sts:AssumeRoleWithWebIdentity", "Effect": "Allow", "Principal": {"Federated": "arn:aws:iam::${aws_account_id}:oidc-provider/token.actions.githubusercontent.com"}, "Condition": {"StringEquals": {"token.actions.githubusercontent.com:aud": "${aws_sts_client_id}"}, "StringLike": {"token.actions.githubusercontent.com:sub": "repo:${github_repository}:*"}}}]}