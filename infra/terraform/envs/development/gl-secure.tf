# ---------------------------------------------
# gl-secure
# ---------------------------------------------
module "gl_secure" {
  source      = "../../modules/gl-secure"
  project     = var.project_name
  environment = var.environment
  waf_info = {
    ip_whitelist               = local.waf_ip_whitelist
    waf_target_arns            = [module.alb_api.aws_lb_arn]
    waf_cloudfront_target_arns = [module.cloudfront_webapp_user.distribution_arn, module.cloudfront_webapp_admin.distribution_arn]
  }

  enable_guardduty = true
}
