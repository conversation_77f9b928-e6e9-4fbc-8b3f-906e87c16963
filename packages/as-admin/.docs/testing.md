# Testing

- Testing is the cornerstone of building robust applications. It's like a quality check, ensuring everything functions as intended and seamlessly interacts. This proactive approach prevents bugs and fosters smooth collaboration between different parts of your application.

## Types of Tests

### Unit Tests

Unit tests are tiny tests that check individual parts of your code, like building blocks. They are fast to write and run, making them ideal for ensuring each piece works as expected. 

[Unit test example](../src/components/forms/input-text/__tests__/InputText.test.tsx)

### Integration Tests

Integration testing refers to testing the interactions between the different components in a application, i.e., how these components work together to create a specific functionality or workflow.

[Integration test](../src/features/todos/ui/components/__tests__/TodoList.test.tsx)

### End-to-End (E2E) Tests

End-to-End (E2E) testing mimics how users interact with your application, automating tests across the entire system – frontend, backend, and integrations. This approach validates if the application functions seamlessly from start to finish, catching issues that might slip through isolated unit tests.

**Note:** While your sample app uses a mocked API server, the test structure replicates real E2E tests that would interact with the live API.

[Integration test](../e2e/tests/todos.spec.ts)

## Recommendations Tool

### [Vitest](https://vitest.dev)

**Note:** Vitest leverages Vite's configuration, eliminating the need for separate setups for testing and building. This simplifies your workflow and reduces boilerplate code.

### [Testing Library](https://testing-library.com/)

### [Playwright](https://playwright.dev)

#### [MSW](https://mswjs.io)

**Note:** API mocking service that let us intercept outgoing requests, observe them, and respond to them using mocked responses.
