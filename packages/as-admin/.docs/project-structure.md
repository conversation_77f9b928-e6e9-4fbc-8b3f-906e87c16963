# AS E-commerce Admin site

## Overview

This project follows Clean Architecture principles combined with a feature-based (slice) structure to organize code. This approach ensures:

- Clear separation of concerns
- High maintainability
- Easy testing
- Scalable codebase
- Independent feature development

## Directory Structure

```sh
src/
├── app/                   # Application routing and layout
├── assets/                # Static assets (images, fonts, etc.)
├── bundles/               # Generated API models and types
├── components/            # Shared UI components
├── config/                # Environment and app configuration
├── constants/             # Global constants and enums
├── features/              # Feature modules (see Feature Structure below)
├── hooks/                 # Shared custom hooks
├── lib/                   # Pre-configured libraries (axios, etc.)
├── providers/             # React context providers
├── stores/                # Global state management
├── types/                 # Global TypeScript types
├── utils/                 # Utility functions
├── main.tsx               # Application entry point
└── index.html             # HTML template
```

## Feature Structure

Each feature follows Clean Architecture with three main layers:

### 1. Domain Layer
Contains business logic and interfaces:
```sh
domain/
├── entities/             # Core business models
│   └── entity.ts
└── repositories/         # Repository interfaces
    ├── repository.interface.ts
```

### 2. Application Layer
Implements use cases:
```sh
application/
├── create.ts            # Create use case
├── update.ts            # Update use case
├── delete.ts            # Delete use case
└── get.ts              # Retrieve use case
```

### 3. Infrastructure Layer
Handles external concerns:
```sh
infrastructure/
├── api/                 # API implementations
│   └── api.ts
├── repositories/        # Repository implementations
│   └── repository.ts
└── stores/             # State management
    └── store.ts
```

### 4. UI Layer
Contains presentation components:
```sh
ui/
├── components/          # Feature-specific components
│   ├── __tests__/      # Component tests
│   └── Component.tsx
└── hooks/              # Feature-specific hooks
    ├── __tests__/      # Hook tests
    └── useHook.ts
```

## Example Feature Structure

```sh
features/
└── auth/
    ├── domain/
    │   ├── entities/
    │   │   └── auth-entity.ts
    │   └── repositories/
    │       └── auth-repository.ts
    ├── application/
    │   └── auth.ts
    ├── infrastructure/
    │   ├── api/
    │   │   └── auth-api.ts
    │   ├── repositories/
    │   │   └── auth-repository.ts
    │   └── stores/
    │       └── auth-store.ts
    └── ui/
        ├── components/
        │   └── LoginForm.tsx
        └── hooks/
            └── useAuth.ts
```

## Development Guidelines

1. **Feature Independence**: Each feature should be self-contained with minimal dependencies on other features.

2. **Clean Architecture Layers**:
   - Domain: Contains business logic and interfaces
   - Application: Implements use cases
   - Infrastructure: Handles external concerns
   - UI: Manages presentation

3. **Testing**:
   - Unit tests for business logic
   - Integration tests for repositories
   - Component tests for UI elements

4. **State Management**:
   - Local state: React useState
   - Feature state: Custom stores
   - Global state: Global stores

5. **API Integration**:
   - Use generated types from bundles/model
   - Implement repository pattern
   - Handle errors consistently
