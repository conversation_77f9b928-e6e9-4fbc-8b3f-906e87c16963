# Components and Style

## Styling solution

There are multiple ways to style a React application. Some good options are:

[TailwindCSS](https://tailwindcss.com/)
[vanilla-extract](https://github.com/seek-oss/vanilla-extract)
[Panda CSS](https://panda-css.com/)
[CSS modules](https://github.com/css-modules/css-modules)
[styled-components](https://styled-components.com/)
[emotion](https://emotion.sh/docs/introduction)

## Headless component

These component libraries provide unstyled components. If you have a specific design system to implement, opting for headless components that come unstyled might be a more efficient and effective solution than adapting a fully-featured component library such as Material UI to fit your needs. Some good options are:

[Radix UI](https://www.radix-ui.com/)
[Headless UI](https://headlessui.com/v1)
[ShadCN](https://ui.shadcn.com/)
[React spectrum](https://react-spectrum.adobe.com/react-aria/)

## Fully featured component libraries

These component libraries offer fully styled components:

[Ant Design](https://ant.design/)
[Charka UI](https://v2.chakra-ui.com/)
[Material UI](https://mui.com/material-ui/)
[React Bootstrap](https://react-bootstrap.netlify.app/)

## Storybook

[Storybook](https://storybook.js.org/) is an excellent tool for developing and testing components in isolation. Think of it as a catalog of all the components your application uses. It is very useful for both developing and discovering components.

[Storybook Story Example Code](../src/components/forms/button/Button.stories.tsx)
