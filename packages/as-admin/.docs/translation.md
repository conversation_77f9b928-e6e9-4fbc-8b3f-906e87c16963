# Internationalization (i18n)

This section empowers you to create a multilingual React application, reaching a global audience. It leverages i18next and the react-i18next library for streamlined translation management.

[i18next](https://www.i18next.com/)
[react-i18next](https://react.i18next.com/)

## Configuration

The template provides an [initial configuration file](src/lib/i18n/config.ts). Explore more advanced configuration options in [the official i18next documentation](https://github.com/i18next/next-i18next)

## Adding Languages

To support additional languages, create translation files (e.g., en/translation.json, fr/translation.json) for each language in src/lib/i18n/locales directory. Each file should be a JSON object with key-value pairs representing the translated strings. And add it into `resources` in config file.

Example Translation File [en/translation.json](../src/lib/i18n/locales):

```sh
{
  "welcome": "Welcome!",
  "how_are_you": "How are you?",
  // ... other translations
}
```
