# Additional resources

## React

[Tao of React](https://alexkondov.com/tao-of-react/)
[React handbook](https://reacthandbook.dev/)
[React pattern](https://reactpatterns.com/)

## Javascript

[JS info](https://javascript.info/)
[You don't know JS](https://github.com/getify/You-Dont-Know-JS)
[JS concept](https://github.com/leonardomso/33-js-concepts#8-iife-modules-and-namespaces)

## Typescript

[Cheat sheet](https://react-typescript-cheatsheet.netlify.app/)
[Naming convention](https://google.github.io/styleguide/tsguide.html)
[Clean code](https://github.com/labs42io/clean-code-typescript)

## Best practices

[Architect web-apps](https://www.patterns.dev/)
[Git commits](https://dev.to/phukon/how-i-write-commits-like-a-pro-340l)
