# Overview

This repository offers a React and TypeScript template for building applications using the Slice architecture. It leverages Vite for a fast build process, React Router for efficient routing, and Tailwind CSS for streamlined component styling.

## [React](https://react.dev/)

## [Vite](https://vitejs.dev/)

## Prerequisites

- Node 18+
- Yarn 1.22+

## Installation and Setup

- SSH:

```bash
<NAME_EMAIL>:genkisystem/template-vite-react.git
cd template-vite-react
yarn install
```

- HTTPS:

```bash
git clone https://github.com/genkisystem/template-vite-react.git
cd template-vite-react
yarn install
```

- Create `env` file and append this content:

```code
VITE_APP_API_URL=https://react-template.app.com
VITE_APP_ENABLE_API_MOCKING=true
```

**Note**:
`VITE_APP_API_URL`: API end point
`VITE_APP_ENABLE_API_MOCKING`: Enable mock server for development/testing or use real API in production.

## Available Scripts

### `yarn install`

- Install all packages that require to run the app.

### `yarn dev`

- Runs the app in the development mode.

### `yarn build`

- Builds the app for production to `dist` folder

### `yarn preview`

- Preview the build of the production in local environment

### `yarn format`

- Format the code following the rule config in `@/src/.prettierrc.cjs`

### `yarn lint`

- Check ESlint of the code following the rule config in `@/src/.eslintrc.cjs`

### `yarn lint:fix`

- Fix ESlint error following the rule config in `@/src/.eslintrc.cjs`

### `yarn test`

- Runs unit tests for your application. These tests verify if individual parts of your code function as expected.

### `yarn test:coverage`

- Runs the tests and also generates a report that shows how much of your code is covered by the tests. High coverage indicates that most parts of your code have been tested.

### `yarn test:e2e`

- Runs end-to-end (e2e) tests in the application. These tests simulate user interactions and verify the overall functionality of the app from the user's perspective

### `yarn test:e2e:ui`

- Same with `yarn test:e2e` but in interactive UI mode, with a built-in watch mode

[All script available](../package.json)

## Project Structure

[Project Structure](./project-structure.md)

## Related Tools

[Translation](./Translation.md)
[Testing](./testing.md)
[State Management](./state-management.md)
[Linter](./linter.md)
[Error handler](./error-handling.md)
