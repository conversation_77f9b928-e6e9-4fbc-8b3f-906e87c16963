# Error Handling

Errors are an inherent aspect of software development. It's recommended to employ specialized Error Boundaries instead of embedding error-handling logic within generic components. However, Error Boundaries should be used alongside, not in lieu of, try/catch statements, both playing crucial roles in effective error management in React.

## In App Errors

- Utilize [react-error-boundary](https://www.npmjs.com/package/react-error-boundary) within the [App Provider](../src/app/AppProvider.tsx) to catch all errors.

- Leverage the error-handling capabilities provided by [react-router](https://reactrouter.com), which offers mechanisms to capture errors thrown during loading, actions, or component rendering. Refer to [this example code](../src/app/routes/index.ts) for integrating error elements from react-router
