# State Management

- Effective state management is crucial for application performance and maintainability. It ensures that your application remains responsive and efficient, even as it grows in complexity. Properly managed state helps organize application logic, making debugging and updates easier. Without it, applications can become slow, difficult to maintain, and prone to bugs.

## Component State

- Component state should be specific to individual components and not shared globally. When needed, pass it down to child components as props. Start by defining state within the component itself, and only elevate it to a higher level if it is required elsewhere in the application. For managing component state, utilize React hooks such as useState and useReducer.

- [useState](https://react.dev/reference/react/useState)
- [useReducer](https://react.dev/reference/react/useReducer)

## Global State

Application state manages global aspects like modals, notifications, and color modes. To ensure optimal performance and ease of maintenance, localize state to the components that need it. Avoid making all state variables global from the start to maintain a structured and efficient state management architecture.

- [zustand](https://github.com/pmndrs/zustand)
- [context](https://react.dev/learn/passing-data-deeply-with-context)
- [redux](https://redux.js.org/) + [redux toolkit](https://redux-toolkit.js.org/) | [redux toolkit](https://redux-saga.js.org/)
- [mobx](https://mobx.js.org)
- [jotai](https://github.com/pmndrs/jotai)
- [recoiljs](https://recoiljs.org/)

**Note**: This template uses `zustand` because it is straightforward to set up and use: <https://docs.pmnd.rs/zustand/getting-started/comparison>

- All the needed types are declared in [here](../src/types/store.ts)
- Below is a sample code snippet:

```code
import { createStore } from 'zustand';
import { devtools } from 'zustand/middleware';
import { shallow } from 'zustand/shallow';
import { useStoreWithEqualityFn } from 'zustand/traditional';

import { env } from '@/config';
import { ExtractState, Params } from '@/types/store';

import { Todo } from '../../domain/entities/todo';

interface TodosStore {
  todos: Todo[];
  actions: {
    updateTodos: (todos: Todo[]) => void;
    resetState: () => void;
  };
}

const todosStore = createStore<TodosStore>()(
  devtools(
    (set) => ({
      todos: [],
      actions: {
        updateTodos: (todos: Todo[]) => {
          set({
            todos,
          });
        },
        resetState: () => {
          set({
            todos: [],
          });
        },
      },
    }),
    {
      name: 'todos',
      enable: !env.PROD,
    },
  ),
);

const useStoresStore = <U>(
  selector: Params<U, typeof todosStore>[1],
  equalityFn?: Params<U, typeof todosStore>[2],
) => {
  return useStoreWithEqualityFn(todosStore, selector, equalityFn);
};

// Selectors
const todosSelector = (state: ExtractState<typeof todosStore>) => state.todos;

// Getters
const getTodos = () => todosSelector(todosStore.getState());

// Hooks
const useStores = () => useStoresStore(todosSelector, shallow);

export {
  // Getters
  getTodos,

  // Hooks
  useStores,
};
```

## Form State

Forms are one of the most popular elements of any application, and effectively managing their state is essential for a seamless user experience. Utilize libraries like `Formik` or `React Hook Form` to streamline this process. These libraries provide built-in validation, error handling, and form submission functionalities, simplifying form state management within your application.

- [formik](https://formik.org/)
- [react-hook-form](https://react-hook-form.com/)
