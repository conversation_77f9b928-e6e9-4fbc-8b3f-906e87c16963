export enum DateFormat {
  fullDateTimeWithHyphen = 'YYYY-MM-DD HH:mm',
  fullDateWithHyphen = 'YYYY-MM-DD',
  timeWithoutSeconds = 'HH:mm',
  fullDateISO8601WithOffset = "YYYY-MM-DD'T'HH:mm:ssxxx",
  fullDateDDMMYYYYWithSlash = 'DD/MM/YYYY',
  fullDateWithSlash = 'YYYY/MM/DD',
  fullDateISO8601UTC = 'YYYY-MM-DDTHH:mm:ssZ',
  fullDateTimeYYYYMMDDWithSlash = 'YYYY/MM/DD HH:mm:ss',
  fullDateYYYYMMDDWithDot = 'YYYY.MM.DD',
  fullDateYYYYMMDDWithDotHHmm = 'YYYY.MM.DD HH:mm',
  fullDateYYYYMMDDWithDotHHmmss = 'YYYY.MM.DD HH:mm:ss',
  fullDateYYYYMMDDHHmmss = 'YYYYMMDDHHmmss',
}
