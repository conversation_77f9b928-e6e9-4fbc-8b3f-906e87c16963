import {
  AccountStatus,
  AccountType,
  ProductCondition,
  ProductTarget,
  ReportStatus,
  SizeTable,
  TaxName,
} from '@/bundles/model/models';
import { Item } from '@/components/select/Select';

const getPeriodOptions = (): Item[] => {
  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 3 }, (_, i) => currentYear + i).reverse();

  return [
    { value: '', key: '選択してください' },
    { value: 'last_30_days', key: '過去30日間' },
    { value: 'last_3_months', key: '過去3ヶ月' },
    ...years.map((year) => ({ value: year.toString(), key: year.toString() })),
  ];
};

const periodOptions = getPeriodOptions();

const productTargetOptions: Item[] = [
  {
    key: 'メンズ',
    value: ProductTarget.Men,
  },
  {
    key: 'レディース',
    value: ProductTarget.Women,
  },
  {
    key: 'ユニセックス',
    value: ProductTarget.Unisex,
  },
  {
    key: 'キッズ',
    value: ProductTarget.Children,
  },
];

const sizeTableOptions: Item[] = [
  {
    key: 'ズボン',
    value: SizeTable.Pants,
  },
  {
    key: '靴',
    value: SizeTable.Shoes,
  },
  {
    key: '帽子',
    value: SizeTable.Hat,
  },
  {
    key: '指輪',
    value: SizeTable.Ring,
  },
  {
    key: 'その他',
    value: SizeTable.General,
  },
];

const productConditionOptions: Item[] = [
  {
    key: '新品',
    value: ProductCondition.Level1,
  },
  {
    key: '未使用品・展示品',
    value: ProductCondition.Level2,
  },
  {
    key: '中古品：A',
    value: ProductCondition.Level3,
  },
  {
    key: '中古品：AB',
    value: ProductCondition.Level4,
  },
  {
    key: '中古品：B',
    value: ProductCondition.Level5,
  },
  {
    key: '中古品：C',
    value: ProductCondition.Level6,
  },
];
const taxNameOptions: Item[] = [
  { key: '消費税', value: TaxName.ConsumptionTax },
  { key: '売上税', value: TaxName.SalesTax },
  { key: 'VAT', value: TaxName.Vat },
  { key: 'GST', value: TaxName.Gst },
];

const accountStatusOptions: Item[] = [
  { key: 'ALL', value: 'ALL' },
  { key: '審査中', value: AccountStatus.Inreview },
  { key: '稼働中', value: AccountStatus.Active },
  { key: '退会済', value: AccountStatus.Viewonly },
  { key: '制限中', value: AccountStatus.Restricted },
  { key: '停止中', value: AccountStatus.Suspended },
];

const accountTypeOptions: Item[] = [
  { key: 'ALL', value: 'ALL' },
  { key: '通常会員', value: AccountType.Standard },
  { key: 'プレミアム', value: AccountType.Premium },
  { key: 'セラー会員', value: AccountType.Seller },
];

const reportStatusOptions: Item[] = [
  { key: 'ALL', value: 'ALL' },
  { key: '未対応', value: ReportStatus.NotHandled },
  { key: '処理中', value: ReportStatus.InProgress },
  { key: '保留', value: ReportStatus.Pending },
  { key: '完了', value: ReportStatus.Completed },
  { key: '完了以外', value: ReportStatus.Uncompleted },
];

const languageOptions: Item[] = [
  { key: '英語', value: 'en' },
  { key: 'スペイン語', value: 'es' },
  { key: 'フランス語', value: 'fr' },
  { key: 'イタリア語', value: 'it' },
  { key: '日本語', value: 'ja' },
  { key: '日本語', value: 'jp' },
  { key: '韓国語', value: 'ko' },
  { key: '简体中文', value: 'zh' },
];

const sizeItems: Item[] = [
  { key: 'FreeSize', value: 'freesize' },
  { key: 'XXS', value: 'xxs' },
  { key: 'XS', value: 'xs' },
  { key: 'S', value: 's' },
  { key: 'M', value: 'm' },
  { key: 'L', value: 'l' },
  { key: 'XL', value: 'xl' },
  { key: 'XXL', value: 'xxl' },
  { key: '0-3m', value: '0-3m' },
  { key: '3-6m', value: '3-6m' },
  { key: '6-9m', value: '6-9m' },
  { key: '9-12m', value: '9-12m' },
  { key: '12-18m', value: '12-18m' },
  { key: '2T', value: '2t' },
  { key: '3T', value: '3t' },
  { key: '4T', value: '4t' },
  { key: '22', value: '22' },
  { key: '24', value: '24' },
  { key: '26', value: '26' },
  { key: '28', value: '28' },
  { key: '30', value: '30' },
  { key: '32', value: '32' },
  { key: '34', value: '34' },
  { key: '36', value: '36' },
  { key: '38', value: '38' },
  { key: '21', value: '21' },
  { key: '21.5', value: '21.5' },
  { key: '22', value: '22' },
  { key: '22.5', value: '22.5' },
  { key: '23', value: '23' },
  { key: '23.5', value: '23.5' },
  { key: '24', value: '24' },
  { key: '24.5', value: '24.5' },
  { key: '25', value: '25' },
  { key: '25.5', value: '25.5' },
  { key: '26', value: '26' },
  { key: '26.5', value: '26.5' },
  { key: '27', value: '27' },
  { key: '27.5', value: '27.5' },
  { key: '28', value: '28' },
  { key: '28.5', value: '28.5' },
  { key: '29', value: '29' },
  { key: '6-5/8', value: '6-5/8' },
  { key: '6-3/4', value: '6-3/4' },
  { key: '6-7/8', value: '6-7/8' },
  { key: '7', value: '7' },
  { key: '7-1/8', value: '7-1/8' },
  { key: '7-1/4', value: '7-1/4' },
  { key: '7-3/8', value: '7-3/8' },
  { key: '7-1/2', value: '7-1/2' },
  { key: '7-3/4', value: '7-3/4' },
  { key: '7-7/8', value: '7-7/8' },
  { key: '8', value: '8' },
  { key: '1', value: '1' },
  { key: '2', value: '2' },
  { key: '3', value: '3' },
  { key: '4', value: '4' },
  { key: '5', value: '5' },
  { key: '6', value: '6' },
  { key: '7', value: '7' },
  { key: '8', value: '8' },
  { key: '9', value: '9' },
  { key: '10', value: '10' },
  { key: '11', value: '11' },
  { key: '12', value: '12' },
  { key: '13', value: '13' },
];

export {
  productTargetOptions,
  sizeTableOptions,
  productConditionOptions,
  sizeItems,
  taxNameOptions,
  accountStatusOptions,
  accountTypeOptions,
  periodOptions,
  reportStatusOptions,
  languageOptions,
};
