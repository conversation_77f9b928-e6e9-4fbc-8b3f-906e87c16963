import { GenericRequest } from '@/utils/types';

export const queryKeys = {
  users: {
    all: ['users'] as const,
    list: (filters: GenericRequest) => ['users', 'list', filters] as const,
    detail: (id: string) => ['users', 'detail', id] as const,
  },
  products: {
    all: ['products'] as const,
    list: (filters: GenericRequest) => ['products', 'list', filters] as const,
    detail: (id: string) => ['products', 'detail', id] as const,
  },
  orders: {
    all: ['orders'] as const,
    list: (filters: GenericRequest) => ['orders', 'list', filters] as const,
    detail: (id: string) => ['orders', 'detail', id] as const,
  },
  categories: {
    all: ['categories'] as const,
    list: (filters: GenericRequest) => ['categories', 'list', filters] as const,
    detail: (id: string) => ['categories', 'detail', id] as const,
  },
  // Thêm các domain khác t<PERSON>ơng tự
} as const;
