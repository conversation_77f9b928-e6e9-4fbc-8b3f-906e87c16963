import dayjs from 'dayjs';

import {
  AccountStatus,
  ProductTarget,
  ProductTransactionStatus,
  ReportItem,
  ReportReporter,
  ReportStatus,
  TaxInfo,
  TaxItemInfo,
} from '@/bundles/model';
import { Item } from '@/components/select/Select';
import { env } from '@/config';
import { getCountryInfo } from '@/utils/country';
import { getImageUrl } from '@/utils/image-utils';

import { DateFormat } from './date';
import { NUMERIC_INPUT_PATTERN } from './regex';

const GRID_TEMPLATE_COLUMNS = '5% 1fr 1fr 1fr 1fr';

const PAGE_LIMIT_DEFAULT = 10;

const dateFormat = (date?: string, format?: DateFormat) => {
  if (!date) return '';
  return dayjs(date).format(format);
};

const getDisplayNameDefault = (displayName?: { [key: string]: string }) => {
  if (!displayName) return '';
  return displayName['en'];
};

const setDisplayNameDefault = (value: string): { [key: string]: string } => {
  return { en: value };
};

const setDisplayNameByCode = (
  value: string,
  code: string,
  displayName?: { [key: string]: string },
): { [key: string]: string } => {
  return { ...displayName, [code]: value };
};

const getDisplayNameByCode = (
  code: string,
  displayName?: { [key: string]: string },
) => {
  if (!code || !displayName) {
    return '';
  }
  return displayName[code];
};

// Function to create a File from a path base64
const createFileFromPath = async (
  fileName: string,
  path: string,
  type: string,
): Promise<File | undefined> => {
  try {
    const response = await fetch(`data:${type};base64,${path}`);
    if (!response.ok) return undefined;

    const blob = await response.blob();
    return new File([blob], fileName, { type: type });
  } catch {
    return undefined;
  }
};

const handleDownloadFile = async (
  fileName: string,
  fileUrl: string,
  type: string,
) => {
  try {
    const response = await fetch(fileUrl);
    const text = await response.text();

    // Add UTF-8 BOM to ensure opening with standard Excel or Notepad
    const BOM = '\uFEFF';
    const blob = new Blob([BOM + text], { type: `${type};charset=utf-8;` });

    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    a.remove();
    window.URL.revokeObjectURL(url);
    return '';
  } catch (err) {
    return `ダウンロードに失敗しました: ${err}`;
  }
};

/**
 * Formats numeric value for price input
 */
const formatNumericInput = (value: string): string => {
  // Only allow numbers and decimal point
  const numericValue = value.replace(NUMERIC_INPUT_PATTERN, '');

  // Prevent multiple decimal points
  const parts = numericValue.split('.');
  return parts.length > 2
    ? parts[0] + '.' + parts.slice(1).join('')
    : numericValue;
};

/**
 * Formats date with fallback
 */
const formatDateWithFallback = (
  date?: string,
  format: DateFormat = DateFormat.fullDateYYYYMMDDWithDotHHmm,
): string => {
  return date ? dateFormat(date, format) : '-';
};

export enum StatusApplicationDate {
  Draft = 'draft',
  Publish = 'publish',
  Unpublish = 'unpublish',
  Delete = 'delete',
}

const getTextByTransactionStatus = (
  transactionStatus?: ProductTransactionStatus,
) => {
  if (!transactionStatus) return '';
  switch (transactionStatus) {
    case ProductTransactionStatus.Unshipped:
      return '未発送';
    case ProductTransactionStatus.UnderReview:
      return '審査中';
    case ProductTransactionStatus.PreparingForShipment:
      return '発送準備中';
    case ProductTransactionStatus.Shipped:
      return '発送済';
    case ProductTransactionStatus.Received:
      return '受取済';
    case ProductTransactionStatus.Completed:
      return '取引完了';
    case ProductTransactionStatus.OnHold:
      return '保留中';
    case ProductTransactionStatus.Canceled:
      return '正常';
    default:
      return '';
  }
};

const getColorByTransactionStatus = (
  transactionStatus?: ProductTransactionStatus,
) => {
  if (!transactionStatus) return '';
  switch (transactionStatus) {
    case ProductTransactionStatus.Unshipped:
      return '#FF7171';
    case ProductTransactionStatus.UnderReview:
      return '#FF8D44';
    case ProductTransactionStatus.PreparingForShipment:
      return '#7096F8';
    case ProductTransactionStatus.Shipped:
    case ProductTransactionStatus.Received:
      return '#51B883';
    case ProductTransactionStatus.Completed:
      return '#000000';
    case ProductTransactionStatus.Canceled:
      return '#FF7171';
    case ProductTransactionStatus.OnHold:
      return '#006a6a';
    default:
      return '';
  }
};

const isToday = (date: string | Date): boolean => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  if (isNaN(dateObj.getTime())) {
    return false;
  }

  const today = new Date();
  const todayDateString = today.toDateString();
  const inputDateString = dateObj.toDateString();

  return todayDateString === inputDateString;
};

const getTaxItemInfo = (taxInfo: TaxItemInfo) => {
  if (!taxInfo) return '';

  if (taxInfo.district) {
    return getDisplayNameDefault(taxInfo.district.displayName);
  }

  if (taxInfo.region) {
    return getDisplayNameDefault(taxInfo.region.displayName);
  }

  if (taxInfo.country) {
    return getDisplayNameDefault(taxInfo.country.displayName);
  }

  return '';
};

const renderCategory = (target?: ProductTarget) => {
  if (!target) return '';
  switch (target) {
    case 'men':
      return 'メンズ';
    case 'women':
      return 'レディース';
    case 'unisex':
      return 'ユニセックス';
    case 'children':
      return 'キッズ';
    default:
      return '';
  }
};

const findItemByValue = (items: Item[], value?: string) => {
  if (!value) return '';
  return items.find((item) => item.value === value)?.key ?? '';
};

const maskCardNumber = (cardNumber?: string): string => {
  if (!cardNumber) return '';
  const cleanNumber = cardNumber.replace(/\D/g, '');
  if (cleanNumber.length < 4) return '';
  const lastFour = cleanNumber.slice(-4);
  return `**** **** **** ${lastFour}`;
};

const getTaxRegionCountry = (taxInfo?: TaxInfo) => {
  if (!taxInfo) return '';
  const countryCode = taxInfo.country?.code ?? '';
  const country = getCountryInfo(countryCode);
  const regions = taxInfo.regions?.map((region) =>
    getDisplayNameDefault(region.displayName),
  );
  if (!regions || !regions.length) {
    return country?.name ?? '';
  }

  return `${regions?.join(', ')}, ${country?.name}`;
};

const getAvatarInitial = (nickname?: string) => {
  if (!nickname) return '';
  return nickname.charAt(0).toUpperCase();
};

// TODO: mapping api country code to display name
// Language mapping from code to display name
const LANGUAGE_MAPPING: Record<string, string> = {
  en: 'English',
  es: 'Español',
  fr: 'Français',
  it: 'Italiano',
  ja: '日本語',
  jp: '日本語', // Alternative code for Japanese
  ko: '한국어',
  zh: '简体中文',
  zhCN: '简体中文',
  'zh-CN': '简体中文',
};

const getLanguageDisplayName = (languageCode?: string) => {
  if (!languageCode) return '';
  return LANGUAGE_MAPPING[languageCode] || languageCode;
};

const mapAccountStatus = (status?: string): AccountStatus | undefined => {
  switch (status) {
    case 'inreview':
      return AccountStatus.Inreview;
    case 'active':
      return AccountStatus.Active;
    case 'restricted':
      return AccountStatus.Restricted;
    case 'suspended':
      return AccountStatus.Suspended;
    // TODO:
    // case 'pending':
    //   return AccountStatus.Pending;
    default:
      return;
  }
};

const getReporterName = (reporter?: ReportReporter) => {
  if (!reporter) return '';

  // Type guard Seller (have shopName)
  if ('shopName' in reporter && reporter.shopName) {
    return reporter.shopName;
  }

  // Type guard User (have nickname)
  if ('nickname' in reporter && reporter.nickname) {
    return reporter.nickname;
  }

  return '';
};

const getReporterAvatar = (reporter?: ReportReporter) => {
  if (!reporter) return '';

  // Type guard Seller (have avatarUrl)
  if ('avatarUrl' in reporter && reporter.avatarUrl) {
    return `${env.CDN_URL}/${reporter.avatarUrl}`;
  }

  // Type guard User - return empty to show initial letter
  if ('nickname' in reporter) {
    return '';
  }

  return '';
};

const getReporterType = (
  reporter?: ReportReporter,
): 'user' | 'seller' | 'unknown' => {
  if (!reporter) return 'unknown';

  // Type guard Seller (have shopName)
  if ('shopName' in reporter) {
    return 'seller';
  }

  // Type guard User (have nickname)
  if ('nickname' in reporter) {
    return 'user';
  }

  return 'unknown';
};

const getReporterUserId = (reporter?: ReportReporter) => {
  if (!reporter) return '';

  // Both User and Seller should have id
  if ('id' in reporter && reporter.id) {
    return reporter.id;
  }

  return '';
};

const getItemName = (item?: ReportItem) => {
  if (!item) return '';

  // Type guard Product (have name is object)
  if ('name' in item && item.name && typeof item.name === 'object') {
    // Get first available language or default to Japanese
    const nameObj = item.name as { [key: string]: string };
    return nameObj.ja || nameObj.en || Object.values(nameObj)[0] || '';
  }

  // Type guard Seller (have shopName)
  if ('shopName' in item && item.shopName) {
    return item.shopName;
  }

  // Type guard User (have nickname)
  if ('nickname' in item && item.nickname) {
    return item.nickname;
  }

  return '';
};

const getItemAvatar = (item?: ReportItem) => {
  if (!item) return '';

  // Type guard Product (have images)
  if ('images' in item && item.images && item.images.length > 0) {
    return getImageUrl(item.images[0]);
  }

  // Type guard Seller (have avatarUrl)
  if ('avatarUrl' in item && item.avatarUrl) {
    return getImageUrl(item.avatarUrl);
  }

  // Type guard User (have nickname)
  if ('nickname' in item && item.nickname) {
    return item.nickname;
  }

  return '';
};

// Helper function to check if item is a Product
const getItemType = (
  item?: ReportItem,
): 'product' | 'seller' | 'user' | 'unknown' => {
  if (!item) return 'unknown';

  // Type guard Product (have name as object and images)
  if ('name' in item && item.name && typeof item.name === 'object') {
    return 'product';
  }

  // Type guard Seller (have shopName and avatarUrl)
  if ('shopName' in item) {
    return 'seller';
  }

  // Type guard User (have nickname)
  if ('nickname' in item) {
    return 'user';
  }

  return 'unknown';
};

const handleReportItem = (item?: ReportItem) => {
  if (!item)
    return {
      itemName: '',
      itemAvatar: '',
      isImageUrl: false,
      itemType: 'unknown',
      itemDetails: { brandName: '', description: '', price: '', about: '' },
    };
  const itemName = getItemName(item);
  const itemAvatar = getItemAvatar(item);
  const isImageUrl = itemAvatar?.startsWith('http');
  const itemType = getItemType(item);
  const itemDetails = getItemDetails(item);
  return {
    itemName,
    itemAvatar,
    isImageUrl,
    itemType,
    itemDetails,
  };
};

const handleReportReporter = (reporter?: ReportReporter) => {
  if (!reporter)
    return {
      reporterName: '',
      reporterAvatar: '',
      reporterType: 'unknown',
    };

  const reporterName = getReporterName(reporter);
  const reporterAvatar = getReporterAvatar(reporter);
  const reporterType = getReporterType(reporter);
  return {
    reporterName,
    reporterAvatar,
    reporterType,
  };
};
const getStatusBgColor = (status?: ReportStatus) => {
  switch (status) {
    case ReportStatus.NotHandled:
      return '#FA0000';
    case ReportStatus.InProgress:
      return '#4285F4';
    case ReportStatus.Pending:
      return '#9E9E9E';
    case ReportStatus.Completed:
      return '#34A853';
    default:
      return '#9E9E9E';
  }
};

const getStatusLabel = (status?: ReportStatus) => {
  switch (status) {
    case ReportStatus.NotHandled:
      return '未対応';
    case ReportStatus.InProgress:
      return '処理中';
    case ReportStatus.Pending:
      return '保留';
    case ReportStatus.Completed:
      return '完了';
    default:
      return '不明';
  }
};

const getItemAccountId = (item?: ReportItem) => {
  if (!item) return '';

  // Type guard User (have nickname)
  if ('accountId' in item && item.accountId) {
    return item.accountId;
  }

  return '';
};

const getItemBrandName = (item?: ReportItem) => {
  if (!item) return '';

  // Type guard Product (have brand)
  if ('brand' in item && item.brand?.displayName) {
    return getDisplayNameDefault(item.brand.displayName);
  }

  return '';
};

const getItemDescription = (item?: ReportItem) => {
  if (!item) return '';

  // Type guard Product (have description)
  if ('description' in item && item.description) {
    return getDisplayNameDefault(item.description);
  }

  return '';
};

const getItemPrice = (item?: ReportItem) => {
  if (!item) return '';

  // Type guard Product (have price)
  if ('price' in item && item.price) {
    return `$ ${item.price.toLocaleString()}`;
  }

  return '';
};

const getItemDetails = (item?: ReportItem) => {
  if (!item) return { brandName: '', description: '', price: '', about: '' };

  const itemType = getItemType(item);

  switch (itemType) {
    case 'user':
      return {
        brandName: '',
        description: '',
        price: '',
        about: getItemAccountId(item),
      };

    case 'seller': {
      // Type guard Seller (have about)
      const about = 'about' in item && item.about ? item.about : {};
      return {
        brandName: '',
        description: '',
        price: '',
        about: getDisplayNameDefault(about),
      };
    }

    case 'product':
      return {
        brandName: getItemBrandName(item),
        description: getItemDescription(item),
        price: getItemPrice(item),
        about: '',
      };

    default:
      return { brandName: '', description: '', price: '', about: '' };
  }
};

export {
  GRID_TEMPLATE_COLUMNS,
  PAGE_LIMIT_DEFAULT,
  dateFormat,
  getDisplayNameDefault,
  getDisplayNameByCode,
  setDisplayNameDefault,
  setDisplayNameByCode,
  createFileFromPath,
  handleDownloadFile,
  formatNumericInput,
  formatDateWithFallback,
  getTextByTransactionStatus,
  getColorByTransactionStatus,
  isToday,
  getTaxItemInfo,
  renderCategory,
  findItemByValue,
  maskCardNumber,
  getTaxRegionCountry,
  getAvatarInitial,
  getLanguageDisplayName,
  mapAccountStatus,
  getReporterName,
  getReporterAvatar,
  getItemName,
  getItemAvatar,
  getItemType,
  getStatusBgColor,
  getStatusLabel,
  getItemDetails,
  getItemAccountId,
  getReporterType,
  getReporterUserId,
  handleReportItem,
  handleReportReporter,
};
