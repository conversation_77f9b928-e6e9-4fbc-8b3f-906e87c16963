import {
  Box,
  Typography,
  Select,
  MenuItem,
  FormControl,
  Divider,
} from '@mui/material';
import type { ICountry } from 'country-state-city';
import { forwardRef } from 'react';

import { InputTextField } from '@/components/input-text/InputTextField';
import {
  getCountryInfoWithPhone,
  getAllowedCountriesWithPhone,
} from '@/utils/country';

interface PhoneInputProps {
  label?: string;
  countryCode?: string;
  onClear?: () => void;
  onCountryChange?: (countryCode: string) => void;
  value?: string;
  onChange: (value?: string) => void;
}

// Get filtered countries with phone codes
const filteredCountries = getAllowedCountriesWithPhone();

export const PhoneInput = forwardRef<HTMLInputElement, PhoneInputProps>(
  (
    {
      label,
      countryCode = 'JP',
      onClear,
      onCountryChange,
      value,
      onChange,
      ...props
    },
    ref,
  ) => {
    const handleCountryChange = (newCountryCode: string) => {
      onCountryChange?.(newCountryCode);
      // Clear the phone number when country changes
      onChange(undefined);
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      // Only allow numbers
      const numericValue = e.target.value.replace(/[^0-9]/g, '');
      onChange(numericValue);
    };

    const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
      // Prevent non-numeric characters except control keys
      if (
        !/[0-9]/.test(e.key) &&
        ![
          'Backspace',
          'Delete',
          'Tab',
          'Escape',
          'Enter',
          'ArrowLeft',
          'ArrowRight',
        ].includes(e.key)
      ) {
        e.preventDefault();
      }
    };

    const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
      // Only allow pasting numeric content
      const pasteData = e.clipboardData.getData('text');
      if (!/^[0-9]*$/.test(pasteData)) {
        e.preventDefault();
      }
    };

    const handleClear = () => {
      onChange(undefined);
      onClear?.();
    };

    // Create country selector for startAdornment
    const countrySelector = (
      <Box
        sx={{ display: 'flex', alignItems: 'center', gap: 1, mr: 1 }}
        className="flex items-center"
      >
        {/* Country Selector */}
        <FormControl size="small" sx={{ minWidth: 75 }}>
          <Select
            value={countryCode}
            onChange={(e) => handleCountryChange(e.target.value)}
            variant="standard"
            disableUnderline
            sx={{
              '& .MuiSelect-select': {
                padding: 0,
                display: 'flex',
                alignItems: 'center',
                gap: 0.5,
                border: 'none',
                '&:focus': {
                  backgroundColor: 'transparent',
                },
                width: '80px',
              },
            }}
            renderValue={(selected) => {
              const selectedCountry = getCountryInfoWithPhone(selected);
              return (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <span style={{ fontSize: '35px' }}>
                    {selectedCountry.flag}
                  </span>
                  <Typography variant="body2" sx={{ minWidth: '35px' }}>
                    {selectedCountry.phoneCode}
                  </Typography>
                </Box>
              );
            }}
          >
            {filteredCountries.map((country: ICountry) => {
              const countryInfo = getCountryInfoWithPhone(country.isoCode);

              return (
                <MenuItem key={country.isoCode} value={country.phonecode}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <span style={{ fontSize: '35px' }}>{countryInfo.flag}</span>
                    <Typography variant="body2">
                      {countryInfo.name} ({countryInfo.phoneCode})
                    </Typography>
                  </Box>
                </MenuItem>
              );
            })}
          </Select>
        </FormControl>

        <Box className="flex !h-6 !pl-1">
          <Divider orientation="vertical" flexItem />
        </Box>
      </Box>
    );

    return (
      <Box sx={{ paddingLeft: '8px' }}>
        <InputTextField
          ref={ref}
          label={label}
          value={value || ''}
          onChange={handleInputChange}
          onKeyPress={handleKeyPress}
          onPaste={handlePaste}
          startIcon={countrySelector}
          hasValue={!!value}
          onClear={handleClear}
          type="tel"
          className="!w-full"
          inputMode="numeric"
          {...props}
        />
      </Box>
    );
  },
);

PhoneInput.displayName = 'PhoneInput';
