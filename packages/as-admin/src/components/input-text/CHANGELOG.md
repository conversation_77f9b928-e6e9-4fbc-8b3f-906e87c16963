# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added

- Add `jaJP` locale in `locale.ts`
- Add field `subLabelPassword` for `FieldWrapper`, `InputText`
- Add `InputDateRange` component
- Add `InputNumber` component
- Add `InputZipCode` component
- Add `InputTime` for handle time value, `FieldWrapper` common component for display label
- Add InputText component
- Add InputDate component
- Add field `placeholder` for InputDate

### Changed

- Update min date for `InputDate`
- Change the language for `InputDate`, `InputDateRange` component
- Changed `margin` of `InputLabelText`
- Changed `props` and `text-ellipsis` in `InputLabelText`
- Changed `props` in `InputDateRange`
- Changed label accept react node
- Remove `InputZipCode` component
- Apply `FieldWrapper` for `InputText`, `InputDate`
- Changed `InputDate`, `InputLabelText`

### Fixed

- Fixed missing import of InputDate
- Fixed render input props
