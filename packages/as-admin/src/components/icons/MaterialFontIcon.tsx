import ArticleIcon from '@mui/icons-material/Article';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import GroupIcon from '@mui/icons-material/Group';
import HomeIcon from '@mui/icons-material/Home';
import MarkUnreadChatAltIcon from '@mui/icons-material/MarkUnreadChatAlt';
import SettingsIcon from '@mui/icons-material/Settings';
import StorageIcon from '@mui/icons-material/Storage';
import React from 'react';

interface MaterialFontIconProps {
  icon: string;
  fontSize?: 'small' | 'medium' | 'large' | 'inherit';
  color?: string;
}

const iconMap: Record<string, React.ComponentType<any>> = {
  Storage: StorageIcon,
  StorageIcon: StorageIcon,
  Home: HomeIcon,
  HomeIcon: HomeIcon,
  ArticleIcon: ArticleIcon,
  MarkUnreadChatAltIcon: MarkUnreadChatAltIcon,
  GroupIcon: GroupIcon,
  AttachMoneyIcon: AttachMoneyIcon,
  SettingsIcon: SettingsIcon,
  // Add other icons here
};

const circleColor = '#BA1A1A';

const MaterialFontIcon: React.FC<MaterialFontIconProps> = ({
  icon,
  fontSize = 'medium',
  color,
}) => {
  const IconComponent = iconMap[icon];

  if (!IconComponent) {
    console.warn(`Icon not found in map: ${icon}`);
    return null;
  }

  let customSx = {};

  if (icon === 'MarkUnreadChatAltIcon') {
    customSx = {
      '& circle': {
        fill: circleColor,
      },
    };
  }

  return <IconComponent fontSize={fontSize} style={{ color }} sx={customSx} />;
};

export default MaterialFontIcon;
