import SvgIcon from '@mui/material/SvgIcon';

type Props = {
  /**
   * The viewBox is an attribute of the SVG element in HTML.
   */
  viewBox?: string;
  /**
   * height of SVG.
   */
  height?: string;
  /**
   * width of SVG.
   */
  width?: string;
  /**
   * A set of commands which define the path.
   */
  d: string;
};

export const Icon: React.FC<Props> = ({ viewBox, height, width, d }: Props) => {
  return (
    <SvgIcon>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        height={height}
        viewBox={viewBox}
        width={width}
      >
        <path strokeLinecap="round" strokeLinejoin="round" d={d} />
      </svg>
    </SvgIcon>
  );
};
