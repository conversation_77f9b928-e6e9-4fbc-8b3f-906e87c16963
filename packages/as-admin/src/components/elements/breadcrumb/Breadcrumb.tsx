import HomeOutlinedIcon from '@mui/icons-material/HomeOutlined';
import { Box, Typography } from '@mui/material';
import { Link as RouterLink, useLocation } from 'react-router-dom';

import { useLocalStorage } from '@/hooks/use-local-storage';

interface BreadcrumbItem {
  name: string;
  href?: string;
}

interface BreadcrumbProps {
  title: string;
  items: BreadcrumbItem[];
}

export const Breadcrumb: React.FC<BreadcrumbProps> = ({ title, items }) => {
  const { pathname } = useLocation();
  const { setValue: updateActiveItem } = useLocalStorage(
    'menu_active_route',
    pathname,
  );

  return (
    <Box className="px-6 py-4">
      <Box className="flex items-center">
        <Typography variant="h1" className="!text-primary">
          {title}
        </Typography>
        <Box className="ml-auto flex text-sm items-center">
          <HomeOutlinedIcon className="!w-4 !h-4 !fill-onSurfaceVariant" />
          <Box className="!text-onSurface ml-1 flex items-center">
            {items.map((item, index) => {
              if (index === items.length - 1) {
                return (
                  <Typography
                    component="span"
                    key={index}
                    className="!text-onSurface"
                    variant="h5"
                  >
                    {item.name}
                  </Typography>
                );
              }
              return (
                <Box key={index} className="flex items-center">
                  {item.href ? (
                    <RouterLink
                      to={item.href}
                      className="!text-onSurface no-underline hover:no-underline flex text-decoration-none"
                      onClick={() => updateActiveItem(item.href!)}
                    >
                      <Typography
                        component="span"
                        key={index}
                        className="!text-onSurface"
                        variant="subtitle2"
                      >
                        {item.name}
                      </Typography>
                    </RouterLink>
                  ) : (
                    item.name
                  )}
                  {item.href !== undefined && (
                    <Typography component="span" className="px-1">
                      /
                    </Typography>
                  )}
                </Box>
              );
            })}
          </Box>
        </Box>
      </Box>
    </Box>
  );
};
