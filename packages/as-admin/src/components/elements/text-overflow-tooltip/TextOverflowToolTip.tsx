import { Typography, Tooltip, TooltipProps } from '@mui/material';
import React, { useRef, useEffect, useState } from 'react';

type TextOverflowToolTipProps = {
  value: string;
} & Pick<TooltipProps, 'placement'>;

export const TextOverflowToolTip: React.FC<TextOverflowToolTipProps> = ({
  value,
}) => {
  const textElementRef = useRef<HTMLDivElement>(null);
  const [hover, setHover] = useState(false);

  const compareSize = () => {
    if (textElementRef.current) {
      const compare =
        textElementRef.current.scrollWidth > textElementRef.current.clientWidth;

      setHover(compare);
    }
  };

  useEffect(() => {
    compareSize();
    window.addEventListener('resize', compareSize);
    return () => {
      window.removeEventListener('resize', compareSize);
    };
  }, []);

  return (
    <Tooltip title={hover ? value : ''} disableHoverListener={!hover} arrow>
      <Typography
        variant="body2"
        ref={textElementRef}
        style={{
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        }}
      >
        {value}
      </Typography>
    </Tooltip>
  );
};
