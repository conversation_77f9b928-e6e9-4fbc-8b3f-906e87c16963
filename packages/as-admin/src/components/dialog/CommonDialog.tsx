import CheckCircleRoundedIcon from '@mui/icons-material/CheckCircleRounded';
import WarningIcon from '@mui/icons-material/Warning';
import {
  Dialog,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
} from '@mui/material';
import React from 'react';

export type CommonDialogType = 'alert' | 'success' | 'error' | 'confirm-delete';

export interface DialogState {
  open: boolean;
  type: CommonDialogType;
  message: string;
  option?: boolean;
  action?: any;
}

interface CommonDialogProps {
  open: boolean;
  type?: CommonDialogType;
  message: string;
  okText: string;
  cancelText: string;
  okColor?: string;
  onOk: () => void;
  onCancel?: () => void;
}

export const CommonDialog: React.FC<CommonDialogProps> = ({
  open,
  type,
  message,
  okText,
  cancelText,
  okColor,
  onOk,
  onCancel,
}) => {
  return (
    <Dialog
      open={open}
      onClose={onCancel}
      PaperProps={{
        sx: {
          borderRadius: 4,
          minWidth: 340,
          maxWidth: 400,
          p: 0,
        },
      }}
    >
      <DialogContent
        sx={{
          p: 0,
          pt: 4,
          pb: 2,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Box sx={{ mb: 2 }}>
          {type === 'success' ? (
            <CheckCircleRoundedIcon
              sx={{ fontSize: 56, color: 'success.main' }}
            />
          ) : (
            <WarningIcon sx={{ fontSize: 56, color: 'warning.main' }} />
          )}
        </Box>
        <Typography
          variant="h2"
          sx={{
            textAlign: 'center',
            wordBreak: 'break-word',
            padding: '0 16px',
          }}
        >
          {message}
        </Typography>
      </DialogContent>
      <DialogActions
        sx={{
          p: 0,
          borderTop: '1px solid #EBEBEB',
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'center',
        }}
      >
        {type === 'alert' || type === 'confirm-delete' ? (
          <>
            <Button
              onClick={onCancel}
              sx={{
                flex: 1,
                py: 2,
                borderRadius: 0,
                color: 'unset',
                borderRight: '1px solid #EBEBEB',
              }}
            >
              <Typography variant="body1">{cancelText}</Typography>
            </Button>
            <Button
              onClick={onOk}
              sx={{
                flex: 1,
                py: 2,
                borderRadius: 0,
                color: '#151D1D !important',
              }}
            >
              <Typography
                variant="h4"
                color={type === 'confirm-delete' ? 'red' : 'unset'}
                className={`${okColor}`}
              >
                {okText}
              </Typography>
            </Button>
          </>
        ) : (
          <Button
            onClick={onOk}
            sx={{
              flex: 1,
              py: 2,
              borderRadius: 0,
            }}
          >
            <Typography variant="h4" className={`${okColor}`}>
              {okText}
            </Typography>
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};
