import { Button, Grid, Typography } from '@mui/material';
import React from 'react';
import { useTranslation } from 'react-i18next';

import { useDisclosure } from '@/hooks/use-disclosure';

import { Dialog, DialogType } from './Dialog';

export type ConfirmDeleteDialogProps = {
  triggerButton: React.ReactElement;
  onSubmit: () => Promise<void>;
  type?: DialogType;
  title?: string;
  content?: string;
};

export const ConfirmDeleteDialog = ({
  onSubmit,
  triggerButton,
  type = 'delete',
  title,
  content,
}: ConfirmDeleteDialogProps) => {
  const { close, open, isOpen } = useDisclosure();
  const { t } = useTranslation();

  const onSubmitDelete = async () => {
    await onSubmit();
    close();
  };

  return (
    <>
      {React.cloneElement(triggerButton, { onClick: open })}
      <Dialog
        title={title ?? t('misc.confirmDelete.title')}
        open={isOpen}
        type={type}
        onClose={close}
        footer={
          <Grid item justifyContent="flex-end" container spacing={2}>
            <Grid item>
              <Button variant="outlined" size="large" onClick={close}>
                {t('common.button.cancel')}
              </Button>
            </Grid>
            <Grid item>
              <Button
                variant="contained"
                size="large"
                onClick={onSubmitDelete}
                aria-label="Submit delete"
                color={type === 'delete' ? 'error' : 'primary'}
              >
                {t('common.button.confirm')}
              </Button>
            </Grid>
          </Grid>
        }
      >
        <Grid
          container
          sx={{ padding: '16px 32px', whiteSpace: 'pre-line' }}
          direction="column"
          justifyContent="center"
          alignItems="flex-start"
        >
          <Typography>{content ?? t('misc.confirmDelete.content')}</Typography>
        </Grid>
      </Dialog>
    </>
  );
};
