import CloseIcon from '@mui/icons-material/Close';
import {
  Dialog as MuiDialog,
  DialogTitle,
  DialogActions,
  DialogContent,
  DialogProps,
  Divider,
  Grid,
  IconButton,
  Typography,
  Theme,
  useTheme,
} from '@mui/material';
import useMediaQuery from '@mui/material/useMediaQuery';
import { makeStyles, createStyles } from '@mui/styles';
import clsx from 'clsx';
import React from 'react';

const useStyles = makeStyles((theme: Theme) =>
  createStyles({
    root: {
      margin: 'auto',
      [theme.breakpoints.up('lg')]: {
        maxWidth: '960px',
      },
      [theme.breakpoints.up('xl')]: {
        maxWidth: '1280px',
      },
      [theme.breakpoints.down('md')]: {
        maxWidth: '1280px',
      },
      '& .MuiDialog-paper': {
        borderTopLeftRadius: 0,
        borderTopRightRadius: 0,
      },
    },
    header: {
      background: theme.palette.primary.main,
      color: '#FFFFFF',
      position: 'relative',
      '&& svg': {
        color: '#FFFFFF',
      },
      '&__title': {
        flex: 1,
      },
    },
    headerDelete: {
      background: theme.palette.error.main,
    },
    children: {
      padding: 0,
    },
  }),
);

export type DialogType = 'normal' | 'delete';

type Props = {
  /**
   * Label of dialog.
   */
  title: string;
  /**
   * Content element of dialog.
   */
  children: React.ReactNode;
  /**
   * Content element of dialog.
   */
  footer: React.ReactNode;
  /**
   * Callback fired when close the dialog.
   */
  onClose: () => void;
  /**
   * Type of dialog
   */
  type?: DialogType;
  /**
   * Set the class attribute for an element.
   */
  className?: string;
};

type CustomDialogProps = Props & DialogProps;

export const Dialog: React.FC<CustomDialogProps> = ({
  title,
  children,
  footer,
  type,
  className: classNameProp,
  onClose,
  ...restProps
}: CustomDialogProps) => {
  const classes = useStyles();
  const theme = useTheme();
  const isMatchesMd = useMediaQuery(theme.breakpoints.down('md'));

  return (
    <MuiDialog
      {...restProps}
      className={classes.root}
      maxWidth="xl"
      fullScreen={isMatchesMd}
      onClose={onClose}
    >
      <DialogTitle
        className={clsx(classes.header, classNameProp, {
          [classes.headerDelete]: type === 'delete',
        })}
      >
        <Grid
          item
          container
          justifyContent="center"
          alignItems="center"
          style={{
            height: theme.spacing(5),
          }}
        >
          <Typography
            align="center"
            className={clsx(`${classes.header}__title ${classNameProp}__title`)}
            variant="h2"
          >
            {title}
          </Typography>
          <Grid item alignSelf="end">
            <IconButton
              onClick={onClose}
              disabled={restProps.disableEscapeKeyDown}
              aria-label="Close dialog"
            >
              <CloseIcon />
            </IconButton>
          </Grid>
        </Grid>
      </DialogTitle>
      <DialogContent className={classes.children}>{children}</DialogContent>
      <Divider />
      <DialogActions>{footer}</DialogActions>
    </MuiDialog>
  );
};
