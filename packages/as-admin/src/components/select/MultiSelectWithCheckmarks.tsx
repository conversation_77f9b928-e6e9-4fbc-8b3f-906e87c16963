import {
  Checkbox,
  FormControl,
  FormHelperText,
  Select as MuiSelect,
  SelectProps as MuiSelectProps,
  MenuItem,
  ListItemIcon,
  ListItemText,
  SelectChangeEvent,
} from '@mui/material';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { FieldWrapper, FieldWrapperCommonProps } from '../input-text';

import { Item } from './Select';

type Props = {
  /**
   * Data input.
   */
  options: Item[];
  /**
   * Helper text.
   */
  helperText?: string;
  /**
   * Size of select.
   */
  size?: 'small' | 'medium';
  /**
   * Flag to display all select
   */
  hasAll?: boolean;
  /**
   * Custom onChange.
   */
  onChange: (data: string[]) => void;
  /**
   * Placeholder label.
   */
  placeholder?: string;
};

type MultiSelectWithCheckmarksProps = Props &
  Omit<FieldWrapperCommonProps, 'id'> &
  Omit<MuiSelectProps<string[]>, 'onChange'>;

const CHECK_ALL_KEY = 'check-all';

const MultiSelectWithCheckmarks: React.FC<MultiSelectWithCheckmarksProps> = ({
  labelPosition,
  label,
  options,
  helperText,
  size,
  hasAll,
  onChange,
  ...restProps
}: MultiSelectWithCheckmarksProps) => {
  const { t } = useTranslation();

  const selectedData = restProps.value ? restProps.value : [];
  const optionsMap = useMemo(() => {
    return options.reduce(
      (acc, item) => {
        acc[item.value] = item.key;
        return acc;
      },
      {} as Record<string, string>,
    );
  }, [options]);

  const handleChange = (event: SelectChangeEvent<string[]>) => {
    const selected = event.target.value as string[];
    if (selected.includes(CHECK_ALL_KEY)) {
      const isUncheck = selectedData.length === options.length;
      onChange(isUncheck ? [] : options.map((entry) => entry.value));
    } else {
      onChange(selected);
    }
  };

  return (
    <FieldWrapper
      id={`${restProps.id}-select-with-checkmarks`}
      label={label}
      labelPosition={labelPosition}
      required={restProps.required}
    >
      <FormControl fullWidth={restProps.fullWidth} size={size}>
        <MuiSelect
          {...restProps}
          variant="outlined"
          inputProps={{
            ...restProps.inputProps,
            id: `${restProps.id}-select-with-checkmarks`,
          }}
          multiple
          renderValue={(selected) => {
            const { placeholder, displayEmpty } = restProps;
            if (selected.length === 0 && placeholder && displayEmpty) {
              return <span style={{ color: '#A9A9A9' }}>{placeholder}</span>;
            }

            return selected.map((key) => optionsMap[key]).join(', ');
          }}
          onChange={handleChange}
          value={selectedData}
        >
          {hasAll && (
            <MenuItem value={CHECK_ALL_KEY}>
              <ListItemIcon>
                <Checkbox
                  checked={
                    selectedData.length === options.length && options.length > 0
                  }
                  indeterminate={
                    selectedData.length !== 0 &&
                    options.length > selectedData.length
                  }
                  id={'checkbox-all'}
                />
              </ListItemIcon>
              <ListItemText primary={t('common.all')} />
            </MenuItem>
          )}
          {options.map((item) => (
            <MenuItem
              key={item.value}
              value={item.value}
              disabled={restProps.disabled}
            >
              <ListItemIcon>
                <Checkbox
                  checked={selectedData.includes(item.value)}
                  id={`checkbox-${item.key}-${item.value}`}
                />
              </ListItemIcon>
              <ListItemText primary={item.key} />
            </MenuItem>
          ))}
        </MuiSelect>
        {helperText && (
          <FormHelperText
            error={restProps.error}
            style={{ marginLeft: 'unset' }}
          >
            {helperText}
          </FormHelperText>
        )}
      </FormControl>
    </FieldWrapper>
  );
};

MultiSelectWithCheckmarks.defaultProps = {
  labelPosition: 'top',
  size: 'small',
};

export { MultiSelectWithCheckmarks };
