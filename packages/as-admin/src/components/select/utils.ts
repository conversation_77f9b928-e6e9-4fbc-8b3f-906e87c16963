import { HierarchicalItem, FlattenedItem } from './types';

// Flatten hierarchical items for rendering (shows all items)
export const flattenItems = (
  items: HierarchicalItem[],
  level: number = 0,
): FlattenedItem[] => {
  const result: FlattenedItem[] = [];

  items.forEach((item) => {
    const flatItem: FlattenedItem = {
      ...item,
      level,
      visible: true,
    };
    result.push(flatItem);

    // Always add children (no expand/collapse logic)
    if (item.children && item.children.length > 0) {
      const childItems = flattenItems(item.children, level + 1);
      result.push(...childItems);
    }
  });

  return result;
};

// Find item by value in hierarchical structure
export const findItemByValue = (
  items: HierarchicalItem[],
  value: string,
): HierarchicalItem | undefined => {
  for (const item of items) {
    if (item.value === value) {
      return item;
    }
    if (item.children) {
      const found = findItemByValue(item.children, value);
      if (found) return found;
    }
  }
  return undefined;
};

// Get display path for an item (breadcrumb style)
export const getItemPath = (
  items: HierarchicalItem[],
  value: string,
  path: string[] = [],
): string[] => {
  for (const item of items) {
    const currentPath = [...path, item.label];

    if (item.value === value) {
      return currentPath;
    }

    if (item.children) {
      const result = getItemPath(item.children, value, currentPath);
      if (result.length > 0) {
        return result;
      }
    }
  }
  return [];
};
