import CloseIcon from '@mui/icons-material/Close';
import {
  FormControl,
  FormHelperText,
  IconButton,
  Select as MuiSelect,
  SelectProps as MuiSelectProps,
  SelectChangeEvent,
  MenuItem,
  InputAdornment,
  Typography,
  InputLabel,
  CircularProgress,
  Box,
} from '@mui/material';
import { createStyles, makeStyles } from '@mui/styles';
import { useMemo, useState, useCallback } from 'react';

import { FieldWrapper, FieldWrapperCommonProps } from '../input-text';

const useStyles = makeStyles(() =>
  createStyles({
    endAdornment: {
      width: '16px',
      height: '16px',
      marginRight: '16px',
      cursor: 'pointer',
      display: 'none',
    },

    close: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    },

    loadingContainer: {
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      padding: '8px',
    },
  }),
);

type Item = {
  key: string;
  value: string;
};

type InfiniteScrollOptions = {
  items: Item[];
  hasMore: boolean;
  isLoading: boolean;
  page: number;
};

type Props = {
  options: InfiniteScrollOptions;
  helperText?: string;
  size?: 'small' | 'medium';
  placeholder?: string;
  onClear?: () => void;
  customLabel?: string;
  onLoadMore: () => void;
  threshold?: number;
  // loadingText?: string;
  // noMoreDataText?: string;
};

type InfiniteScrollSelectProps = Props &
  Omit<FieldWrapperCommonProps, 'id'> &
  MuiSelectProps;

const defaultRenderValue = (
  selectedOptions: unknown,
  optionsMap: Record<string, string>,
) => {
  if (Array.isArray(selectedOptions)) {
    return selectedOptions.map((key) => optionsMap[key]).join(', ');
  }

  return optionsMap[selectedOptions as string];
};

const InfiniteScrollSelect: React.FC<InfiniteScrollSelectProps> = ({
  labelPosition = 'top',
  label,
  options,
  helperText,
  size = 'small',
  placeholder,
  renderValue: renderValueProp,
  defaultValue: defaultValueProp,
  displayEmpty: displayEmptyProp,
  endAdornment,
  onClear,
  customLabel,
  onLoadMore,
  // loadingText = 'Loading...',
  // noMoreDataText = 'No more data',
  ...restProps
}: InfiniteScrollSelectProps) => {
  const { value: selectedValue, ...otherRestProps } = restProps;
  const classes = useStyles();
  const [hover, setHover] = useState<boolean>(false);

  const isControlled = typeof selectedValue !== 'undefined';
  const value = isControlled ? selectedValue : defaultValueProp ?? '';

  const isEmptyValue = useMemo(() => {
    return (
      !selectedValue || (Array.isArray(selectedValue) && !selectedValue.length)
    );
  }, [selectedValue]);

  const optionsMap = useMemo(() => {
    return options.items.reduce(
      (acc, item) => {
        acc[item.value] = item.key;
        return acc;
      },
      {} as Record<string, string>,
    );
  }, [options.items]);

  const renderValueOptions: (
    selectedOptions: unknown,
    optionsMap: Record<string, string>,
  ) => React.ReactNode = useMemo(() => {
    return renderValueProp ? renderValueProp : defaultRenderValue;
  }, [renderValueProp]);

  const handleScroll = useCallback(
    (event: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, scrollHeight, clientHeight } = event.currentTarget;
      const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;

      if (scrollPercentage > 0.8 && options.hasMore && !options.isLoading) {
        onLoadMore();
      }
    },
    [options.hasMore, options.isLoading, onLoadMore],
  );

  const handleOpen = useCallback(() => {
    // Handle open if needed
  }, []);

  const handleClose = useCallback(() => {
    setHover(false);
    setTimeout(() => {
      if (document.activeElement instanceof HTMLElement) {
        document.activeElement.blur();
      }
    }, 0);
  }, []);

  const renderEndAdornment = () => {
    if (!onClear || !hover) {
      return endAdornment ?? undefined;
    }
    return (
      <InputAdornment
        position="end"
        className={`${classes.endAdornment} ${value ? classes.close : ''}`}
      >
        <IconButton
          focusRipple
          onClick={onClear}
          sx={{
            fontSize: '8px',
            width: '1.5rem',
            height: '1.5rem',
          }}
        >
          <CloseIcon />
        </IconButton>
        {endAdornment}
      </InputAdornment>
    );
  };

  const renderLoadingItem = () => (
    <Box className={classes.loadingContainer}>
      <CircularProgress size={20} />
      {/* <Typography variant="body2" sx={{ ml: 1 }}>
        {loadingText}
      </Typography> */}
    </Box>
  );

  // const renderNoMoreDataItem = () => (
  //   <Box className={classes.loadingContainer}>
  //     <Typography variant="body2" color="text.secondary">
  //       {noMoreDataText}
  //     </Typography>
  //   </Box>
  // );

  return (
    <FieldWrapper
      id={`${restProps.id}-infinite-scroll-select`}
      labelPosition={labelPosition}
      required={restProps.required}
    >
      <FormControl fullWidth={restProps.fullWidth} size={size}>
        {label && <InputLabel>{label}</InputLabel>}
        <MuiSelect
          {...otherRestProps}
          label={label}
          value={value}
          displayEmpty={!!placeholder || displayEmptyProp}
          defaultValue={defaultValueProp}
          variant="outlined"
          inputProps={{
            ...restProps.inputProps,
            id: `${restProps.id}-infinite-scroll-select`,
          }}
          endAdornment={renderEndAdornment()}
          renderValue={(selected) => {
            if (placeholder && isEmptyValue) {
              return <span style={{ color: '#A9A9A9' }}>{placeholder}</span>;
            }

            const value = renderValueOptions(selected, optionsMap);
            return value;
          }}
          startAdornment={
            customLabel && (
              <InputAdornment position="start">
                <Typography variant="body2" style={{ fontWeight: 'bold' }}>
                  {customLabel}:
                </Typography>
              </InputAdornment>
            )
          }
          onMouseEnter={() => setHover(true)}
          onMouseLeave={() => setHover(false)}
          onOpen={handleOpen}
          onClose={handleClose}
          MenuProps={{
            PaperProps: {
              style: {
                maxHeight: 300,
              },
              onScroll: handleScroll,
            },
          }}
        >
          {options.items.map((item, index) => (
            <MenuItem
              key={`item-value-index-${index}`}
              value={item.value}
              disabled={restProps.disabled}
            >
              {item.key}
            </MenuItem>
          ))}

          {options.isLoading && renderLoadingItem()}

          {/* {!options.hasMore &&
            options.items.length > 0 &&
            renderNoMoreDataItem()} */}
        </MuiSelect>
        {helperText && (
          <FormHelperText
            error={restProps.error}
            style={{ marginLeft: 'unset' }}
          >
            {helperText}
          </FormHelperText>
        )}
      </FormControl>
    </FieldWrapper>
  );
};

export type { SelectChangeEvent, Item, InfiniteScrollOptions };
export { InfiniteScrollSelect };
