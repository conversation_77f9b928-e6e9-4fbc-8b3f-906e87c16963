import CloseIcon from '@mui/icons-material/Close';
import {
  FormControl,
  FormHelperText,
  IconButton,
  Select as MuiSelect,
  SelectProps as MuiSelectProps,
  SelectChangeEvent,
  MenuItem,
  InputAdornment,
  Typography,
  InputLabel,
} from '@mui/material';
import { createStyles, makeStyles } from '@mui/styles';
import { useMemo, useState } from 'react';

import { FieldWrapper, FieldWrapperCommonProps } from '../input-text';

const useStyles = makeStyles(() =>
  createStyles({
    endAdornment: {
      width: '16px',
      height: '16px',
      marginRight: '16px',
      cursor: 'pointer',
      display: 'none',
    },

    close: {
      display: 'flex',
      alginItems: 'center',
      justifyContent: 'center',
    },
  }),
);

/**
 * Props that are specific to this component.
 */
type Item = {
  /**
   * Item's key. Key is display content.
   */
  key: string;
  /**
   * Item's value.
   */
  value: string;
};

type Props = {
  /**
   * Data input.
   */
  options: Item[];
  /**
   * Helper text.
   */
  helperText?: string;
  /**
   * Size of select.
   */
  size?: 'small' | 'medium';
  /**
   * The short hint displayed in the input before the user select a value
   */
  placeholder?: string;
  /**
   * Callback fired when the clear icon is clicked.
   */
  onClear?: () => void;
  /**
   * The label of the select custom.
   */
  customLabel?: string;

  isOptional?: boolean;
};

type SelectProps = Props & Omit<FieldWrapperCommonProps, 'id'> & MuiSelectProps;

const defaultRenderValue = (
  selectedOptions: unknown,
  optionsMap: Record<string, string>,
) => {
  if (Array.isArray(selectedOptions)) {
    return selectedOptions.map((key) => optionsMap[key]).join(', ');
  }

  return optionsMap[selectedOptions as string];
};

const Select: React.FC<SelectProps> = ({
  labelPosition = 'top',
  label,
  options,
  helperText,
  size = 'small',
  placeholder,
  renderValue: renderValueProp,
  defaultValue: defaultValueProp,
  displayEmpty: displayEmptyProp,
  endAdornment,
  onClear,
  customLabel,
  isOptional = false,
  ...restProps
}: SelectProps) => {
  const { value: selectedValue, ...otherRestProps } = restProps;
  const classes = useStyles();

  const isControlled = typeof selectedValue !== 'undefined';
  const value = isControlled ? selectedValue : defaultValueProp ?? '';
  const [hover, setHover] = useState<boolean>(false);

  const isEmptyValue = useMemo(() => {
    return (
      !selectedValue || (Array.isArray(selectedValue) && !selectedValue.length)
    );
  }, [selectedValue]);

  const optionsMap = useMemo(() => {
    return options.reduce(
      (acc, item) => {
        acc[item.value] = item.key;
        return acc;
      },
      {} as Record<string, string>,
    );
  }, [options]);

  const renderValueOptions: (
    selectedOptions: unknown,
    optionsMap: Record<string, string>,
  ) => React.ReactNode = useMemo(() => {
    return renderValueProp ? renderValueProp : defaultRenderValue;
  }, [renderValueProp]);

  const renderEndAdornment = () => {
    if (!onClear || !hover) {
      return endAdornment ?? undefined;
    }
    return (
      <InputAdornment
        position="end"
        className={`${classes.endAdornment} ${value ? classes.close : ''}`}
      >
        <IconButton
          focusRipple
          onClick={onClear}
          sx={{
            fontSize: '8px',
            width: '1.5rem',
            height: '1.5rem',
          }}
        >
          <CloseIcon />
        </IconButton>
        {endAdornment}
      </InputAdornment>
    );
  };

  return (
    <FieldWrapper
      id={`${restProps.id}-select`}
      labelPosition={labelPosition}
      required={restProps.required}
    >
      <FormControl fullWidth={restProps.fullWidth} size={size}>
        {label && <InputLabel>{label}</InputLabel>}
        <MuiSelect
          {...otherRestProps}
          label={label}
          value={value}
          displayEmpty={!!placeholder || displayEmptyProp}
          defaultValue={defaultValueProp}
          variant="outlined"
          inputProps={{
            ...restProps.inputProps,
            id: `${restProps.id}-select`,
          }}
          endAdornment={renderEndAdornment()}
          renderValue={(selected) => {
            if (placeholder && isEmptyValue) {
              return <span style={{ color: '#A9A9A9' }}>{placeholder}</span>;
            }

            const value = renderValueOptions(selected, optionsMap);
            return value;
          }}
          startAdornment={
            customLabel && (
              <InputAdornment position="start">
                <Typography variant="body2" style={{ fontWeight: 'bold' }}>
                  {customLabel}:
                </Typography>
              </InputAdornment>
            )
          }
          onMouseEnter={() => setHover(true)}
          onMouseLeave={() => setHover(false)}
          onClose={() => {
            setHover(false);
            setTimeout(() => {
              if (document.activeElement instanceof HTMLElement) {
                document.activeElement.blur();
              }
            }, 0);
          }}
        >
          {options.map((item) => (
            <MenuItem
              key={item.value}
              value={item.value}
              disabled={restProps.disabled}
            >
              {item.key}
            </MenuItem>
          ))}
        </MuiSelect>
        {helperText && (
          <FormHelperText
            error={restProps.error}
            style={{ marginLeft: 'unset' }}
          >
            {helperText}
          </FormHelperText>
        )}

        {isOptional && (
          <Typography
            sx={{
              fontSize: '11px',
              fontWeight: 700,
              position: 'absolute',
              right: '8px',
              top: '-8px',
              backgroundColor: 'white',
              padding: '0 4px',
              zIndex: 1,
            }}
          >
            任意項目
          </Typography>
        )}
      </FormControl>
    </FieldWrapper>
  );
};

export type { SelectChangeEvent, Item };
export { Select };
