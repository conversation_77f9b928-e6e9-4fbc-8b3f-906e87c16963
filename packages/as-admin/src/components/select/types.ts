export interface HierarchicalItem {
  id: string;
  label: string;
  value: string;
  level: number;
  children?: HierarchicalItem[];
  disabled?: boolean;
}

export interface HierarchicalSelectProps {
  items: HierarchicalItem[];
  value?: string;
  placeholder?: string;
  label?: string;
  onChange?: (value: string) => void;
  className?: string;
  disabled?: boolean;
  error?: boolean;
  helperText?: string;
  fullWidth?: boolean;
}

export interface FlattenedItem extends HierarchicalItem {
  visible: boolean;
}
