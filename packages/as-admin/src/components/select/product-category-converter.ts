import { ProductCategory } from '@/bundles/model';
import { getDisplayNameDefault } from '@/constants/utils';

import { HierarchicalItem } from './types';

// Find ProductCategory by ID in hierarchical structure
export const findProductCategoryById = (
  categories: ProductCategory[],
  categoryId: string,
): ProductCategory | undefined => {
  for (const category of categories) {
    if (category.id === categoryId) {
      return category;
    }
    if (category.subCategories) {
      const found = findProductCategoryById(category.subCategories, categoryId);
      if (found) return found;
    }
  }
  return undefined;
};

// Get all category IDs from hierarchy (useful for selections)
export const getAllCategoryIds = (categories: ProductCategory[]): string[] => {
  const ids: string[] = [];

  categories.forEach((category) => {
    if (category.id) {
      ids.push(category.id);
    }
    if (category.subCategories) {
      ids.push(...getAllCategoryIds(category.subCategories));
    }
  });

  return ids;
};

// Get category path (breadcrumb) by ID
export const getCategoryPath = (
  categories: ProductCategory[],
  categoryId: string,
  path: string[] = [],
): string[] => {
  for (const category of categories) {
    const displayName =
      getDisplayNameDefault(category.displayName) || 'Unnamed';
    const currentPath = [...path, displayName];

    if (category.id === categoryId) {
      return currentPath;
    }

    if (category.subCategories) {
      const result = getCategoryPath(
        category.subCategories,
        categoryId,
        currentPath,
      );
      if (result.length > 0) {
        return result;
      }
    }
  }
  return [];
};

// Helper function to check if category should be disabled (self, descendant, or ancestor)
const shouldDisableCategory = (
  category: ProductCategory,
  disabledCategoryId?: string,
  allCategories?: ProductCategory[],
): boolean => {
  if (!disabledCategoryId || !allCategories) return false;

  // Disable if it's the selected category itself
  if (category.id === disabledCategoryId) return true;

  // Flatten all categories with their parent info
  const flattenWithParent = (
    categories: ProductCategory[],
    parentId: string | null = null,
  ): Array<{ id: string; parentId: string | null }> => {
    const result: Array<{ id: string; parentId: string | null }> = [];

    for (const cat of categories) {
      if (cat.id) {
        result.push({ id: cat.id, parentId });

        if (cat.subCategories) {
          result.push(...flattenWithParent(cat.subCategories, cat.id));
        }
      }
    }

    return result;
  };

  const flatCategories = flattenWithParent(allCategories);
  const currentCategoryId = category.id;

  if (!currentCategoryId) return false;

  // Helper to check if A is ancestor of B
  const isAncestorOf = (ancestorId: string, targetId: string): boolean => {
    let current = flatCategories.find((cat) => cat.id === targetId);

    while (current && current.parentId) {
      if (current.parentId === ancestorId) {
        return true;
      }
      current = flatCategories.find((cat) => cat.id === current!.parentId);
    }

    return false;
  };

  // Disable if current category is ancestor of disabled category
  if (isAncestorOf(currentCategoryId, disabledCategoryId)) {
    return true;
  }

  // Disable if disabled category is ancestor of current category
  if (isAncestorOf(disabledCategoryId, currentCategoryId)) {
    return true;
  }

  return false;
};

// Convert ProductCategory to HierarchicalItem with option to disable specific category
export const convertProductCategoryToHierarchicalWithDisabled = (
  category: ProductCategory,
  level: number = 0,
  disabledCategoryId?: string,
  allCategories?: ProductCategory[],
): HierarchicalItem => {
  const displayName = getDisplayNameDefault(category.displayName);

  return {
    id: category.id || `category-${Date.now()}-${Math.random()}`,
    label: displayName || 'Unnamed Category',
    value: category.id || '',
    level,
    disabled:
      !category.active ||
      shouldDisableCategory(category, disabledCategoryId, allCategories),
    children: category.subCategories
      ? category.subCategories.map((subCategory) =>
          convertProductCategoryToHierarchicalWithDisabled(
            subCategory,
            level + 1,
            disabledCategoryId,
            allCategories,
          ),
        )
      : undefined,
  };
};

// Convert array of ProductCategory to HierarchicalItem array with option to disable specific category
export const convertProductCategoriesToHierarchicalWithDisabled = (
  categories: ProductCategory[],
  disabledCategoryId?: string,
): HierarchicalItem[] => {
  return categories.map((category) =>
    convertProductCategoryToHierarchicalWithDisabled(
      category,
      0,
      disabledCategoryId,
      categories,
    ),
  );
};
