import {
  FormControl,
  FormHelperText,
  MenuItem,
  Select,
  SelectChangeEvent,
  Typography,
} from '@mui/material';
import clsx from 'clsx';
import React, { useMemo, useCallback } from 'react';

import { HierarchicalSelectProps, HierarchicalItem } from './types';
import { flattenItems, findItemByValue } from './utils';

export const HierarchicalSelect: React.FC<HierarchicalSelectProps> = ({
  items,
  value = '',
  placeholder = '選択してください',
  onChange,
  className,
  disabled = false,
  error = false,
  helperText,
  fullWidth = true,
}) => {
  // Flatten items for display (all items visible since no expand/collapse)
  const flatItems = useMemo(() => {
    return flattenItems(items);
  }, [items]);

  // Handle selection change
  const handleChange = useCallback(
    (event: SelectChangeEvent<string>) => {
      const newValue = event.target.value;
      const item = findItemByValue(items, newValue);
      if (item && onChange) {
        onChange(newValue);
      }
    },
    [items, onChange],
  );

  // Render menu item with proper indentation only
  const renderMenuItem = useCallback((item: HierarchicalItem) => {
    const indentLevel = item.level * 20; // 20px per level

    return (
      <MenuItem
        key={item.id}
        value={item.value}
        disabled={item.disabled}
        className="!py-2 !min-h-[40px]"
        sx={{
          paddingLeft: `${16 + indentLevel}px !important`,
        }}
      >
        <Typography
          variant="body2"
          className={clsx(
            'flex-1 truncate',
            item.level > 0 && 'text-gray-600',
            item.disabled && 'text-gray-400',
          )}
        >
          {item.label}
        </Typography>
      </MenuItem>
    );
  }, []);

  const renderValue = useCallback(
    (selectedValue: string) => {
      if (!selectedValue) {
        return (
          <Typography variant="body2" className="text-gray-500">
            {placeholder}
          </Typography>
        );
      }
      const selectedItem = findItemByValue(items, selectedValue);
      return selectedItem ? selectedItem.label : '';
    },
    [items, placeholder],
  );

  return (
    <FormControl
      fullWidth={fullWidth}
      error={error}
      disabled={disabled}
      className={className}
    >
      <Select
        value={value}
        onChange={handleChange}
        displayEmpty
        renderValue={renderValue}
        MenuProps={{
          PaperProps: {
            style: {
              overflow: 'auto',
            },
          },
        }}
        sx={{
          '& .MuiSelect-select': {
            minHeight: 'unset !important',
          },
        }}
      >
        {/* Render flattened items */}
        {flatItems.map((item) => renderMenuItem(item))}
      </Select>

      {helperText && <FormHelperText>{helperText}</FormHelperText>}
    </FormControl>
  );
};
