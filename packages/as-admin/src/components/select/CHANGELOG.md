# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added

- Add `endAdornment` for `Select` component
- Apply MultiSelectWithCheckmarks component
- Add handle display placeholder
- Apply FieldWrapper to Select component
- Add Select component

### Changed

- Changed the behavior of `select option`, blur after selection
- Changed clear icon, using `IconButton` for `Select` component

### Fixed

- Fixed display placeholder for `Select component`
- Fixed display check all option for `MultiSelectWithCheckmark`
- Fixed display error
