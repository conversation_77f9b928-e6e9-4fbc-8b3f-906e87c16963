import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowLeftIcon from '@mui/icons-material/KeyboardArrowLeft';
import {
  Grid,
  Pagination,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import { makeStyles, createStyles } from '@mui/styles';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { BaseEntity } from '@/types';

import { Select, SelectChangeEvent } from './../select/Select';
import { TablePaginationProps } from './types';

const useStyles = makeStyles(() =>
  createStyles({
    select: {
      width: 'auto',
      '& .MuiSelect-select': {
        paddingRight: '8px !important',
      },
      '& .MuiOutlinedInput-notchedOutline': { border: 0 },
      '& .MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline': {
        border: 0,
      },
      '& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline': {
        border: 0,
      },
    },
  }),
);

export const TablePagination = <Entry extends BaseEntity>({
  paginationModel = { page: 1, pageSize: 10 },
  onPaginationModelChange,
  hideFooterPagination,
}: TablePaginationProps<Entry>) => {
  const classes = useStyles();
  const { t } = useTranslation();
  const theme = useTheme();
  const matchesSM = useMediaQuery(theme.breakpoints.down('sm'));

  const pagination = useMemo(() => paginationModel, [paginationModel]);

  if (hideFooterPagination) {
    return null;
  }

  const { page, pageSize, total } = paginationModel;
  const from = (page - 1) * pageSize + 1;
  const to = Math.min(page * pageSize, total || 0);
  const totalPages = total && pageSize ? Math.ceil(total / pageSize) : 1;

  const optionPageSizes = [
    { key: '10', value: '10' },
    { key: '50', value: '50' },
    { key: '100', value: '100' },
  ];

  const onChangePage = (_: React.ChangeEvent<unknown>, page: number) => {
    const newPagination = { ...pagination, page };
    onPaginationModelChange?.(newPagination);
  };

  const onChangeRowPerPage = (event: SelectChangeEvent<unknown>) => {
    const rowsPerPageNew = parseInt(event.target.value as string, 10);
    const newPagination = { ...pagination, page: 1, pageSize: rowsPerPageNew };
    onPaginationModelChange?.(newPagination);
  };

  return (
    <Grid
      item
      container
      sx={{
        padding: '16px',
      }}
      data-testid="table-pagination"
    >
      {!matchesSM && (
        <Grid item container alignItems="center" xs={4}>
          <Grid item>
            <Typography variant="body2">
              {t('component.table.displayedRows', {
                total: total || 0,
                from,
                to,
              })}
            </Typography>
          </Grid>
        </Grid>
      )}
      <Grid
        item
        container
        spacing={2}
        alignItems="center"
        justifyContent={matchesSM ? 'center' : 'flex-end'}
        xs={12}
        md={8}
      >
        <Grid item>
          <Select
            onChange={onChangeRowPerPage}
            options={optionPageSizes}
            value={pagination.pageSize}
            name="row-per-page"
            data-testid="row-per-page-select"
            id="row-per-page-select"
            size="small"
            className={classes.select}
            IconComponent={(props) => {
              return props.className.includes('MuiSelect-iconOpen') ? (
                <KeyboardArrowDownIcon />
              ) : (
                <KeyboardArrowLeftIcon />
              );
            }}
          />
        </Grid>
        <Grid item>
          <Typography variant="body2">
            {t('component.table.rowPerPage')}
          </Typography>
        </Grid>
        <Grid item>
          <Pagination
            count={totalPages}
            page={pagination.page}
            onChange={onChangePage}
            color="primary"
            showFirstButton
            showLastButton
          />
        </Grid>
      </Grid>
    </Grid>
  );
};
