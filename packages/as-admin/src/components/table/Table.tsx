import { TableContainer, Paper, Table as MuiTable } from '@mui/material';

import { BaseEntity } from '@/types';

import { TableBody } from './TableBody';
import { TableHead } from './TableHead';
import { TablePagination } from './TablePagination';
import { TableProvider } from './TableProvider';
import { TableProps } from './types';

export const Table = <Entry extends BaseEntity>({
  data,
  columns,
  sortData,
  onSortModelChange,
  checkboxSelection,
  rowSelectionModel,
  onRowSelectionModelChange,
  isLoading,
  paginationModel,
  onPaginationModelChange,
  hideFooterPagination = false,
  disableRowSelectionOnClick,
  onRowClick,
}: TableProps<Entry>) => {
  return (
    <TableProvider<Entry> tableData={data} rowsSelection={rowSelectionModel}>
      <Paper
        sx={{
          width: '100%',
        }}
      >
        <TableContainer>
          <MuiTable sx={{ minWidth: 650 }} aria-label="Table">
            <TableHead<Entry>
              count={data.length}
              columns={columns}
              sortData={sortData}
              onSortModelChange={onSortModelChange}
              checkboxSelection={checkboxSelection}
              onRowSelectionModelChange={onRowSelectionModelChange}
              rowSelectionModel={rowSelectionModel}
            />
            <TableBody<Entry>
              data={data}
              columns={columns}
              checkboxSelection={checkboxSelection}
              onRowSelectionModelChange={onRowSelectionModelChange}
              disableRowSelectionOnClick={disableRowSelectionOnClick}
              onRowClick={onRowClick}
              isLoading={isLoading}
              rowSelectionModel={rowSelectionModel}
            />
          </MuiTable>
        </TableContainer>
        <TablePagination<Entry>
          paginationModel={paginationModel}
          onPaginationModelChange={onPaginationModelChange}
          hideFooterPagination={
            hideFooterPagination || !data.length || isLoading
          }
        />
      </Paper>
    </TableProvider>
  );
};
