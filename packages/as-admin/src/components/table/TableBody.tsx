import {
  TableBody as MuiTableBody,
  TableRow,
  TableCell,
  Checkbox,
} from '@mui/material';
import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';

import { BaseEntity } from '@/types';
import { deepEqual } from '@/utils';

import { useTable } from './TableProvider';
import { TableBodyProps, TableRowContentProps } from './types';

export const TableBody = <Entry extends BaseEntity>({
  data,
  columns,
  checkboxSelection,
  onRowSelectionModelChange,
  disableRowSelectionOnClick,
  onRowClick,
  isLoading,
}: TableBodyProps<Entry>) => {
  const { t } = useTranslation();
  const { selectedData, handleCheckBoxChange } = useTable();

  if (isLoading) {
    const colSpan: number = checkboxSelection
      ? columns.length + 1
      : columns.length;

    return (
      <MuiTableBody data-testid="table-body">
        <TableRow>
          <TableCell
            colSpan={colSpan}
            style={{ textAlign: 'center' }}
          ></TableCell>
        </TableRow>
      </MuiTableBody>
    );
  }

  if (!data?.length) {
    const colSpan: number = checkboxSelection
      ? columns.length + 1
      : columns.length;

    return (
      <MuiTableBody data-testid="table-body">
        <TableRow>
          <TableCell colSpan={colSpan} style={{ textAlign: 'center' }}>
            {t('component.table.noRows')}
          </TableCell>
        </TableRow>
      </MuiTableBody>
    );
  }

  return (
    <MuiTableBody data-testid="table-body">
      {data.map((entry, index) => (
        <MemoTableRow
          key={entry.id ?? `table-row-${index}`}
          rowData={entry}
          columns={columns}
          checkboxSelection={checkboxSelection}
          isChecked={selectedData.includes(entry.id ?? '')}
          onRowSelectionChange={(e) =>
            handleCheckBoxChange(e, onRowSelectionModelChange)
          }
          disableRowSelectionOnClick={disableRowSelectionOnClick}
          onRowClick={onRowClick}
        />
      ))}
    </MuiTableBody>
  );
};

const TableRowContent = <Entry extends BaseEntity>({
  rowData,
  columns,
  checkboxSelection,
  onRowSelectionChange,
  isChecked,
  disableRowSelectionOnClick,
  onRowClick,
}: TableRowContentProps<Entry>) => {
  const handleChangeCheckBox = () => {
    const { id } = rowData;

    onRowSelectionChange?.({
      target: {
        value: id,
        valueAsNumber: typeof id === 'number' ? id : NaN,
        checked: !isChecked,
      },
    } as React.ChangeEvent<HTMLInputElement>);
  };

  const handleOnClickRow = (
    event: React.MouseEvent<HTMLTableRowElement, MouseEvent>,
  ) => {
    if (!rowData.id) return;

    const isInputElement = event.target instanceof HTMLInputElement;
    if (!disableRowSelectionOnClick && !isInputElement) {
      onRowClick?.(rowData.id);
    } else {
      handleChangeCheckBox();
    }
  };
  const renderCellValue = (field: keyof Entry, entry: Entry) => {
    return <>{String(entry[field])}</>;
  };

  const renderCheckBoxCell = () => {
    if (!checkboxSelection) {
      return null;
    }

    return (
      <TableCell
        sx={{
          width: '50px',
        }}
      >
        <Checkbox
          color="primary"
          inputProps={{
            'aria-label': 'select item',
          }}
          checked={isChecked}
          onChange={() => handleChangeCheckBox()}
          onClick={(e) => e.stopPropagation()}
          value={rowData.id}
          name="checkbox"
        />
      </TableCell>
    );
  };

  return (
    <TableRow onClick={handleOnClickRow}>
      {renderCheckBoxCell()}
      {columns.map(({ Cell, field, style }, columnIndex) => {
        return (
          <TableCell key={`${rowData.id}-${columnIndex}`} style={style}>
            {Cell ? <Cell entry={rowData} /> : renderCellValue(field, rowData)}
          </TableCell>
        );
      })}
    </TableRow>
  );
};

const MemoTableRow = memo(TableRowContent, (prevProps, nextProps) => {
  if (prevProps.checkboxSelection && nextProps.checkboxSelection) {
    return deepEqual(
      {
        value: prevProps.rowData,
        isChecked: prevProps.isChecked,
      },
      {
        value: nextProps.rowData,
        isChecked: nextProps.isChecked,
      },
    );
  } else {
    return deepEqual(prevProps.rowData, nextProps.rowData);
  }
}) as <Entry extends BaseEntity>(
  props: TableRowContentProps<Entry>,
) => JSX.Element;
