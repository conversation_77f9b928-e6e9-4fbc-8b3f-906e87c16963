import { ReactNode } from 'react';

/**
 * Represents the identifier for a selected row.
 * Can be either a string or a number.
 */
type RowSelectionId = string | number;

/**
 * Represents the model for selecting rows.
 * It is an array of RowSelectionId, used for selecting multiple rows.
 */

type RowSelectionModel = Array<RowSelectionId>;

/**
 * Define sorting directions in table columns
 */

type SortDirection = 'asc' | 'desc';

/**
 * Defines the sorting configuration for a table column
 */
type SortModel<Entry> = {
  /**
   * Active sort field name, must be the key of the generic type
   */
  field: keyof Entry;
  /**
   * Sort direction
   */
  direction: SortDirection;
};

/**
 * Define the type of the sort field
 */
type SortField<Entry> = {
  /**
   * The field to sort by
   */
  sortBy: Entry[];
  /**
   * The direction to sort by
   */
  sortOrder: SortDirection[];
};

/**
 * Type of the label
 */
type TitleTypeEnum = 'sort' | 'default';

/**
 * Define TitleProps to describe the title of a table column
 */
type TitleProps = {
  /**
   * The display label for the column
   */
  label: string;
  /**
   * The type of title, either 'sort' or 'default'
   */
  type?: TitleTypeEnum;
};

/**
 * Props for individual cell components in a table column
 */
type CellProps<Entry> = {
  /**
   * Cell value.
   */
  entry: Entry;
};

/**
 * Defines the configuration of a table column, including its title and field
 */
type TableColumn<Entry> = {
  /**
   * Label of the column.
   */
  title: TitleProps;
  /**
   * Field name, must be the key of the generic type
   */
  field: keyof Entry;
  /**
   * Stylesheet apply for a column
   */
  style?: React.CSSProperties;
  /**
   * Custom display of cell
   */
  Cell?({ entry }: CellProps<Entry>): React.ReactElement;
};

type PaginationModel = {
  page: number;
  pageSize: number;
  total?: number;
};

/**
 * Props for the main table component
 */
type TableProps<Entry> = {
  /**
   * Data for table
   */
  data: Entry[];
  /**
   * Set of columns of the table
   */
  columns: TableColumn<Entry>[];
  /**
   * Sort object. Include the active sort field and the direction
   */
  sortData?: SortModel<Entry>;
  /**
   * Callback fired when the sort model changes before a column is sorted.
   */
  onSortModelChange?: (sortObj: SortModel<Entry>) => void;
  /**
   * If true, the grid get a first column with a checkbox that allows to select rows.
   */
  checkboxSelection?: boolean;
  /**
   * Sets the row selection model of the table
   */
  rowSelectionModel?: RowSelectionModel;
  /**
   * Callback fired when the selection state of one or multiple rows changes.
   */
  onRowSelectionModelChange?: (selectedDataIds: RowSelectionModel) => void;
  /**
   *  If true, disable all action in table
   */
  isLoading?: boolean;
  /**
   * The pagination model which refers to current page and pageSize.
   */
  paginationModel?: PaginationModel;
  /**
   *   Callback fired when the pagination model has changed.
   */
  onPaginationModelChange?: (pageModel: PaginationModel) => void;
  /**
   * If true, the pagination component in the footer is hidden.
   */
  hideFooterPagination?: boolean;
  /**
   * If true, the selection on click on a row or cell is disabled.
   */
  disableRowSelectionOnClick?: boolean;
  /**
   *  Callback fired when a row is clicked. Not called if disableRowSelectionOnClick props is true.
   */
  onRowClick?: (selectedRowId: RowSelectionId) => void;
};

/**
 * Props for the table head component
 */
type TableHeadProps<Entry> = Omit<TableProps<Entry>, 'data'> & {
  // Amount of data
  count: number;
};

/**
 * Props for the table pagination component
 */
type TablePaginationProps<Entry> = Pick<
  TableProps<Entry>,
  'paginationModel' | 'onPaginationModelChange' | 'hideFooterPagination'
>;

/**
 * Props for the table body component
 */
type TableBodyProps<Entry> = TableProps<Entry>;

/**
 * Props for the content of a table row
 */
type TableRowContentProps<Entry> = Pick<
  TableBodyProps<Entry>,
  'columns' | 'checkboxSelection' | 'disableRowSelectionOnClick' | 'onRowClick'
> & {
  /**
   * Data of the row
   */
  rowData: Entry;
  /**
   * State of the checkbox if checkboxSelection is true
   */
  isChecked: boolean;
  /**
   * Callback fired when the selection state of row changes.
   */
  onRowSelectionChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
};

/**
 * Define the type for the context data
 */
type TableContextType = {
  /**
   * The selected data
   */
  selectedData: RowSelectionModel;
  /**
   * Callback fired when the selection state of one row changes.
   */
  handleCheckBoxChange: (
    event: React.ChangeEvent<HTMLInputElement>,
    callBack?: (selectedData: RowSelectionModel) => void,
  ) => void;
  /**
   * Callback fired when the selection state of all rows changes.
   */
  handleCheckBoxAllChange: (
    event: React.ChangeEvent<HTMLInputElement>,
    callBack?: (selectedData: RowSelectionModel) => void,
  ) => void;
};

/**
 * Create a context for the table-related data
 */
type TableProviderProps<Entry> = {
  children: ReactNode;
  tableData: Entry[];
  rowsSelection?: RowSelectionModel;
};

export type {
  CellProps,
  PaginationModel,
  RowSelectionId,
  RowSelectionModel,
  SortField,
  SortModel,
  TableBodyProps,
  TableColumn,
  TableContextType,
  TableHeadProps,
  TablePaginationProps,
  TableProps,
  TableProviderProps,
  TableRowContentProps,
};
