import React, { createContext, useContext, useState, useEffect } from 'react';

import { BaseEntity } from '@/types';

import {
  RowSelectionModel,
  TableContextType,
  TableProviderProps,
} from './types';

const TableContext = createContext<TableContextType | undefined>(undefined);

const TableProvider = <Entry extends BaseEntity>({
  children,
  tableData,
  rowsSelection,
}: TableProviderProps<Entry>) => {
  // State to manage the selected data
  const [selectedData, setSelectedData] = useState<RowSelectionModel>([]);

  // Function to handle individual checkbox change
  const handleCheckBoxChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    callBack?: (selectedData: RowSelectionModel) => void,
  ) => {
    const { value, checked } = event.target;

    if (!value) {
      return;
    }

    setSelectedData((prevState) => {
      const selectedDataCopy = checked
        ? [...prevState, value]
        : prevState.filter((data) => data !== value);

      if (callBack) {
        callBack(selectedDataCopy);
      }

      return selectedDataCopy;
    });
  };

  // Function to handle the "Select All" checkbox change
  const handleCheckBoxAllChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    callBack?: (selectedData: RowSelectionModel) => void,
  ) => {
    const selectedData = event.target.checked
      ? tableData.map((entry) => entry.id)
      : [];

    const filteredSelectedData = selectedData.filter(
      Boolean,
    ) as RowSelectionModel;

    setSelectedData(filteredSelectedData);
    if (callBack) {
      callBack(filteredSelectedData);
    }
  };

  useEffect(() => {
    if (rowsSelection) {
      setSelectedData(rowsSelection);
    }

    return () => {
      setSelectedData([]);
    };
  }, [rowsSelection]);

  const contextValue: TableContextType = {
    selectedData,
    handleCheckBoxChange,
    handleCheckBoxAllChange,
  };

  return (
    <TableContext.Provider value={contextValue}>
      {children}
    </TableContext.Provider>
  );
};

const useTable = (): TableContextType => {
  const context = useContext(TableContext);
  if (!context) {
    throw new Error('useTable must be wrapped by TableProvider');
  }
  return context;
};

// eslint-disable-next-line react-refresh/only-export-components
export { TableProvider, useTable };
