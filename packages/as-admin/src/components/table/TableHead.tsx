import {
  TableCell,
  TableHead as Mui<PERSON><PERSON><PERSON><PERSON>,
  TableRow,
  TableSortLabel,
  Checkbox,
  Typography,
} from '@mui/material';

import { BaseEntity } from '@/types';

import { useTable } from './TableProvider';
import { TableHeadProps } from './types';

export const TableHead = <Entry extends BaseEntity>({
  count,
  columns,
  onRowSelectionModelChange,
  sortData,
  onSortModelChange,
  checkboxSelection,
}: TableHeadProps<Entry>) => {
  const { selectedData, handleCheckBoxAllChange } = useTable();

  // Handle sorting of the table columns
  const createSortHandler = (property: keyof Entry) => {
    if (!sortData) {
      return;
    }

    const { field, direction } = sortData;
    const isAsc: boolean = field === property && direction === 'asc';

    if (onSortModelChange) {
      // Trigger a callback when the column header is clicked to change sorting
      onSortModelChange({
        field: property,
        direction: isAsc ? 'desc' : 'asc',
      });
    }
  };

  const renderCheckBoxCell = () => {
    if (!checkboxSelection) {
      return;
    }

    return (
      <TableCell
        sx={{
          width: '50px',
        }}
      >
        <Checkbox
          color="primary"
          inputProps={{
            'aria-label': 'select item',
          }}
          checked={selectedData.length !== 0 && count === selectedData.length}
          indeterminate={
            selectedData.length !== 0 && count > selectedData.length
          }
          onChange={(e) =>
            handleCheckBoxAllChange(e, onRowSelectionModelChange)
          }
          name="checkbox-all"
        />
      </TableCell>
    );
  };

  return (
    <MuiTableHead data-testid="table-head">
      <TableRow>
        {renderCheckBoxCell()}
        {columns.map(({ title, field, style }, index) => {
          const { type = 'default', label } = title;
          return (
            <TableCell
              key={label + index}
              style={{ wordBreak: 'keep-all', ...style }}
            >
              {type === 'sort' ? (
                <TableSortLabel
                  active={sortData?.field === field}
                  direction={sortData?.direction || 'desc'}
                  onClick={() => createSortHandler(field)}
                >
                  <Typography variant="h3">{label}</Typography>
                </TableSortLabel>
              ) : (
                <Typography variant="h3">{label}</Typography>
              )}
            </TableCell>
          );
        })}
      </TableRow>
    </MuiTableHead>
  );
};
