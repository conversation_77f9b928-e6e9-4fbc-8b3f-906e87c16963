import { Avatar } from '@mui/material';

import { getAvatarInitial } from '@/constants';

export const UserAvatar = ({ nickname }: { nickname?: string }) => {
  if (!nickname) {
    return <Avatar className="w-12 h-12 bg-gray-300" />;
  }

  return (
    <Avatar
      className="w-12 h-12"
      sx={{
        bgcolor: '#9E9E9E',
        color: 'white',
        fontWeight: 'bold',
        fontSize: '16px',
      }}
    >
      {getAvatarInitial(nickname)}
    </Avatar>
  );
};
