import { ButtonOwnProps, Button as MUIButton } from '@mui/material';
import clsx from 'clsx';
import React, { useCallback } from 'react';

import { Spinner } from '../../feedback/spinner';

import { ColorScheme } from './types';

const sizes = {
  sm: 'py-2 px-4 text-sm',
  md: 'py-2 px-6 text-md',
  lg: 'py-3 px-8 text-lg',
};

type ButtonIconProps =
  | { startIcon: React.ReactElement; endIcon?: never }
  | { endIcon: React.ReactElement; startIcon?: never }
  | { endIcon?: undefined; startIcon?: undefined };

type ButtonProps = React.ButtonHTMLAttributes<HTMLButtonElement> & {
  /**
   * The color appearance of the Button
   */
  colorScheme?: ColorScheme;
  /**
   * The size of Button
   */
  sizeButton?: keyof typeof sizes;
  /**
   * Custom class name
   */
  className?: string;
  /**
   * Show spinner when loading
   */
  isLoading?: boolean;
  /**
   * Display text when loading
   */
  loadingText?: string;
  /**
   * Determines the placement of the spinner when isLoading is true
   */
  spinnerPlacement?: 'start' | 'end';
} & ButtonOwnProps &
  ButtonIconProps;

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = 'contained',
      sizeButton = 'md',
      colorScheme = 'teal',
      className,
      startIcon,
      endIcon,
      isLoading = false,
      loadingText,
      spinnerPlacement = 'start',
      children,
      ...restProps
    },
    ref,
  ) => {
    const renderSpinner = useCallback(
      () => (
        <Spinner
          size={sizeButton}
          color={variant === 'contained' ? 'white' : colorScheme}
        />
      ),
      [sizeButton, variant, colorScheme],
    );

    return (
      <MUIButton
        ref={ref}
        type="button"
        className={clsx(
          'h-8 w-20 flex justify-center items-center transition-all disabled:opacity-70 disabled:shadow-none disabled:cursor-not-allowed !px-0',
          sizes[sizeButton],
          className,
        )}
        {...(restProps as any)}
        disabled={isLoading || restProps.disabled}
        variant={variant}
      >
        {isLoading && spinnerPlacement === 'start' && renderSpinner()}

        {!isLoading && startIcon}

        <span>{loadingText && isLoading ? loadingText : children}</span>

        {!isLoading && endIcon}

        {isLoading && spinnerPlacement === 'end' && renderSpinner()}
      </MUIButton>
    );
  },
);

Button.displayName = 'Button';

export type { ColorScheme, ButtonProps };
