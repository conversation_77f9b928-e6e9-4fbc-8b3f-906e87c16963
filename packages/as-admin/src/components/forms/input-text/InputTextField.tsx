import AccountBoxOutlinedIcon from '@mui/icons-material/AccountBoxOutlined';
import CloseIcon from '@mui/icons-material/Close';
import LockOutlinedIcon from '@mui/icons-material/LockOutlined';
import VisibilityOffOutlinedIcon from '@mui/icons-material/VisibilityOffOutlined';
import VisibilityOutlinedIcon from '@mui/icons-material/VisibilityOutlined';
import {
  IconButton,
  InputAdornment,
  TextField,
  TextFieldProps,
} from '@mui/material';
import { createStyles, makeStyles } from '@mui/styles';
import clsx from 'clsx';
import React, { useState } from 'react';
import { UseFormRegisterReturn } from 'react-hook-form';

import { FieldWrapperProps } from '@/components/forms/field-wrapper/FieldWrapper';

const useStyles = makeStyles(() =>
  createStyles({
    inputTextField: {
      '& .MuiOutlinedInput-root': {
        backgroundColor: '#FFFFFF',
        borderRadius: '4px',
        height: '48px',
      },
      '& .MuiOutlinedInput-adornedEnd': {
        paddingRight: 'unset',
      },
      '& .MuiInputAdornment-positionEnd': {
        marginLeft: 'unset',
      },
      '& .MuiInputAdornment-positionStart': {
        marginRight: '8px',
      },
      '& .MuiIconButton-root': {
        padding: '4px',
        marginRight: 'unset',
      },
      '& .MuiFormHelperText-root': {
        marginRight: 'unset',
        marginLeft: 'unset',
      },
      '& .MuiInputBase-adornedEnd': {
        paddingRight: '8px',
      },
      '& .MuiInputBase-adornedStart': {
        paddingLeft: '8px',
      },
      '& .MuiInputLabel-shrink': {
        transform: 'translate(14px, -6px) scale(0.75)',
      },
      '& .MuiInputBase-input': {
        fontSize: '16px',
      },
    },
    eyeIcon: {
      backgroundColor: '#EBF3F3',
      borderRadius: '0 4px 4px 0',
      height: '100%',
      marginLeft: '8px',
      padding: '12px !important',
      '&:hover': {
        backgroundColor: '#EBF3F3',
      },
    },
  }),
);

type Props = {
  /**
   * Registration form props for react-hook-form.
   */
  registrationForm?: Partial<UseFormRegisterReturn>;
  /**
   * The sub-label for password input
   */
  subLabelPassword?: string;
  /**
   * The function to be called when the clear button is clicked
   */
  onClear?: () => void;
  /**
   * Icon to display at the start of the input
   */
  startIcon?: 'person' | 'lock' | React.ReactNode;

  hasValue?: boolean;
};

export type InputTextFieldProps = Props &
  Omit<FieldWrapperProps, 'id'> &
  TextFieldProps;

export const InputTextField: React.FC<InputTextFieldProps> = ({
  label,
  registrationForm = {},
  onClear,
  startIcon,
  hasValue,
  ...restProps
}: InputTextFieldProps) => {
  const classes = useStyles();
  const [showPassword, setShowPassword] = useState(false);
  const isPassword = restProps.type === 'password';

  const handleClickShowPassword = () => {
    setShowPassword((prev) => !prev);
  };

  const handleMouseDownPassword = (
    event: React.MouseEvent<HTMLButtonElement>,
  ) => {
    event.preventDefault();
  };

  const handleMouseUpPassword = (
    event: React.MouseEvent<HTMLButtonElement>,
  ) => {
    event.preventDefault();
  };

  const handleClear = () => {
    if (onClear) {
      onClear();
    }
  };

  const renderStartAdornment = () => {
    if (startIcon === 'person') {
      return (
        <InputAdornment position="start">
          <AccountBoxOutlinedIcon />
        </InputAdornment>
      );
    }

    if (startIcon === 'lock' || isPassword) {
      return (
        <InputAdornment position="start">
          <LockOutlinedIcon />
        </InputAdornment>
      );
    }

    if (startIcon) {
      return <InputAdornment position="start">{startIcon}</InputAdornment>;
    }

    return restProps?.InputProps?.startAdornment;
  };

  const renderEndAdornment = () => {
    const existingEndAdornment = restProps?.InputProps?.endAdornment;

    return (
      <InputAdornment position="end">
        {existingEndAdornment}
        {hasValue && (
          <IconButton
            aria-label="clear input"
            onClick={handleClear}
            edge="end"
            size="small"
            onMouseUp={handleMouseUpPassword}
          >
            <CloseIcon className="!fill-onSurface" />
          </IconButton>
        )}
        {isPassword && (
          <IconButton
            aria-label={
              showPassword ? 'hide the password' : 'display the password'
            }
            onClick={handleClickShowPassword}
            onMouseDown={handleMouseDownPassword}
            onMouseUp={handleMouseUpPassword}
            edge="end"
            className={classes.eyeIcon}
          >
            {showPassword ? (
              <VisibilityOffOutlinedIcon />
            ) : (
              <VisibilityOutlinedIcon />
            )}
          </IconButton>
        )}
      </InputAdornment>
    );
  };

  return (
    <TextField
      {...restProps}
      label={label ? label.toString() : undefined}
      placeholder={label ? label.toString() : undefined}
      type={isPassword && showPassword ? 'text' : restProps.type || 'text'}
      InputProps={{
        ...restProps.InputProps,
        startAdornment: renderStartAdornment(),
        endAdornment: renderEndAdornment(),
      }}
      {...registrationForm}
      className={clsx(classes.inputTextField, restProps.className)}
      variant="outlined"
      InputLabelProps={{
        ...restProps.InputLabelProps,
        shrink: hasValue || restProps.focused,
      }}
    />
  );
};
