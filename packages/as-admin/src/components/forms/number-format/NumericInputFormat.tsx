import React from 'react';
import { NumericFormat, OnValueChange } from 'react-number-format';

import { InputTextField } from '@/components/forms/input-text';

interface NumericInputFormatProps {
  value?: string | number | null;
  onChange: (value: string) => void;
  label?: string;
  placeholder?: string;
  prefix?: string;
  suffix?: string;
  className?: string;
  maxLength?: number;
  onClear?: () => void;
  displayType?: 'text' | 'input';
  size?: 'small' | 'medium';
  disabled?: boolean;
  error?: boolean;
  helperText?: string;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
}

export const NumericInputFormat = ({
  value,
  onChange,
  label,
  placeholder,
  prefix,
  suffix,
  className,
  onClear,
  maxLength,
  displayType = 'input',
  size = 'small',
  disabled = false,
  error = false,
  helperText,
  startIcon,
  endIcon,
}: NumericInputFormatProps) => {
  const handleValueChange: OnValueChange = (values) => {
    onChange(values.value || '');
  };

  if (displayType === 'text') {
    return (
      <NumericFormat
        value={value || ''}
        thousandSeparator={true}
        decimalScale={2}
        prefix={prefix}
        suffix={suffix}
        displayType="text"
        className={className}
      />
    );
  }

  const inputProps: any = {
    value: value || '',
    onValueChange: handleValueChange,
    thousandSeparator: true,
    decimalScale: 2,
    allowNegative: false,
    prefix,
    suffix,
    customInput: InputTextField,
    label,
    placeholder: placeholder || label,
    onClear,
    size,
    inputProps: { maxLength },
    className,
    InputProps: {
      endAdornment: value ? endIcon : undefined,
      startAdornment: startIcon,
    },
  };

  if (disabled) inputProps.disabled = disabled;
  if (error) inputProps.error = error;
  if (helperText) inputProps.helperText = helperText;

  return (
    <NumericFormat
      {...inputProps}
      sx={{
        '& .MuiInputBase-input': {
          padding: `${startIcon ? '0px' : '0px 14px'}`,
        },
      }}
    />
  );
};
