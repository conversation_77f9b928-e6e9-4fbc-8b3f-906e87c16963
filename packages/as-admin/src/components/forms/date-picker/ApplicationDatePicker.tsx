import CalendarMonthOutlinedIcon from '@mui/icons-material/CalendarMonthOutlined';
import SearchOutlinedIcon from '@mui/icons-material/SearchOutlined';
import { InputAdornment } from '@mui/material';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { jaJP } from '@mui/x-date-pickers/locales';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import dayjs, { Dayjs } from 'dayjs';
import { useCallback, useState } from 'react';

import { DateFormat } from '@/constants/date';

import 'dayjs/locale/ja';
dayjs.locale('ja');

interface ApplicationDatePickerProps {
  label?: string;
  value: Dayjs | null;
  onChange: (value: string) => void;
  minDate?: Dayjs;
  disabled?: boolean;
}

export const ApplicationDatePicker = ({
  label = '更新適用日',
  value,
  onChange,
  minDate = dayjs(),
  disabled = false,
}: ApplicationDatePickerProps) => {
  const [open, setOpen] = useState(false);

  const handleChange = useCallback(
    (newValue: Dayjs | null) => {
      if (newValue) {
        onChange(newValue.format(DateFormat.fullDateWithHyphen));
      }
    },
    [onChange],
  );

  const handleAdornmentClick = useCallback(() => {
    if (!disabled) {
      setOpen(true);
    }
  }, [disabled]);

  const handleClose = useCallback(() => {
    setOpen(false);
  }, []);

  return (
    <LocalizationProvider
      dateAdapter={AdapterDayjs}
      adapterLocale="ja"
      localeText={
        jaJP.components.MuiLocalizationProvider.defaultProps.localeText
      }
    >
      <DatePicker
        label={label}
        open={open}
        onClose={handleClose}
        minDate={minDate}
        value={value}
        onChange={(newValue) => handleChange(dayjs(newValue))}
        format={DateFormat.fullDateYYYYMMDDWithDot}
        disabled={disabled}
        shouldDisableDate={(date) => date.isBefore(minDate, 'day')}
        slotProps={{
          textField: {
            error: false,
            helperText: null,
            InputProps: {
              style: { cursor: disabled ? 'default' : 'pointer' },
              startAdornment: (
                <InputAdornment position="start">
                  <CalendarMonthOutlinedIcon
                    className={`${disabled ? 'opacity-50' : ''}`}
                  />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <SearchOutlinedIcon
                    className={`${disabled ? 'opacity-50' : ''}`}
                  />
                </InputAdornment>
              ),
            },
            onClick: handleAdornmentClick,
            onKeyDown: (e: React.KeyboardEvent) => {
              e.preventDefault();
            },
          },
        }}
        sx={{
          width: '100%',
          mb: 3,
          '& .MuiPickersOutlinedInput-root': {
            color: '#151D1D',
            '& fieldset': { borderColor: '#e5e7eb' },
            '&:hover fieldset': { borderColor: '#d1d5db' },
            '&.Mui-focused fieldset': { borderColor: '#1B666D' },
          },
        }}
      />
    </LocalizationProvider>
  );
};
