import CloseIcon from '@mui/icons-material/Close';
import SearchIcon from '@mui/icons-material/Search';
import UploadFileOutlinedIcon from '@mui/icons-material/UploadFileOutlined';
import {
  InputAdornment,
  TextField,
  TextFieldProps,
  IconButton,
  styled,
} from '@mui/material';
import { createStyles, makeStyles } from '@mui/styles';
import clsx from 'clsx';
import React, { useRef, useState } from 'react';

interface FileUploadInputProps extends Omit<TextFieldProps, 'onChange'> {
  accept?: string;
  file?: File;
  onChange?: (file: File) => void;
}

const useStyles = makeStyles(() =>
  createStyles({
    inputTextField: {
      '& .MuiOutlinedInput-root': {
        backgroundColor: '#FFFFFF',
        borderRadius: '4px',
        height: '48px',
      },
      '& .MuiOutlinedInput-adornedEnd': {
        paddingRight: 'unset',
      },
      '& .MuiInputAdornment-positionEnd': {
        marginLeft: 'unset',
      },
      '& .MuiInputAdornment-positionStart': {
        marginRight: '8px',
      },
      '& .MuiIconButton-root': {
        padding: '4px 4px 4px 16px',
        marginRight: 'unset',
      },
      '& .MuiFormHelperText-root': {
        marginRight: 'unset',
        marginLeft: 'unset',
      },
      '& .MuiInputBase-adornedEnd': {
        paddingRight: '8px',
      },
      '& .MuiInputBase-adornedStart': {
        paddingLeft: '8px',
      },
      '& .MuiInputLabel-shrink': {
        transform: 'translate(14px, -6px) scale(0.75)',
      },
      '& .MuiInputBase-input': {
        fontSize: '16px',
        paddingLeft: '0px',
      },
    },
  }),
);

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

export const FileUploadInput: React.FC<FileUploadInputProps> = ({
  accept = '',
  onChange,
  file,
  disabled,
  ...textFieldProps
}) => {
  const classes = useStyles();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [fileName, setFileName] = useState<string>('');

  // Handle click on the icon to trigger file input
  const handleIconClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Handle file selection
  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      setFileName(file.name);

      if (onChange) {
        onChange(file);
      }
    }
  };

  // Handle clear button click
  const handleClear = () => {
    setFileName('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Update file name when value changes externally
  React.useEffect(() => {
    if (file) {
      setFileName(file.name);
    } else {
      setFileName('');
    }
  }, [file]);

  return (
    <>
      <TextField
        {...textFieldProps}
        value={fileName}
        disabled={disabled}
        className={clsx(classes.inputTextField, textFieldProps.className)}
        InputProps={{
          readOnly: true,
          startAdornment: (
            <InputAdornment position="start">
              <IconButton
                color="primary"
                onClick={handleIconClick}
                edge="start"
                disabled={disabled}
              >
                <UploadFileOutlinedIcon />
              </IconButton>
            </InputAdornment>
          ),
          endAdornment: fileName ? (
            <InputAdornment position="end">
              <IconButton edge="end" onClick={handleClear} disabled={disabled}>
                <CloseIcon />
              </IconButton>
            </InputAdornment>
          ) : (
            <InputAdornment position="end">
              <IconButton edge="end" onClick={() => {}} disabled={disabled}>
                <SearchIcon />
              </IconButton>
            </InputAdornment>
          ),
          ...textFieldProps.InputProps,
        }}
      />
      <VisuallyHiddenInput
        type="file"
        onChange={handleFileChange}
        ref={fileInputRef}
        disabled={disabled}
        accept={accept}
      />
    </>
  );
};
