import { routes } from '@/routes/routes';

import { Menu } from './types';

const SELECTED_ITEM_BACKGROUND = '#DFEFEE';

const SIDEBAR_WIDTH = 270;
const MIN_SIDEBAR_WIDTH = 58; // Minimum width
const MAX_SIDEBAR_WIDTH = 270; // Maximum width
const APP_BAR_HEIGHT = 49;

// const userNavigation:  MenuItemProps[] = [
//   {
//     name: 'loginInfo.titleSidebar',
//     icon: <StorageIcon />,
//     path: '/login-info',
//     isContent: false,
//   },
//   {
//     name: 'dashboard.title',
//     icon: <AnalyticsIcon />,
//     path: '/dashboard',
//     isContent: false,
//   },
//   {
//     name: 'caseManagement.title',
//     icon: <ErrorIcon />,
//     children: [
//       {
//         name: 'caseManagement.menu.all',
//         icon: <ViewListIcon />,
//         path: ['/cases/all', '/cases/import'],
//         accessEntity: 'menu:casesall',
//       },
//       {
//         name: 'caseManagement.menu.approvalPending',
//         icon: <HourglassBottomIcon />,
//         path: '/cases/approval-pending',
//         accessEntity: 'menu:casesapprovalpending',
//       },
//       {
//         name: 'caseManagement.menu.editing',
//         icon: <AssignmentLateIcon />,
//         path: '/cases/editing',
//         accessEntity: 'menu:casesediting',
//       },
//     ],
//   },
// ];

const userNavigation: Menu[] = [
  {
    title: 'menu.home',
    path: routes.home.index,
    isContent: false,
    icon: 'HomeIcon',
  },
  {
    title: '顧客対応',
    path: routes.customerManagement.wildcard,
    isContent: false,
    icon: 'MarkUnreadChatAltIcon',
  },
  {
    title: 'ユーザー管理',
    isContent: false,
    icon: 'GroupIcon',
    path: `${routes.userManagement.index}${routes.userManagement.search.index}`,
  },
  {
    title: '出金管理',
    isContent: false,
    icon: 'AttachMoneyIcon',
  },
  {
    title: 'コンテンツ管理',
    isContent: false,
    icon: 'ArticleIcon',
    path: `${routes.contentManagement.index}`,
  },
  {
    title: 'マスタ管理',
    path: routes.masterManagement.wildcard,
    isContent: false,
    icon: 'StorageIcon',
  },
];

export {
  SELECTED_ITEM_BACKGROUND,
  SIDEBAR_WIDTH,
  MIN_SIDEBAR_WIDTH,
  MAX_SIDEBAR_WIDTH,
  userNavigation,
  APP_BAR_HEIGHT,
};
