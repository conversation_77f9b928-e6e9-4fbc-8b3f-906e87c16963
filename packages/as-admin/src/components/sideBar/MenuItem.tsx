import ArrowRightIcon from '@mui/icons-material/ArrowRight';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowLeftIcon from '@mui/icons-material/KeyboardArrowLeft';
import {
  Badge,
  Collapse,
  Grid,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
} from '@mui/material';
import { createStyles, makeStyles, withStyles } from '@mui/styles';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import MaterialFontIcon from '../icons/MaterialFontIcon';

import { SELECTED_ITEM_BACKGROUND } from './constants';
import { Menu } from './types';

const useStyles = makeStyles(() =>
  createStyles({
    menuItem: {},
  }),
);

const ItemButton = withStyles({
  root: {
    height: '36px',
    padding: '0 16px',
    '&& svg': {
      color: '#006A6A',
    },
    '&:hover': {
      backgroundColor: SELECTED_ITEM_BACKGROUND,
      color: '#006A6A',
      '&& svg': {
        color: '#006A6A',
      },
    },
  },
})(ListItemButton);

const ItemIcon = withStyles({
  root: {
    '&.active': {
      '&& svg': {
        color: '#006A6A',
      },
    },
  },
})(ListItemIcon);

const CollapsedItemButton = withStyles({
  root: {
    paddingRight: '16px',
    '&& svg': {
      color: SELECTED_ITEM_BACKGROUND,
    },
    '&:hover': {
      backgroundColor: SELECTED_ITEM_BACKGROUND,
      color: SELECTED_ITEM_BACKGROUND,
      '&& svg': {
        color: SELECTED_ITEM_BACKGROUND,
      },
    },
  },
})(ListItemButton);

const CollapsedItemIcon = withStyles({
  root: {
    '&.active': {
      '&& svg': {
        color: ` ${SELECTED_ITEM_BACKGROUND}`,
      },
    },
  },
})(ListItemIcon);

type MenuItemProps = Menu & {
  open: boolean;
  activeItem: string;
  updateActiveItem: React.Dispatch<React.SetStateAction<string>>;
};

const MenuItem: React.FC<MenuItemProps> = ({
  children,
  icon,
  path,
  title: name,
  updateActiveItem,
  activeItem,
  open,
}: MenuItemProps) => {
  const { t } = useTranslation();
  const classes = useStyles();
  const navigate = useNavigate();

  const isActive =
    path === activeItem ||
    children?.some(({ path }) => path === activeItem) ||
    false;

  const [openCollapseMenu, setOpenCollapseMenu] = useState<boolean>(isActive);

  const handleNavigate = (route?: string) => {
    if (route) {
      navigate(route);
      updateActiveItem(route);
    }
  };

  const renderCollapsedIcon = () =>
    children &&
    (openCollapseMenu ? <KeyboardArrowDownIcon /> : <KeyboardArrowLeftIcon />);

  const renderChildItem = () => {
    if (!children) return null;

    return (
      <Collapse in={openCollapseMenu} timeout="auto" unmountOnExit>
        {children.map(({ title: name, icon, path: childPath }) => {
          const isChildActive = childPath === activeItem;
          return (
            <ListItem disablePadding key={name}>
              <CollapsedItemButton
                sx={{ pl: 4 }}
                onClick={() => {
                  handleNavigate(childPath);
                }}
              >
                <CollapsedItemIcon
                  className={isChildActive ? 'active' : undefined}
                >
                  {icon && <MaterialFontIcon icon={icon} />}
                </CollapsedItemIcon>
                <Badge
                  max={99}
                  color={
                    childPath === '/cases/approval-pending'
                      ? 'error'
                      : 'primary'
                  }
                  slotProps={{
                    badge: {
                      style: {
                        top: '8px',
                        right: '-16px',
                      },
                    },
                  }}
                >
                  <ListItemText
                    style={{
                      width: '100%',
                      color: isChildActive
                        ? SELECTED_ITEM_BACKGROUND
                        : undefined,
                    }}
                    primary={t(name)}
                  />
                </Badge>
              </CollapsedItemButton>
            </ListItem>
          );
        })}
      </Collapse>
    );
  };

  return (
    <Grid item container direction="column" className={classes.menuItem}>
      <ListItem disablePadding>
        <ItemButton
          style={{
            backgroundColor: isActive ? SELECTED_ITEM_BACKGROUND : undefined,
          }}
          onClick={() => {
            if (children) {
              setOpenCollapseMenu((prevState) => !prevState);
              return;
            }
            handleNavigate(path);
          }}
        >
          <ItemIcon className={isActive ? 'active' : undefined}>
            {icon && <MaterialFontIcon icon={icon} color="primary" />}
          </ItemIcon>
          {open && (
            <>
              <ListItemText
                primary={<Typography variant="body2">{t(name)}</Typography>}
              />
              {children ? (
                renderCollapsedIcon()
              ) : (
                <ArrowRightIcon className="!fill-primary" />
              )}
            </>
          )}
        </ItemButton>
      </ListItem>
      {renderChildItem()}
    </Grid>
  );
};

export default MenuItem;
