import ArrowRightIcon from '@mui/icons-material/ArrowRight';
import SettingsIcon from '@mui/icons-material/Settings';
import ZoomInMapIcon from '@mui/icons-material/ZoomInMap';
import ZoomOutMapIcon from '@mui/icons-material/ZoomOutMap';
import {
  List,
  Grid,
  Theme,
  Box,
  IconButton,
  ListItem,
  ListItemIcon,
  ListItemText,
  Typography,
} from '@mui/material';
import { createStyles, makeStyles } from '@mui/styles';
import clsx from 'clsx';
import React, { useEffect, useState, useCallback, memo } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';

// eslint-disable-next-line import/no-restricted-paths
import { useMenuContent } from '@/features/cms/ui/stores/menuContentStore';
import { useLocalStorage } from '@/hooks/use-local-storage';
import { routes } from '@/routes/routes';
import storage from '@/utils/storage';

import { userNavigation } from './constants';
import MenuItem from './MenuItem';
import { Menu } from './types';

const useStyles = makeStyles((theme: Theme) =>
  createStyles({
    wrapped: {
      height: '100%',
      display: 'inline-flex',
      flexDirection: 'column',
      justifyContent: 'space-between',
      paddingBottom: '0px',
      paddingTop: '0px',
    },
    menuList: {
      '& > *:not(:last-child)': {
        borderBottom: '1px solid #B9CAC9',
      },

      [theme.breakpoints.down('sm')]: {
        '& > *': {
          borderBottom: '1px solid #B9CAC9',
        },
      },
    },
    wrapMenuLink: {
      padding: '48px 0',

      [theme.breakpoints.down('sm')]: {
        padding: 0,
      },
    },
    menuLink: {
      paddingBottom: '32px',
      display: 'flex',
      flexDirection: 'column',
      gap: '8px',

      [theme.breakpoints.down('sm')]: {
        padding: '16px 0',
        paddingBottom: '16px',
      },

      '&__link': {
        width: '100%',
        fontSize: '12px',

        '& a': {
          textDecoration: 'none',

          '&:visited': {
            color: 'blue',
          },
        },
      },
    },
    systemAdmin: {
      '& .MuiListItemButton-root': {
        height: '36px',
        padding: '0 16px',
      },
      '& .MuiListItemIcon-root': {
        color: '#006A6A',
        minWidth: '36px',
      },
    },
  }),
);

interface UserNavigationProps {
  open: boolean;
  isMobile: boolean;
  setOpen: (open: boolean) => void;
}

// Extracted SystemAdmin component
const SystemAdmin = memo(({ open }: { open: boolean }) => {
  const classes = useStyles();
  const navigate = useNavigate();

  const handleSystemAdminClick = () => {
    navigate('/system-admin');
  };

  return (
    <ListItem disablePadding className={classes.systemAdmin}>
      <Box
        className="flex items-center w-full h-9 px-4 cursor-pointer hover:bg-[#DFEFEE]"
        onClick={handleSystemAdminClick}
      >
        <ListItemIcon>
          <SettingsIcon className="!fill-primary" />
        </ListItemIcon>
        {open && (
          <>
            <ListItemText
              primary={
                <Typography variant="body2" className="text-onSurfaceVariant">
                  システム管理
                </Typography>
              }
            />
            <ArrowRightIcon className="!fill-primary" />
          </>
        )}
      </Box>
    </ListItem>
  );
});
SystemAdmin.displayName = 'SystemAdmin';

// Extracted ZoomButton component
const ZoomButton = memo(
  ({ isZoomedOut, onClick }: { isZoomedOut: boolean; onClick: () => void }) => (
    <Box className="flex justify-end pr-3 bg-secondaryFixedDim">
      <IconButton onClick={onClick} size="small">
        {isZoomedOut ? (
          <ZoomOutMapIcon className="!fill-primary" />
        ) : (
          <ZoomInMapIcon className="!fill-primary" />
        )}
      </IconButton>
    </Box>
  ),
);

ZoomButton.displayName = 'ZoomButton';

const UserNavigation: React.FC<UserNavigationProps> = ({
  open,
  isMobile,
  setOpen,
}) => {
  const classes = useStyles();
  const menuContent = useMenuContent();
  const [isZoomedOut, setIsZoomedOut] = useState<boolean>(false);

  const { i18n } = useTranslation();
  const { pathname } = useLocation();

  const [menuItems, setMenuItems] = useState<Menu[]>([]);

  const handleZoomClick = useCallback(() => {
    setIsZoomedOut((prev: boolean) => !prev);
    setOpen(!open);
    const event = new CustomEvent('sidebar-zoom', {
      detail: { width: !isZoomedOut ? 58 : 270 },
    });
    window.dispatchEvent(event);
  }, [isZoomedOut, setOpen, open]);

  const processMenuItems = useCallback(
    (content: typeof menuContent) => {
      const newMenuItems: Menu[] = JSON.parse(JSON.stringify(userNavigation));

      if (!content.length) {
        newMenuItems.push({
          title: 'menu.contentManagement',
          isContent: true,
          icon: 'StorageIcon',
          children: [],
        });
        return newMenuItems;
      }

      const router = routes.contentManagement.index;
      const contentMenuLanguage = content.filter(
        (m) => m.displayName && m.displayName[i18n.language],
      );

      if (contentMenuLanguage.length > 0) {
        const menuContentDynamic = contentMenuLanguage.map((item) => ({
          code: item.code,
          title: item.displayName?.[i18n.language] ?? '',
          isContent: false,
          path: `${router}${routes.contentManagement.faq.index}`,
        }));

        if (menuContentDynamic.length > 0) {
          newMenuItems.push({
            title: 'menu.contentManagement',
            isContent: true,
            icon: 'StorageIcon',
            children: menuContentDynamic,
          });
        }
      }

      return newMenuItems;
    },
    [i18n.language],
  );

  useEffect(() => {
    setMenuItems(processMenuItems(menuContent));
  }, [menuContent, processMenuItems]);

  const { storedValue: activeItem, setValue: updateActiveItem } =
    useLocalStorage('menu_active_route', pathname);

  useEffect(() => {
    const currentValue = storage.get('menu_active_route');
    if (currentValue && currentValue !== activeItem) {
      updateActiveItem(JSON.parse(currentValue));
    }
  }, [activeItem, updateActiveItem]);

  return (
    <List className={clsx(classes.wrapped, `bg-surface`)}>
      <Grid container className={classes.menuList}>
        {menuItems.map((item, index) => (
          <MenuItem
            {...item}
            open={open}
            key={item.code ?? `${item.title}-${index}`}
            updateActiveItem={updateActiveItem}
            activeItem={activeItem}
          />
        ))}
      </Grid>
      <Box>
        <SystemAdmin open={open} />
        {!isMobile && (
          <ZoomButton isZoomedOut={isZoomedOut} onClick={handleZoomClick} />
        )}
      </Box>
    </List>
  );
};

const MemoizedUserNavigation = memo(UserNavigation);
MemoizedUserNavigation.displayName = 'UserNavigation';
export default MemoizedUserNavigation;
