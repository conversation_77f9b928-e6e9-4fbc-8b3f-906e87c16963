import { SvgIconProps } from '@mui/material';

/**
 * Props for Menu item.
 */
type MenuItemProps = {
  /**
   * Menu Code.
   */
  code?: string;
  /**
   * Menu name.
   */
  name: string;
  /**
   * Path to navigate.
   */
  path?: string | string[];
  /**
   * Display icon.
   */
  icon?: React.ReactElement<SvgIconProps>;
  /**
   * Menu dropdown.
   */
  children?: Omit<MenuItemProps, 'children'>[];
  /**
   * Access role per user
   */
  accessEntity?: string;
  isContent: boolean;
};

/**
 * Props for SideBar component.
 */
type SidebarProps = {
  open?: boolean;
  setSidebarOpen?: React.Dispatch<React.SetStateAction<boolean>>;
};

/**
 * Type of BadgeContent of cases
 */
type CaseBadgeContent = {
  all: number;
  approvalPending: number;
  temporary: number;
};

/**
 * Menu object
 * @export
 * @interface Menu
 */
interface Menu {
  /**
   * Menu Code.
   */
  code?: string;
  /**
   *
   * @type {string}
   * @memberof Menu
   */
  title: string;
  /**
   *
   * @type {string}
   * @memberof Menu
   */
  icon?: string;
  icon2?: React.ReactElement<SvgIconProps>;
  /**
   *
   * @type {string}
   * @memberof Menu
   */
  path?: string;
  /**
   *
   * @type {string}
   * @memberof Menu
   */
  access?: string;
  /**
   *
   * @type {boolean}
   * @memberof Menu
   */
  isActive?: boolean;
  /**
   *
   * @type {Array<Menu>}
   * @memberof Menu
   */
  children?: Array<Menu>;
  isContent: boolean;
}

export type { SidebarProps, MenuItemProps, CaseBadgeContent, Menu };
