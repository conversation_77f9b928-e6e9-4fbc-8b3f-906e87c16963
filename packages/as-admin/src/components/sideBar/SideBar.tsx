import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import {
  CardMedia,
  Divider,
  Drawer,
  Theme,
  useMediaQuery,
  useTheme,
  Box,
} from '@mui/material';
import { createStyles, makeStyles } from '@mui/styles';
import clsx from 'clsx';
import React, { useState, useEffect } from 'react';

import { useLayoutStore } from '@/stores/layout.store';

import {
  APP_BAR_HEIGHT,
  MAX_SIDEBAR_WIDTH,
  MIN_SIDEBAR_WIDTH,
} from './constants';
import { SidebarProps } from './types';
import UserNavigation from './UserNavigation';

const useStyles = makeStyles((theme: Theme) =>
  createStyles({
    sidebar: {
      marginTop: APP_BAR_HEIGHT,
      whiteSpace: 'nowrap',
      backgroundColor: '#FFFFFF',
      transition: 'width 500ms ease',
      height: `calc(100vh - ${APP_BAR_HEIGHT}px)`,
      overflow: 'visible',
      '&--open': {
        transition: 'width 500ms ease',
      },
      '&--closed': {
        overflowX: 'hidden',
        [theme.breakpoints.up('sm')]: {
          transition: 'width 500ms ease',
        },
        width: '0',
      },
      '@media (max-width: 600px)': {
        marginTop: '0px',
        height: '100vh',
      },
      zIndex: 999,
    },
    logo: {
      maxWidth: '200px',
      maxHeight: '200px',
      padding: '16px',
    },
    resizer: {
      position: 'absolute',
      right: '-15px',
      top: '50%',
      transform: 'translateY(-50%)',
      width: '14px',
      height: '66px',
      zIndex: 1100,
      cursor: 'pointer',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      borderTop: '14px solid transparent',
      borderBottom: '14px solid transparent',
      borderLeft: '14px solid #B0DAD5',
      pointerEvents: 'auto',
    },
    resizerIcon: {
      position: 'absolute',
      right: 0,
      transform: 'translateY(-50%)',
      top: '50%',
      width: '12px',
      height: '12px',
      color: '#FFFFFF',
    },
  }),
);

export const SideBar: React.FC<SidebarProps> = ({
  open,
  setSidebarOpen,
}: SidebarProps) => {
  const classes = useStyles();
  const theme = useTheme();
  const matchesSM = useMediaQuery(theme.breakpoints.down('sm'));
  const [width, setWidth] = useState(MAX_SIDEBAR_WIDTH);
  const { sidebarOpen, toggleSidebar } = useLayoutStore();

  // Use store state or prop state for backward compatibility
  const isOpen = open !== undefined ? open : sidebarOpen;
  const handleToggle = setSidebarOpen || toggleSidebar;

  const handleResizerClick = () => {
    if (matchesSM) return;
    if (typeof handleToggle === 'function') {
      if (setSidebarOpen) {
        setSidebarOpen((prevState) => !prevState);
      } else {
        toggleSidebar();
      }
    }
    setWidth(
      width === MIN_SIDEBAR_WIDTH ? MAX_SIDEBAR_WIDTH : MIN_SIDEBAR_WIDTH,
    );
  };

  // Listen for zoom events from UserNavigation
  useEffect(() => {
    const handleZoom = (event: Event) => {
      const customEvent = event as CustomEvent;
      setWidth(customEvent.detail.width);
    };

    window.addEventListener('sidebar-zoom', handleZoom);
    return () => {
      window.removeEventListener('sidebar-zoom', handleZoom);
    };
  }, []);

  // Reset width when changing display mode
  useEffect(() => {
    if (matchesSM) {
      setWidth(MAX_SIDEBAR_WIDTH);
    }
  }, [matchesSM]);

  const sidebarWidth = matchesSM ? (isOpen ? MAX_SIDEBAR_WIDTH : 0) : width;

  return (
    <Drawer
      anchor="left"
      variant={matchesSM ? 'temporary' : 'permanent'}
      open={isOpen}
      classes={{
        paper: clsx(classes.sidebar, {
          [`${classes.sidebar}--open`]: matchesSM && isOpen,
          [`${classes.sidebar}--closed`]: matchesSM && !isOpen,
        }),
      }}
      onClose={() => {
        if (setSidebarOpen) {
          setSidebarOpen((prevState) => !prevState);
        } else {
          toggleSidebar();
        }
      }}
      PaperProps={{
        style: { width: sidebarWidth },
      }}
    >
      {matchesSM && (
        <>
          <CardMedia
            component="img"
            image={'TODO'}
            alt="logo"
            style={{ maxWidth: '200px', maxHeight: '200px', padding: '16px' }}
          />
          <Divider />
        </>
      )}
      <UserNavigation
        open={isOpen}
        isMobile={matchesSM}
        setOpen={setSidebarOpen || toggleSidebar}
      />

      {/* Resizer - only show in desktop mode */}
      {!matchesSM && (
        <Box className={classes.resizer} onClick={handleResizerClick}>
          {isOpen ? (
            <ChevronLeftIcon className={classes.resizerIcon} />
          ) : (
            <ChevronRightIcon className={classes.resizerIcon} />
          )}
        </Box>
      )}
    </Drawer>
  );
};
