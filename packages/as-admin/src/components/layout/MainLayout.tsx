import { useTheme, useMediaQuery, Toolbar } from '@mui/material';
import { makeStyles, createStyles } from '@mui/styles';
import clsx from 'clsx';
import { useEffect } from 'react';

import { useLayoutStore } from '@/stores/layout.store';

import { AppBar } from '../appBar';
import { MIN_SIDEBAR_WIDTH, SIDEBAR_WIDTH } from '../sideBar/constants';
import { SideBar } from '../sideBar/SideBar';

type MainLayoutProps = {
  children: React.ReactNode;
};

const useStyles = makeStyles(() =>
  createStyles({
    root: {
      display: 'flex',
      minHeight: 'calc(100vh - 48px)',
    },
    content: {
      transition: 'width 500ms ease-in-out',
    },
    contentOpen: {
      width: `calc(100% - ${SIDEBAR_WIDTH}px)`,
    },
    contentClosed: {
      width: `calc(100% - ${MIN_SIDEBAR_WIDTH}px)`,
    },
    contentSmallScreen: {
      width: '100%',
    },
  }),
);

export const MainLayout: React.FC<MainLayoutProps> = ({
  children,
}: MainLayoutProps) => {
  const classes = useStyles();
  const theme = useTheme();
  const matchesSM = useMediaQuery(theme.breakpoints.down('sm'));
  const { sidebarOpen, setSidebarOpen } = useLayoutStore();

  useEffect(() => {
    if (matchesSM) {
      return;
    }
    setSidebarOpen(!matchesSM);
  }, [matchesSM, setSidebarOpen]);

  return (
    <div className={classes.root}>
      <AppBar />
      <SideBar open={sidebarOpen} />
      <main
        className={clsx(classes.content, {
          [classes.contentSmallScreen]: matchesSM && sidebarOpen,
          [classes.contentOpen]: !matchesSM && sidebarOpen,
          [classes.contentClosed]: !sidebarOpen,
          [classes.contentSmallScreen]: matchesSM,
        })}
      >
        <Toolbar />
        {children}
      </main>
    </div>
  );
};
