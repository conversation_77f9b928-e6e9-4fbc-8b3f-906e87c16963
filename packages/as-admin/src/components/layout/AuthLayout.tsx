import { Container } from '@mui/material';
import { makeStyles, createStyles } from '@mui/styles';

import { AppBar } from '../appBar';

const useStyles = makeStyles(() =>
  createStyles({
    authLayout: {
      display: 'flex',
      flexDirection: 'column',
      width: '100%',
      height: '100vh',
      backgroundColor: '#FFFFFF',
    },
    container: {
      display: 'flex',
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      '& > *:not(:first-child)': {
        marginTop: '48px',
      },
      '&__subtitle': {
        marginTop: '8px',
        fontWeight: 'normal',
      },
    },
  }),
);

/**
 * Props for AuthLayout component.
 */
type AuthLayoutProps = {
  /**
   * The element inside which wrapped by the auth layout.
   */
  children: React.ReactNode;
};

export const AuthLayout: React.FC<AuthLayoutProps> = ({
  children,
}: AuthLayoutProps) => {
  const classes = useStyles();

  return (
    <div className={classes.authLayout}>
      <AppBar />
      <Container className={classes.container}>{children}</Container>
    </div>
  );
};
