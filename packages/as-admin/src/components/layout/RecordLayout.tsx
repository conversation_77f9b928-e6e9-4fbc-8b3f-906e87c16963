import { Paper, Divider, Grid } from '@mui/material';
import { makeStyles, createStyles } from '@mui/styles';
import React from 'react';

const useStyles = makeStyles(() =>
  createStyles({
    root: {
      borderRadius: 5,
      padding: '16px',
      '& > *:not(:first-child)': {
        marginTop: '16px',
      },
    },
  }),
);

type RecordLayoutProps = {
  title: string | React.ReactElement;
  children: React.ReactElement;
  footer?: React.ReactElement;
};

export const RecordLayout: React.FC<RecordLayoutProps> = ({
  title,
  children,
  footer,
}: RecordLayoutProps) => {
  const classes = useStyles();

  return (
    <Paper elevation={0} className={classes.root}>
      <Grid item container>
        {title}
      </Grid>
      <Divider />
      {children}
      {footer && (
        <>
          <Divider />
          <Grid item container>
            {footer}
          </Grid>
        </>
      )}
    </Paper>
  );
};
