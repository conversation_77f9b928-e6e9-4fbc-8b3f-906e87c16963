import {
  KeyboardArrowDown as ArrowDownIcon,
  KeyboardArrowUp as ArrowUpIcon,
  ArrowForward as ArrowForwardIcon,
} from '@mui/icons-material';
import { Stack, Typography } from '@mui/material';
import { Column } from '@tanstack/react-table';

interface DataTableColumnHeaderProps<TData, TValue> {
  column: Column<TData, TValue>;
  title: string;
  className?: string;
  isMultiSort?: boolean;
}

export const DataTableColumnHeader = <TData, TValue>({
  column,
  title,
  className,
  isMultiSort,
}: DataTableColumnHeaderProps<TData, TValue>) => {
  const canSort = column.getCanSort();
  const isSorted = column.getIsSorted();

  const handleSort = () => {
    column.toggleSorting(isSorted === 'asc', !!isMultiSort);
  };

  const renderSortIcon = () => {
    if (isSorted === 'desc') return <ArrowDownIcon />;
    if (isSorted === 'asc') return <ArrowUpIcon />;
    return <ArrowForwardIcon />;
  };

  return canSort ? (
    <Stack
      direction="row"
      spacing={1}
      onClick={handleSort}
      sx={{
        cursor: 'pointer',
        alignItems: 'center',
      }}
      className={className}
    >
      <Typography>{title}</Typography>
      {renderSortIcon()}
    </Stack>
  ) : (
    <Typography className={className}>{title}</Typography>
  );
};
