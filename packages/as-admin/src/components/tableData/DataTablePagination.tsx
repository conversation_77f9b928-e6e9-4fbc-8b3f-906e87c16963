import { Box, Pagination, Stack } from '@mui/material';

export interface PaginationFilter {
  page: number;
  limit: number;
  total?: number;
}

interface DataTablePaginationProps<TFilter extends PaginationFilter> {
  onPageChange: (page: number) => void;
  pagination: TFilter;
}

export const DataTablePagination = <TFilter extends PaginationFilter>({
  onPageChange,
  pagination,
}: DataTablePaginationProps<TFilter>) => {
  const { limit, page, total } = pagination;
  const totalPages = total && limit ? Math.ceil(total / limit) : 1;

  const handlePageChange = (
    _event: React.ChangeEvent<unknown>,
    value: number,
  ) => {
    onPageChange(value);
  };

  return (
    <Box className="!pt-4">
      <Stack direction="row" justifyContent="center" alignItems="center">
        <Pagination
          page={page}
          count={totalPages}
          onChange={handlePageChange}
          shape="rounded"
          color="primary"
          siblingCount={1}
          boundaryCount={1}
          sx={{
            '& .MuiPaginationItem-root': {
              color: '#151D1D',
            },
            '& .MuiPaginationItem-root.Mui-selected': {
              color: 'white',
              fontWeight: 'bold',
            },
            '& .MuiPaginationItem-root.Mui-selected:hover': {
              backgroundColor: '#006a6a',
            },
          }}
        />
      </Stack>
    </Box>
  );
};
