import { Search as SearchIcon } from '@mui/icons-material';
import { Button, Stack } from '@mui/material';
import React, { ReactNode, useEffect, useState } from 'react';

import { GenericRequest } from '@/utils/types';

interface DataTableFilterProps<T extends GenericRequest> {
  initialFilter: T;
  onFilter: (filter: T) => void;
  children: (
    filter: T,
    setFilter: React.Dispatch<React.SetStateAction<T>>,
  ) => ReactNode;
  isSearch?: boolean;
}

export const DataTableFilter = <T extends GenericRequest>({
  initialFilter,
  onFilter,
  children,
  isSearch,
}: DataTableFilterProps<T>) => {
  const [filter, setFilter] = useState<T>(initialFilter);

  useEffect(() => {
    if (!isSearch) {
      onFilter(filter);
    }
  }, [filter, isSearch, onFilter]);

  const handleSearch = () => {
    onFilter(filter);
  };

  return (
    <Stack direction="row" spacing={2} alignItems="flex-start">
      {children(filter, setFilter)}
      {isSearch ?? (
        <Button
          variant="contained"
          color="primary"
          startIcon={<SearchIcon />}
          onClick={handleSearch}
        >
          {'検索'}
        </Button>
      )}
    </Stack>
  );
};
