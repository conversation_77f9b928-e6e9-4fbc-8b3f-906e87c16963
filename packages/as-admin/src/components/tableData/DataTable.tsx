import {
  Box,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  CircularProgress,
  Stack,
  TableContainer,
  Paper,
  TableSortLabel,
  Typography,
} from '@mui/material';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  RowSelectionState,
  useReactTable,
  SortingState,
} from '@tanstack/react-table';
import React, { useEffect, useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Pagination } from '@/bundles/model';
import { GenericRequest } from '@/utils/types';

import { DataTableFilter } from './DataTableFilter';
import { DataTablePagination } from './DataTablePagination';

interface BaseData {
  id: string | number;
}

export interface DataTableProps<TData extends BaseData, TFilter> {
  columns: ColumnDef<TData>[];
  data?: TData[];
  pagination?: Pagination;
  isLoading?: boolean;
  initialFilter: TFilter;
  onRowClick?: (row: TData) => void;
  onPageChange?: (page: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
  onSortChange?: (sorting: SortingState) => void;
  renderFilter?: (
    filter: TFilter,
    setFilter: React.Dispatch<React.SetStateAction<TFilter>>,
  ) => React.ReactElement;
  transformData?: (data: TData[]) => TData[];
  messageEmpty: string;
  isSearch?: boolean;
  onRowSelectionChange?: (selectedRows: TData[]) => void;
  selectedRowId?: string | number;
  headerBackgroundColor?: string;
  rowColors?: {
    odd: string;
    even: string;
  };
  justifyContentHeader?: 'center' | 'start' | 'end';
  containerBackgroundColor?: string;
}

interface GenericFilter extends GenericRequest {
  sorting?: SortingState;
}

export function DataTable<
  TData extends BaseData,
  TFilter extends GenericFilter,
>({
  columns,
  data = [],
  pagination,
  isLoading = false,
  initialFilter,
  onRowClick,
  onPageChange,
  onPageSizeChange,
  onSortChange,
  renderFilter,
  transformData,
  messageEmpty,
  isSearch,
  onRowSelectionChange,
  selectedRowId,
  headerBackgroundColor,
  rowColors,
  justifyContentHeader = 'start',
  containerBackgroundColor,
}: DataTableProps<TData, TFilter>) {
  const { t } = useTranslation();
  const [filter, setFilter] = useState<TFilter>(initialFilter);
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  // Initialize sorting from filter
  const getInitialSorting = (): SortingState => {
    if (initialFilter.sorting) {
      return initialFilter.sorting;
    }
    // Check if filter has orderBy/sortBy (for backwards compatibility)
    const filterAny = initialFilter as any;
    if (filterAny.sortBy && filterAny.orderBy) {
      const columnId = filterAny.sortBy === 'status' ? 'status' : 'createdAt';
      const desc = filterAny.orderBy === 'desc';
      return [{ id: columnId, desc }];
    }
    return [];
  };

  const [sorting, setSorting] = useState<SortingState>(getInitialSorting());

  useEffect(() => {
    setFilter(initialFilter);
  }, [initialFilter]);

  useEffect(() => {
    if (pagination) {
      setFilter((prev) => ({
        ...prev,
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          total: pagination.totalCount,
        },
      }));
    }
  }, [pagination]);

  // Transform data if transformData function is provided
  const displayData = useMemo(() => {
    return transformData ? transformData(data) : data;
  }, [data, transformData]);

  const table = useReactTable({
    manualPagination: true,
    data: displayData,
    columns,
    defaultColumn: {
      minSize: 0,
      size: Number.MAX_SAFE_INTEGER,
      maxSize: Number.MAX_SAFE_INTEGER,
    },
    rowCount: pagination?.totalCount || 0,
    state: {
      pagination: {
        pageIndex: (filter.pagination?.page || 1) - 1,
        pageSize: filter.pagination?.limit || 10,
      },
      sorting,
      rowSelection,
    },
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onPaginationChange: (updater) => {
      if (typeof updater !== 'function') return;
      const newPageInfo = updater(table.getState().pagination);
      const newPage = newPageInfo.pageIndex + 1;
      const newPageSize = newPageInfo.pageSize;

      setFilter((prev) => ({
        ...prev,
        pagination: {
          page: newPage,
          limit: newPageSize,
        },
      }));

      if (onPageChange && newPage !== (filter.pagination?.page || 1)) {
        onPageChange(newPage);
      }
      if (
        onPageSizeChange &&
        newPageSize !== (filter.pagination?.limit || 10)
      ) {
        onPageSizeChange(newPageSize);
      }
    },
    onSortingChange: (updater) => {
      if (typeof updater !== 'function') return;
      const newSortInfo = updater(table.getState().sorting);
      setSorting(newSortInfo);

      // Update filter with new sorting
      setFilter((prev) => ({
        ...prev,
        sorting: newSortInfo,
        pagination: {
          ...prev.pagination,
          page: 1,
        },
      }));

      if (onSortChange) {
        onSortChange(newSortInfo);
      }
    },
    onRowSelectionChange: setRowSelection,
    getPaginationRowModel: getPaginationRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    enableSorting: true,
    enableSortingRemoval: false,
  });

  useEffect(() => {
    const selectedRows = table
      .getSelectedRowModel()
      .rows.map((row) => row.original);
    if (onRowSelectionChange) {
      onRowSelectionChange(selectedRows);
    }
  }, [rowSelection, onRowSelectionChange, table]);

  // Check if all headers are null/undefined to determine if we should show TableHead
  const hasVisibleHeaders = useMemo(() => {
    return table.getHeaderGroups().some((headerGroup) =>
      headerGroup.headers.some((header) => {
        if (header.isPlaceholder) return false;
        const headerContent = header.column.columnDef.header;
        // Check if header is not null/undefined and not a function that returns null
        if (typeof headerContent === 'function') {
          return headerContent(header.getContext()) !== null;
        }
        return headerContent !== null && headerContent !== undefined;
      }),
    );
  }, [table]);

  return (
    <Stack direction="column">
      {renderFilter && (
        <DataTableFilter
          initialFilter={filter}
          onFilter={setFilter}
          isSearch={isSearch}
        >
          {() => {
            return renderFilter(filter, setFilter);
          }}
        </DataTableFilter>
      )}
      <Paper elevation={0} className={`py-6 ${containerBackgroundColor}`}>
        <TableContainer>
          <Table>
            {hasVisibleHeaders && (
              <TableHead>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => (
                      <TableCell
                        key={header.id}
                        colSpan={header.colSpan}
                        className={`!text-onSurface !pl-0 ${
                          headerBackgroundColor
                            ? 'bg-surfaceContainerHighest'
                            : ''
                        }`}
                        sx={{
                          width:
                            header.getSize() === Number.MAX_SAFE_INTEGER
                              ? 'auto'
                              : header.getSize(),
                          height: '50px',
                          padding: '0 16px',
                        }}
                      >
                        {header.isPlaceholder ? null : (
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: justifyContentHeader,
                              cursor: header.column.getCanSort()
                                ? 'pointer'
                                : 'default',
                            }}
                            onClick={header.column.getToggleSortingHandler()}
                          >
                            {flexRender(
                              header.column.columnDef.header,
                              header.getContext(),
                            )}
                            {header.column.getCanSort() && (
                              <TableSortLabel
                                active={!!header.column.getIsSorted()}
                                direction={
                                  header.column.getIsSorted() || undefined
                                }
                              />
                            )}
                          </Box>
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableHead>
            )}
            <TableBody>
              {!isLoading ? (
                <>
                  {table.getRowModel().rows.length ? (
                    table.getRowModel().rows.map((row, index) => (
                      <TableRow
                        key={`${index}-${row.id}`}
                        data-state={row.getIsSelected() && 'selected'}
                        onClick={() => onRowClick?.(row.original)}
                        className={`${
                          selectedRowId === row.original.id.toString()
                            ? '#D1D1D1'
                            : rowColors
                              ? index % 2 === 0
                                ? rowColors.even
                                : rowColors.odd
                              : 'inherit'
                        }`}
                        sx={{
                          cursor: onRowClick ? 'pointer' : 'default',
                          '&:hover': onRowClick ? { bgcolor: '#D1D1D1' } : {},
                          '&.Mui-selected': {
                            bgcolor: 'primary.light',
                          },
                          height: '50px',
                        }}
                      >
                        {row.getVisibleCells().map((cell) => (
                          <TableCell
                            key={cell.id}
                            className="!text-onSurface !pl-0"
                            sx={{
                              width:
                                cell.column.columnDef.size ===
                                Number.MAX_SAFE_INTEGER
                                  ? 'auto'
                                  : cell.column.columnDef.size,
                              height: '50px',
                              padding: '0 16px',
                              whiteSpace: 'nowrap',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                            }}
                          >
                            {flexRender(
                              cell.column.columnDef.cell,
                              cell.getContext(),
                            )}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell
                        colSpan={columns.length}
                        align="left"
                        className="!px-0 !border-none"
                      >
                        <Typography variant="h2" className="!text-tertiary">
                          {messageEmpty || t('components.table.empty')}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </>
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} align="center">
                    <CircularProgress size={40} />
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
        {!isLoading && table.getRowModel().rows.length > 0 && (
          <DataTablePagination
            onPageChange={(page) => {
              table.setPageIndex(page - 1);
            }}
            pagination={filter.pagination}
          />
        )}
      </Paper>
    </Stack>
  );
}
