import { Box, TextField, TextFieldProps, Typography } from '@mui/material';
import React from 'react';

interface FilterInputProps extends Omit<TextFieldProps, 'variant'> {
  label?: string;
  placeholder?: string;
}

const FilterInput: React.FC<FilterInputProps> = ({
  label = 'Label',
  placeholder = 'Enter something...',
  ...props
}) => {
  return (
    <Box sx={{ mx: 1 }}>
      <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
        {label}
      </Typography>
      <TextField
        size="small"
        variant="outlined"
        placeholder={placeholder}
        {...props}
        sx={{
          '& .MuiOutlinedInput-root': {
            '&.Mui-focused fieldset': {
              borderColor: 'primary.main',
            },
          },
          ...props.sx,
        }}
      />
    </Box>
  );
};

export default FilterInput;
