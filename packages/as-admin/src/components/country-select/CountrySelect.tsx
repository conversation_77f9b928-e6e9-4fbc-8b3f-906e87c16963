import PublicOutlinedIcon from '@mui/icons-material/PublicOutlined';
import {
  Box,
  TextField,
  MenuItem,
  TextFieldProps,
  Typography,
} from '@mui/material';
import { forwardRef } from 'react';

import { getAllowedCountries } from '@/utils/country';

interface CountryOption {
  key: string;
  value: string;
  flag: string | React.ReactNode;
  name: string;
}

interface CountrySelectProps extends Omit<TextFieldProps, 'children'> {
  label?: string;
  countries?: CountryOption[];
  isAll?: boolean;
}

// Generate countries list from allowed countries
const defaultCountries: CountryOption[] = getAllowedCountries().map(
  (country) => {
    return {
      key: country.code,
      value: country.code,
      flag: country.flag,
      name: country.name,
    };
  },
);

export const CountrySelect = forwardRef<HTMLDivElement, CountrySelectProps>(
  (
    {
      label,
      countries = defaultCountries,
      isAll = false,
      value,
      onChange,
      ...props
    },
    ref,
  ) => {
    // Add "選択してください" option at the beginning if isAll is true
    const finalCountries = isAll
      ? [
          {
            key: '選択してください',
            value: '',
            flag: <PublicOutlinedIcon />,
            name: '選択してください',
          },
          ...countries,
        ]
      : countries;

    return (
      <TextField
        ref={ref}
        select
        fullWidth
        label={label}
        value={value}
        onChange={onChange}
        {...props}
        sx={{
          '& .MuiSelect-select': {
            padding: '13px 14px',
          },
          ...props.sx,
        }}
        SelectProps={{
          displayEmpty: true,
          renderValue: (selected: unknown) => {
            const country = finalCountries.find((c) => c.value === selected);

            if (!selected || selected === '' || !country) {
              return (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <PublicOutlinedIcon />
                  <Typography
                    sx={{
                      color: '#9CA3AF',
                      fontSize: '14px',
                      lineHeight: '22px',
                      fontWeight: '400',
                    }}
                  >
                    選択してください
                  </Typography>
                </Box>
              );
            }

            return (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {typeof country.flag === 'string' ? (
                  <span style={{ fontSize: '35px' }}>{country.flag}</span>
                ) : (
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {country.flag}
                  </Box>
                )}
                <Typography>{country.name}</Typography>
              </Box>
            );
          },
        }}
      >
        {finalCountries.map((country) => {
          return (
            <MenuItem
              key={`${country.value || 'select'}-${country.key}`}
              value={country.value}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {typeof country.flag === 'string' ? (
                  <span style={{ fontSize: '35px' }}>{country.flag}</span>
                ) : (
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {country.flag}
                  </Box>
                )}
                <Typography>{country.name}</Typography>
              </Box>
            </MenuItem>
          );
        })}
      </TextField>
    );
  },
);

CountrySelect.displayName = 'CountrySelect';
