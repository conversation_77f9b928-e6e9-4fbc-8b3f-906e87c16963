import { Avatar } from '@mui/material';

import { getCountryInfo } from '@/utils/country';

export const CountryFlag = ({ countryCode }: { countryCode?: string }) => {
  if (!countryCode) return null;
  const countryInfo = getCountryInfo(countryCode);

  return (
    <Avatar
      src={
        countryInfo?.code
          ? `https://flagicons.lipis.dev/flags/4x3/${countryInfo?.code?.toLowerCase()}.svg`
          : ''
      }
      className="!w-8 !h-6 !border !border-gray-300"
      variant="square"
    />
  );
};
