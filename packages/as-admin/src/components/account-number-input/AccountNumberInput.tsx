import { AccountBalanceOutlined } from '@mui/icons-material';
import {
  Box,
  Divider,
  Select,
  MenuItem,
  FormControl,
  Typography,
} from '@mui/material';
import { forwardRef } from 'react';

import { InputTextField } from '@/components/input-text/InputTextField';

interface AccountNumberInputProps {
  label?: string;
  countryCode?: string;
  accountType?: string;
  onAccountTypeChange?: (accountType: string) => void;
  value?: string;
  onChange: (value?: string) => void;
  onClear?: () => void;
}

const accountTypes = [
  { value: 'ordinary', label: '普通預金' },
  { value: 'current', label: '当座預金' },
  { value: 'savings', label: '貯蓄預金' },
];

export const AccountNumberInput = forwardRef<
  HTMLInputElement,
  AccountNumberInputProps
>(
  (
    {
      label,
      countryCode = 'JP',
      accountType = 'ordinary',
      onAccountTypeChange,
      value,
      onChange,
      onClear,
      ...props
    },
    ref,
  ) => {
    const isJapan = countryCode === 'JP';

    const handleAccountTypeChange = (newAccountType: string) => {
      onAccountTypeChange?.(newAccountType);
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      // Only allow numbers
      const numericValue = e.target.value.replace(/[^0-9]/g, '');
      onChange(numericValue);
    };

    const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
      // Prevent non-numeric characters except control keys
      if (
        !/[0-9]/.test(e.key) &&
        ![
          'Backspace',
          'Delete',
          'Tab',
          'Escape',
          'Enter',
          'ArrowLeft',
          'ArrowRight',
        ].includes(e.key)
      ) {
        e.preventDefault();
      }
    };

    const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
      // Only allow pasting numeric content
      const pasteData = e.clipboardData.getData('text');
      if (!/^[0-9]*$/.test(pasteData)) {
        e.preventDefault();
      }
    };

    const handleClear = () => {
      onChange(undefined);
      onClear?.();
    };

    // Create account type selector for startAdornment (only for Japan)
    const accountTypeSelector = isJapan ? (
      <Box
        sx={{ display: 'flex', alignItems: 'center', gap: 1, mr: 1 }}
        className="flex items-center"
      >
        <AccountBalanceOutlined className="text-gray-600" />

        {/* Account Type Selector */}
        <FormControl size="small" sx={{ minWidth: 90, paddingLeft: '8px' }}>
          <Select
            value={accountType}
            onChange={(e) => handleAccountTypeChange(e.target.value)}
            variant="standard"
            disableUnderline
            sx={{
              '& .MuiSelect-select': {
                padding: '0 !important',
                fontSize: '14px',
                minHeight: 'unset',
                border: 'none',
                '&:focus': {
                  backgroundColor: 'transparent',
                },
              },
            }}
            renderValue={(selected) => {
              const selectedType = accountTypes.find(
                (type) => type.value === selected,
              );
              return (
                <Typography variant="body2">
                  {selectedType?.label || '普通預金'}
                </Typography>
              );
            }}
          >
            {accountTypes.map((type) => (
              <MenuItem key={type.value} value={type.value}>
                <Typography variant="body2">{type.label}</Typography>
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <Box className="flex !h-6 !pl-1">
          <Divider orientation="vertical" flexItem />
        </Box>
      </Box>
    ) : (
      <AccountBalanceOutlined className="text-gray-600" />
    );

    return (
      <Box sx={{ paddingLeft: '8px' }}>
        <InputTextField
          ref={ref}
          label={label}
          value={value || ''}
          onChange={handleInputChange}
          onKeyPress={handleKeyPress}
          onPaste={handlePaste}
          startIcon={accountTypeSelector}
          hasValue={!!value}
          onClear={handleClear}
          placeholder="例: 1234567"
          type="tel"
          className="!w-full"
          inputMode="numeric"
          sx={{
            '& .MuiOutlinedInput-root input[type=tel]': {
              '&::-webkit-outer-spin-button': { display: 'none' },
              '&::-webkit-inner-spin-button': { display: 'none' },
              MozAppearance: 'textfield',
            },
          }}
          {...props}
        />
      </Box>
    );
  },
);

AccountNumberInput.displayName = 'AccountNumberInput';
