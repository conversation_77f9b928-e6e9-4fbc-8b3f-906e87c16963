import { Box, CircularProgress, Typography } from '@mui/material';
import React from 'react';

export interface LoadingIndicatorProps {
  /**
   * Status loading
   */
  isLoading: boolean;
  /**
   * Content to display when not loading
   */
  children?: React.ReactNode;
  /**
   * Size of CircularProgress
   */
  size?: number;
  /**
   * Color of CircularProgress
   */
  color?:
    | 'primary'
    | 'secondary'
    | 'error'
    | 'info'
    | 'success'
    | 'warning'
    | 'inherit';
  /**
   * Show full screen with overlay on children
   */
  fullscreen?: boolean;
  /**
   * Custom class container
   */
  className?: string;
  /**
   * Text to display when loading
   */
  loadingText?: string;
}

export const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({
  isLoading,
  children,
  size = 40,
  color = 'primary',
  fullscreen = false,
  className = '',
  loadingText = '',
}) => {
  // If fullscreen mode
  if (fullscreen) {
    if (isLoading) {
      return (
        <Box className={`relative ${className}`}>
          {children}
          <Box
            className="fixed inset-0 flex flex-col items-center justify-center"
            sx={{
              zIndex: 9999,
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
            }}
          >
            <CircularProgress size={size} color={color} />
            {loadingText && (
              <Typography className="mt-2 text-white">{loadingText}</Typography>
            )}
          </Box>
        </Box>
      );
    } else {
      return <>{children}</>;
    }
  }

  // If not fullscreen mode, just show children
  if (!isLoading && children) {
    return <>{children}</>;
  }

  // Show only loading
  return (
    <Box
      className={`flex flex-col items-center justify-center p-4 ${className}`}
    >
      <CircularProgress size={size} color={color} />
      {loadingText && (
        <Typography className="mt-2 text-gray-700">{loadingText}</Typography>
      )}
    </Box>
  );
};
