import CheckIcon from '@mui/icons-material/Check';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import ErrorIcon from '@mui/icons-material/Error';
import { IconButton, Tooltip, Zoom, Fade } from '@mui/material';
import React, { useState, useCallback } from 'react';

interface AnimatedCopyButtonProps {
  value: string;
  size?: 'small' | 'medium' | 'large';
  className?: string;
  color?:
    | 'inherit'
    | 'default'
    | 'primary'
    | 'secondary'
    | 'error'
    | 'info'
    | 'success'
    | 'warning';
  sx?: object;
  disabled?: boolean;
  feedbackDuration?: number;
  onCopy?: () => void;
  onError?: () => void;
  tooltipPlacement?: 'top' | 'bottom' | 'left' | 'right';
}

type CopyState = 'idle' | 'success' | 'error';

export const AnimatedCopyButton: React.FC<AnimatedCopyButtonProps> = ({
  value,
  size = 'medium',
  className,
  color = 'inherit',
  sx,
  disabled = false,
  feedbackDuration = 1000,
  onCopy,
  onError,
  tooltipPlacement = 'top',
}) => {
  const [copyState, setCopyState] = useState<CopyState>('idle');

  const handleCopy = useCallback(
    async (event: React.MouseEvent) => {
      event.stopPropagation();

      if (disabled || !value) return;

      try {
        await navigator.clipboard.writeText(value);
        setCopyState('success');
        onCopy?.();

        setTimeout(() => {
          setCopyState('idle');
        }, feedbackDuration);
      } catch (error) {
        setCopyState('error');
        onError?.();

        setTimeout(() => {
          setCopyState('idle');
        }, feedbackDuration);
      }
    },
    [value, disabled, feedbackDuration, onCopy, onError],
  );

  const getIcon = () => {
    switch (copyState) {
      case 'success':
        return (
          <Zoom in={true} timeout={200}>
            <CheckIcon
              fontSize={size === 'small' ? 'small' : 'inherit'}
              className="!fill-primary"
            />
          </Zoom>
        );
      case 'error':
        return (
          <Zoom in={true} timeout={200}>
            <ErrorIcon
              fontSize={size === 'small' ? 'small' : 'inherit'}
              className="!fill-error"
            />
          </Zoom>
        );
      default:
        return (
          <Fade in={true} timeout={200}>
            <ContentCopyIcon sx={{ fontSize: 14 }} className="!fill-primary" />
          </Fade>
        );
    }
  };

  const getTooltipTitle = () => {
    switch (copyState) {
      case 'success':
        return 'コピーしました！';
      case 'error':
        return 'コピーに失敗しました';
      default:
        return '';
    }
  };

  const getIconColor = () => {
    switch (copyState) {
      case 'success':
        return 'success';
      case 'error':
        return 'error';
      default:
        return color;
    }
  };

  const shouldShowTooltip = copyState !== 'idle';

  return (
    <Tooltip
      title={getTooltipTitle()}
      placement={tooltipPlacement}
      arrow
      open={shouldShowTooltip}
      disableHoverListener={true}
      disableFocusListener={true}
      disableTouchListener={true}
    >
      <IconButton
        onClick={handleCopy}
        disabled={disabled}
        size={size}
        className={className}
        color={getIconColor() as any}
        sx={{
          transition: 'all 0.2s ease-in-out',
          transform: copyState !== 'idle' ? 'scale(1.1)' : 'scale(1)',
          ...sx,
        }}
      >
        {getIcon()}
      </IconButton>
    </Tooltip>
  );
};
