import MenuIcon from '@mui/icons-material/Menu';
import NotificationsIcon from '@mui/icons-material/Notifications';
import {
  AppBar as MuiAppBar,
  Avatar,
  Box,
  Button,
  CardMedia,
  Divider,
  IconButton,
  Typography,
  Toolbar,
  useMediaQuery,
  useTheme,
  Badge,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { useAuth } from '@/providers/auth';
import { routes } from '@/routes/routes';
import { useLayoutStore } from '@/stores/layout.store';

export const AppBar: React.FC = () => {
  const theme = useTheme();
  const matchesSM = useMediaQuery(theme.breakpoints.down('sm'));
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { authUser, logoutFn } = useAuth();
  const { toggleSidebar } = useLayoutStore();

  const onClickMenuIcon = () => {
    toggleSidebar();
  };

  const handleLogout = () => {
    logoutFn();
  };

  const hasNewNotifications = true;

  return (
    <Box sx={{ flexGrow: authUser ? 1 : null }}>
      <MuiAppBar
        className="flex justify-between !shadow-none h-auto z-999"
        component="nav"
      >
        <Toolbar>
          <Box className="flex items-center justify-start w-1/2">
            {!matchesSM && (
              <CardMedia
                component="img"
                image={'TODO'}
                alt="logo"
                className="max-w-[200px] max-h-[200px]"
                onClick={() => navigate(`${routes.home.wildcard}`)}
              />
            )}
            {authUser && matchesSM && (
              <IconButton
                size="large"
                edge="start"
                color="inherit"
                aria-label="open sidebar"
                onClick={onClickMenuIcon}
                sx={{ ml: matchesSM ? 0 : 2 }}
              >
                <MenuIcon />
              </IconButton>
            )}
            <Typography
              variant="h3"
              noWrap
              component="div"
              color="text.primary"
            >
              ServiceTitle
            </Typography>
            <Typography
              variant="body1"
              noWrap
              component="div"
              color="text.primary"
              sx={{ ml: 1 }}
            >
              SubName
            </Typography>
          </Box>
          {authUser && (
            <Box className="flex items-center justify-end w-1/2">
              <Box className="flex items-center justify-end gap-4">
                <IconButton aria-label="notifications" size="small">
                  <Badge
                    variant="dot"
                    overlap="circular"
                    color="error"
                    invisible={!hasNewNotifications}
                    sx={{
                      '& .MuiBadge-badge': {
                        backgroundColor: '#BA1A1A',
                        width: '8px',
                        height: '8px',
                        borderRadius: '100%',
                        right: 2,
                        top: 2,
                      },
                    }}
                  >
                    <NotificationsIcon className="!text-[#3A4A49]" />
                  </Badge>
                </IconButton>
                <Divider
                  orientation="vertical"
                  flexItem
                  className="h-6 my-[10px] mx-2"
                />
                <Avatar
                  alt={authUser?.name || 'User'}
                  sx={{ width: 20, height: 20 }}
                />
                <Typography variant="h5">
                  #{authUser?.name || 'UserName'}
                </Typography>
                <Button
                  variant="outlined"
                  onClick={handleLogout}
                  className="h-8"
                >
                  <Typography
                    variant="h5"
                    noWrap
                    component="div"
                    className="!text-primary"
                  >
                    {t('auth.logOut') || 'Logout'}
                  </Typography>
                </Button>
              </Box>
            </Box>
          )}
        </Toolbar>
      </MuiAppBar>
    </Box>
  );
};
