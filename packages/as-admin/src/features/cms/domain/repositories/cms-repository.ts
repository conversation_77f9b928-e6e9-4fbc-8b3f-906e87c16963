import { AxiosRequestConfig } from 'axios';

import { Content, ContentCategory, Language } from '@/bundles/model';

import { ContentSearchQuery } from '../../ui/types';

export interface CmsRepository {
  createContent(
    content: Content,
    option?: AxiosRequestConfig,
  ): Promise<Content>;
  deleteContent(id: string, option?: AxiosRequestConfig): Promise<void>;
  getContentById(id: string, option?: AxiosRequestConfig): Promise<Content>;
  getContentCategories(option?: AxiosRequestConfig): Promise<ContentCategory[]>;
  getContents(
    searchQuery: ContentSearchQuery,
    option?: AxiosRequestConfig,
  ): Promise<Content[]>;
  updateContent(
    id: string,
    content: Content,
    option?: AxiosRequestConfig,
  ): Promise<void>;
  getLanguages(option?: AxiosRequestConfig): Promise<Language[]>;
}
