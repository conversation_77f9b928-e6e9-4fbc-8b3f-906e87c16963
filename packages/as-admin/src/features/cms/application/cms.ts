import { AxiosRequestConfig } from 'axios';

import { Content, ContentCategory, Language } from '@/bundles/model';

import { CmsRepository } from '../domain/repositories/cms-repository';
import { ContentSearchQuery } from '../ui/types';

const createContent =
  (cmsRepository: CmsRepository) =>
  async (content: Content, option?: AxiosRequestConfig): Promise<Content> => {
    return await cmsRepository.createContent(content, option);
  };

const updateContent =
  (cmsRepository: CmsRepository) =>
  async (
    id: string,
    content: Content,
    option?: AxiosRequestConfig,
  ): Promise<void> => {
    return await cmsRepository.updateContent(id, content, option);
  };

const deleteContent =
  (cmsRepository: CmsRepository) =>
  async (id: string, option?: AxiosRequestConfig): Promise<void> => {
    return await cmsRepository.deleteContent(id, option);
  };

const getContentById =
  (cmsRepository: CmsRepository) =>
  async (id: string, option?: AxiosRequestConfig): Promise<Content> => {
    return await cmsRepository.getContentById(id, option);
  };

const getContentCategories =
  (cmsRepository: CmsRepository) =>
  async (option?: AxiosRequestConfig): Promise<ContentCategory[]> => {
    return await cmsRepository.getContentCategories(option);
  };

const getContents =
  (cmsRepository: CmsRepository) =>
  async (
    searchQuery: ContentSearchQuery,
    option?: AxiosRequestConfig,
  ): Promise<Content[]> => {
    return await cmsRepository.getContents(searchQuery, option);
  };

const getLanguages =
  (cmsRepository: CmsRepository) =>
  async (option?: AxiosRequestConfig): Promise<Language[]> => {
    return await cmsRepository.getLanguages(option);
  };

export {
  createContent,
  deleteContent,
  getContentById,
  getContentCategories,
  getContents,
  getLanguages,
  updateContent,
};
