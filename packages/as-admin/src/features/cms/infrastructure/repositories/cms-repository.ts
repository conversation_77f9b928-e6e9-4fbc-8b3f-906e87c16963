import { AxiosRequestConfig } from 'axios';

import { Content } from '@/bundles/model';

import { CmsRepository } from '../../domain/repositories/cms-repository';
import { ContentSearchQuery } from '../../ui/types';
import {
  createContent,
  deleteContent,
  getContentById,
  getContentCategories,
  getContents,
  getLanguages,
  updateContent,
} from '../api/cms-api';

export const cmsRepository = (): CmsRepository => {
  return {
    createContent: async (payload: Content, option?: AxiosRequestConfig) => {
      return await createContent(payload, option);
    },
    deleteContent: async (id: string, option?: AxiosRequestConfig) => {
      return await deleteContent(id, option);
    },
    getContentById: async (id: string, option?: AxiosRequestConfig) => {
      return await getContentById(id, option);
    },
    getContentCategories: async (option?: AxiosRequestConfig) => {
      return await getContentCategories(option);
    },
    getContents: async (
      searchQuery: ContentSearchQuery,
      option?: AxiosRequestConfig,
    ) => {
      return await getContents(searchQuery, option);
    },
    updateContent: async (
      id: string,
      payload: Content,
      option?: AxiosRequestConfig,
    ) => {
      return await updateContent(id, payload, option);
    },
    getLanguages: async (option?: AxiosRequestConfig) => {
      return await getLanguages(option);
    },
  };
};
