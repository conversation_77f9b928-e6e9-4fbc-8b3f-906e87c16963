import { useMutation } from '@tanstack/react-query';
import { AxiosError, AxiosRequestConfig } from 'axios';

import { Language } from '@/bundles/model';

import { getLanguages as getLanguagesUseCase } from '../../application/cms';
import { cmsRepository } from '../../infrastructure/repositories/cms-repository';

export const useGetLanguages = () => {
  return useMutation<Language[], AxiosError, AxiosRequestConfig | undefined>({
    mutationFn: (option?: AxiosRequestConfig) =>
      getLanguagesUseCase(cmsRepository())(option),

    onSuccess: async (response) => {
      if (response) {
        return response;
      }

      return [];
    },

    onError: () => {
      return [];
    },
  });
};
