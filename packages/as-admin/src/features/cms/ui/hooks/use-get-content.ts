import { useMutation } from '@tanstack/react-query';
import { AxiosError, AxiosRequestConfig } from 'axios';

import { Content } from '@/bundles/model';

import { getContentById as getContentByIdUseCase } from '../../application/cms';
import { cmsRepository } from '../../infrastructure/repositories/cms-repository';

export const useGetContent = () => {
  return useMutation<Content, AxiosError, string, AxiosRequestConfig>({
    mutationFn: (id: string, option?: AxiosRequestConfig) =>
      getContentByIdUseCase(cmsRepository())(id, option),

    onSuccess: async (response) => {
      console.log('Get content by id success:', response);
    },

    onError: (error) => {
      console.error('Get content by id failed:', error);
    },
  });
};
