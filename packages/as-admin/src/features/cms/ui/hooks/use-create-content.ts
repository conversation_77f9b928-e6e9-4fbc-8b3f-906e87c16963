import { useMutation } from '@tanstack/react-query';
import { AxiosError, AxiosRequestConfig } from 'axios';

import { Content } from '@/bundles/model';

import { createContent as createContentUseCase } from '../../application/cms';
import { cmsRepository } from '../../infrastructure/repositories/cms-repository';

export const useCreateContent = () => {
  return useMutation<Content, AxiosError, Content, AxiosRequestConfig>({
    mutationFn: (content: Content, option?: AxiosRequestConfig) =>
      createContentUseCase(cmsRepository())(content, option),

    onSuccess: async (response) => {
      console.log('Create content success:', response);
    },

    onError: (error) => {
      console.error('Create content failed:', error);
    },
  });
};
