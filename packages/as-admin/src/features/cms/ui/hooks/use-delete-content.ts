import { useMutation } from '@tanstack/react-query';
import { AxiosError, AxiosRequestConfig } from 'axios';

import { deleteContent as deleteContentUseCase } from '../../application/cms';
import { cmsRepository } from '../../infrastructure/repositories/cms-repository';

export const useDeleteContent = () => {
  return useMutation<void, AxiosError, string, AxiosRequestConfig>({
    mutationFn: (id: string, option?: AxiosRequestConfig) =>
      deleteContentUseCase(cmsRepository())(id, option),

    onError: (error) => {
      console.error('Delete content failed:', error);
    },
  });
};
