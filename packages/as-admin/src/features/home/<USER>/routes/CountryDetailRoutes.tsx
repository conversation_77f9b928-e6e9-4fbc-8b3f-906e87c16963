import { Navigate, Route, Routes } from 'react-router-dom';

import { routes } from '@/routes/routes';

import { CountryDetail } from './CountryDetail';
import { MonthChange } from './MonthChange';
import { RegionalDetailAmerica } from './RegionalDetailAmerica';
import { RegionalDetailFrance } from './RegionalDetailFrance';
import { RegionalDetailItaly } from './RegionalDetailItaly';
import { RegionalDetailKorea } from './RegionalDetailKorea';
import { RegionalDetailSpain } from './RegionalDetailSpain';
import { RegionalDetailUK } from './RegionalDetailUK';

export const CountryDetailRoutes = () => {
  return (
    <Routes>
      <Route index element={<CountryDetail />} />
      <Route
        path={routes.home.userTrendSummary.monthChange.index}
        element={<MonthChange />}
      />
      <Route
        path={routes.home.userTrendSummary.regionalDetailKorea.index}
        element={<RegionalDetailKorea />}
      />
      <Route
        path={routes.home.userTrendSummary.regionalDetailAmerica.index}
        element={<RegionalDetailAmerica />}
      />
      <Route
        path={routes.home.userTrendSummary.regionalDetailUK.index}
        element={<RegionalDetailUK />}
      />
      <Route
        path={routes.home.userTrendSummary.regionalDetailFrance.index}
        element={<RegionalDetailFrance />}
      />
      <Route
        path={routes.home.userTrendSummary.regionalDetailSpain.index}
        element={<RegionalDetailSpain />}
      />
      <Route
        path={routes.home.userTrendSummary.regionalDetailItaly.index}
        element={<RegionalDetailItaly />}
      />
      <Route path="*" element={<Navigate to="." />} />
    </Routes>
  );
};
