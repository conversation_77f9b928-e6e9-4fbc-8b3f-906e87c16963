import { Navigate, Route, Routes } from 'react-router-dom';

import { ContentManagementRoutes } from '@/features/contentManagement/ui';
import { CustomerManagementRoutes } from '@/features/customerManagement/ui';
import { MasterManagementRoutes } from '@/features/masterManagement/ui';
import { NewsRoutes } from '@/features/news/ui';
import { SystemAdminRoutes } from '@/features/systemAdmin/ui';
import { TransactionManagementRoutes } from '@/features/transactionManagement/ui';
import { UserManagementRoutes } from '@/features/userManagement/ui';
import { routes } from '@/routes/routes';

import { CountryDetailRoutes } from './CountryDetailRoutes';
import { Home } from './Home';

export const HomeRoutes = () => {
  return (
    <Routes>
      <Route index element={<Home />} />
      <Route
        path={routes.home.userTrendSummary.countryDetail.wildcard}
        element={<CountryDetailRoutes />}
      />
      <Route
        path={routes.systemAdmin.wildcard}
        element={<SystemAdminRoutes />}
      />
      <Route
        path={routes.masterManagement.wildcard}
        element={<MasterManagementRoutes />}
      />
      <Route
        path={routes.userManagement.wildcard}
        element={<UserManagementRoutes />}
      />
      <Route
        path={routes.customerManagement.wildcard}
        element={<CustomerManagementRoutes />}
      />
      <Route
        path={routes.transactionManagement.wildcard}
        element={<TransactionManagementRoutes />}
      />
      <Route
        path={routes.contentManagement.wildcard}
        element={<ContentManagementRoutes />}
      />
      <Route path={routes.news.wildcard} element={<NewsRoutes />} />
      <Route path="*" element={<Navigate to="." />} />
    </Routes>
  );
};
