import DeleteIcon from '@mui/icons-material/Delete';
import SaveIcon from '@mui/icons-material/Save';
import { Box, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { Button } from '@/components/forms/button';
import { Head } from '@/components/seo/head';
import { env } from '@/config';
import { routes } from '@/routes/routes';

export const Home = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  return (
    <>
      <Head title="管理者ホーム画面" />
      <div className="p-4">
        {/* Slot: Contents Area */}
        <div className="mb-4 p-4 border-2 border-dashed border-purple-400 bg-purple-50 text-purple-600 rounded">
          <span className="font-mono text-sm font-semibold">
            ← slot: Contents Area
          </span>
        </div>

        {/* Title and Actions */}
        <div className="p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <Typography
              variant="h2"
              className="text-2xl font-bold text-gray-800"
            >
              #H2Title H2Title
            </Typography>
            <div className="flex space-x-2 items-center">
              <Button className="!bg-inverseSurface !rounded-2xl !h-8">
                <Typography variant="body2" className="!text-onInverseSurface">
                  下書き
                </Typography>
              </Button>
              <Button className="!bg-surfaceContainerLow !h-8">
                <Typography variant="h3" className="!text-onSurface">
                  編集
                </Typography>
              </Button>
              <Button
                className="!bg-[#FDEEEE] !h-12"
                startIcon={<DeleteIcon className="!fill-red-500" />}
              >
                <Typography variant="h3" className="!text-onSurface">
                  削除
                </Typography>
              </Button>
              <Button
                className="!bg-surfaceContainerHighest !h-12"
                startIcon={<SaveIcon className="!fill-primary" />}
              >
                <Typography variant="h3" className="!text-onSurface">
                  保存
                </Typography>
              </Button>
            </div>
          </div>

          {/* Metadata */}
          <div className="text-xs border-t-2 gap-4 pt-2 border-onSurface flex">
            <p className="mb-1 w-1/2">
              あのイーハトーヴォのすきとおった風、夏でも底に冷たさをもつ青いそら、うつくしい森で飾られたモリーオ市、郊外のぎらぎらひかる草の波。またそのなかでいっしょになったたくさんのひとたち、ファゼーロとロザーロ
            </p>
            <Box className="flex gap-2 pb-6 w-1/2">
              <Box className="flex items-center">
                <Typography variant="h5">作成日: </Typography>
                <Typography className="whitespace-pre">
                  {` 2025.12.31 23:59`}
                </Typography>
              </Box>
              <Box className="flex items-center">
                <Typography variant="h5">最終更新日:</Typography>
                <Typography className="whitespace-pre">
                  {` 2025.12.31 23:59`}
                </Typography>
              </Box>
              <Box className="flex items-center">
                <Typography variant="h5">更新適用日: </Typography>
                <Typography className="whitespace-pre">
                  {` 2025.12.31`}
                </Typography>
              </Box>
            </Box>
          </div>
        </div>

        {/* Slot: Section 1 */}
        <div className="mb-4 p-4 border-2 border-dashed border-cyan-400 bg-cyan-50 text-cyan-600 rounded min-h-[150px]">
          <span className="font-mono text-sm font-semibold">
            ← slot: Section
          </span>
          {/* Content for section 1 goes here */}
        </div>

        {/* Slot: Section 2 */}
        <div className="mb-4 p-4 border-2 border-dashed border-cyan-400 bg-cyan-50 text-cyan-600 rounded min-h-[150px]">
          <span className="font-mono text-sm font-semibold">
            ← slot: Section
          </span>
          {/* Content for section 2 goes here */}
        </div>

        {/* Slot: Section 3 */}
        <div className="mb-4 p-4 border-2 border-dashed border-cyan-400 bg-cyan-50 text-cyan-600 rounded min-h-[150px]">
          <span className="font-mono text-sm font-semibold">
            ← slot: Section
          </span>
          {/* Content for section 3 goes here */}
        </div>
      </div>

      {env.DEV && (
        <div className="flex flex-col justify-center items-center h-screen bg-white">
          <h1 className="text-6xl font-bold mb-4">
            {'2/001 管理者ホーム画面 - Admin home screen'}
          </h1>

          <div className="flex flex-row justify-center items-center bg-white pt-5 w-full">
            <div className="pr-10 w-1/2">
              <h1 className="text-3xl font-bold mb-4">{'2-1/001 国別詳細'}</h1>
              <Button
                onClick={() =>
                  navigate(routes.home.userTrendSummary.countryDetail.wildcard)
                }
              >
                {'Go to page 国別詳細'}
              </Button>
            </div>

            <div className="pr-10 w-1/2">
              <h1 className="text-3xl font-bold mb-4">
                {'3/001 システム管理者ツール'}
              </h1>
              <Button onClick={() => navigate(routes.systemAdmin.wildcard)}>
                {'Go to page システム管理者ツール'}
              </Button>
            </div>
          </div>

          <div className="flex flex-row justify-center items-center bg-white pt-5 w-full">
            <div className=" w-1/2">
              <h1 className="text-3xl font-bold mb-4">
                {'5/001 ユーザー管理ツール'}
              </h1>
              <Button onClick={() => navigate(routes.userManagement.wildcard)}>
                {'Go to page ユーザー管理ツール'}
              </Button>
            </div>

            <div className="pr-10 w-1/2">
              <h1 className="text-3xl font-bold mb-4">
                {'6/001 入出金管理ツール'}
              </h1>
              <Button
                onClick={() => navigate(routes.transactionManagement.wildcard)}
              >
                {'Go to page 入出金管理ツール'}
              </Button>
            </div>
          </div>

          <div className="flex flex-row justify-center items-center bg-white pt-5 w-full">
            <div className="pr-10 w-1/2">
              <h1 className="text-3xl font-bold mb-4">
                {'7/001 顧客対応ツール'}
              </h1>
              <Button
                onClick={() => navigate(routes.customerManagement.wildcard)}
              >
                {'Go to page 顧客対応ツール'}
              </Button>
            </div>

            <div className=" w-1/2">
              <h1 className="text-3xl font-bold mb-4">
                {'9/001 コンテンツ管理ツール'}
              </h1>
              <Button
                onClick={() => navigate(routes.contentManagement.wildcard)}
              >
                {'Go to page コンテンツ管理ツール'}
              </Button>
            </div>
          </div>

          <div className="flex flex-row justify-center items-center bg-white pt-5 w-full">
            <div className="pr-10 w-1/2">
              <h1 className="text-3xl font-bold mb-4">
                {t('components.news.index')}
              </h1>
              <Button onClick={() => navigate(routes.news.wildcard)}>
                {`Go to page ${t('components.news.index')}`}
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
