import { useNavigate } from 'react-router-dom';

import { But<PERSON> } from '@/components/forms/button';
import { Head } from '@/components/seo/head';
import { routes } from '@/routes/routes';

export const CountryDetail = () => {
  const navigate = useNavigate();

  return (
    <>
      <Head title="国別詳細" />
      <div className="flex flex-col justify-center items-center h-screen bg-white">
        <h1 className="text-6xl font-bold mb-4">
          {'2-1/001 国別詳細 - Country details'}
        </h1>
        <Button onClick={() => navigate(routes.home.wildcard)}>
          {'Go back to the previous page 管理者ホーム画面 - Admin home screen'}
        </Button>

        <div className="pr-10 w-1/2">
          <h1 className="text-3xl font-bold mb-4">{'2-1/002 表示月切替'}</h1>
          <Button
            onClick={() =>
              navigate(
                `${routes.home.userTrendSummary.countryDetail.index}${routes.home.userTrendSummary.monthChange.index}`,
              )
            }
          >
            {'Go to page 表示月切替'}
          </Button>
        </div>

        <div className="pr-10 w-1/2">
          <h1 className="text-3xl font-bold mb-4">
            {'2-1/004 地域別詳細（韓国）'}
          </h1>
          <Button
            onClick={() =>
              navigate(
                `${routes.home.userTrendSummary.countryDetail.index}${routes.home.userTrendSummary.regionalDetailKorea.index}`,
              )
            }
          >
            {'Go to page 地域別詳細（韓国）'}
          </Button>
        </div>

        <div className="pr-10 w-1/2">
          <h1 className="text-3xl font-bold mb-4">
            {'2-1/005 地域別詳細（アメリカ）'}
          </h1>
          <Button
            onClick={() =>
              navigate(
                `${routes.home.userTrendSummary.countryDetail.index}${routes.home.userTrendSummary.regionalDetailAmerica.index}`,
              )
            }
          >
            {'Go to page 地域別詳細（アメリカ）'}
          </Button>
        </div>

        <div className="pr-10 w-1/2">
          <h1 className="text-3xl font-bold mb-4">
            {'2-1/006 地域別詳細（UK）'}
          </h1>
          <Button
            onClick={() =>
              navigate(
                `${routes.home.userTrendSummary.countryDetail.index}${routes.home.userTrendSummary.regionalDetailUK.index}`,
              )
            }
          >
            {'Go to page 地域別詳細（UK）'}
          </Button>
        </div>

        <div className="pr-10 w-1/2">
          <h1 className="text-3xl font-bold mb-4">
            {'2-1/007 地域別詳細（フランス）'}
          </h1>
          <Button
            onClick={() =>
              navigate(
                `${routes.home.userTrendSummary.countryDetail.index}${routes.home.userTrendSummary.regionalDetailFrance.index}`,
              )
            }
          >
            {'Go to page 地域別詳細（フランス）'}
          </Button>
        </div>

        <div className="pr-10 w-1/2">
          <h1 className="text-3xl font-bold mb-4">
            {'2-1/009 地域別詳細（スペイン）'}
          </h1>
          <Button
            onClick={() =>
              navigate(
                `${routes.home.userTrendSummary.countryDetail.index}${routes.home.userTrendSummary.regionalDetailSpain.index}`,
              )
            }
          >
            {'Go to page 地域別詳細（スペイン）'}
          </Button>
        </div>

        <div className="pr-10 w-1/2">
          <h1 className="text-3xl font-bold mb-4">
            {'2-1/010 地域別詳細（イタリア）'}
          </h1>
          <Button
            onClick={() =>
              navigate(
                `${routes.home.userTrendSummary.countryDetail.index}${routes.home.userTrendSummary.regionalDetailItaly.index}`,
              )
            }
          >
            {'Go to page 地域別詳細（イタリア）'}
          </Button>
        </div>
      </div>
    </>
  );
};
