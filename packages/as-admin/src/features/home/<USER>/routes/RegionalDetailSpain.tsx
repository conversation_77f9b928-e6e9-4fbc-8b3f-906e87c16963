import { useNavigate } from 'react-router-dom';

import { But<PERSON> } from '@/components/forms/button';
import { Head } from '@/components/seo/head';
import { routes } from '@/routes/routes';

export const RegionalDetailSpain = () => {
  const navigate = useNavigate();

  return (
    <>
      <Head title="地域別詳細（スペイン）" />
      <div className="flex flex-col justify-center items-center h-screen bg-white">
        <h1 className="text-6xl font-bold mb-4">
          {'2-1/009 地域別詳細（スペイン）- Regional details (Spain)'}
        </h1>
        <Button
          onClick={() =>
            navigate(routes.home.userTrendSummary.countryDetail.wildcard)
          }
        >
          {'Go back to the previous page 国別詳細 - Country details'}
        </Button>
      </div>
    </>
  );
};
