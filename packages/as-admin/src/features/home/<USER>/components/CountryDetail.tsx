import { useNavigate } from 'react-router-dom';

import { But<PERSON> } from '@/components/forms/button';
import { Head } from '@/components/seo/head';
import { routes } from '@/routes/routes';

export const CountryDetail = () => {
  const navigate = useNavigate();

  return (
    <>
      <Head title="ユーザー管理ツール" />
      <div className="flex flex-col justify-center items-center h-screen bg-white">
        <h1 className="text-6xl font-bold mb-4">
          {'ユーザー管理ツール User Management Tools'}
        </h1>
        {/* <div className="flex flex-row justify-center items-center bg-white pt-5 w-full">
          <div className="pr-10 w-full">
            <h1 className="flex flex-row justify-center items-center text-3xl font-bold mb-4">
              {'管理者ホーム画面 Admin home screen'}
            </h1>
            <div className="flex flex-row justify-center items-center pr-10 w-full">
              <Button onClick={() => navigate('/')}>
                {'Go back to the previous page 管理者ホーム画面 - Admin home screen'}
              </Button>
            </div>
          </div>
        </div> */}

        <div className="pr-10 w-1/2">
          <h1 className="text-3xl font-bold mb-4">{'2-1/002 表示月切替'}</h1>
          <Button
            onClick={() =>
              navigate(routes.home.userTrendSummary.monthChange.wildcard)
            }
          >
            {'Go to page Master Management'}
          </Button>
        </div>

        <div className="pr-10 w-1/2">
          <h1 className="text-3xl font-bold mb-4">{'2-1/002 表示月切替'}</h1>
          <Button
            onClick={() =>
              navigate(routes.home.userTrendSummary.monthChange.wildcard)
            }
          >
            {'Go to page 表示月切替'}
          </Button>
        </div>

        <div className="pr-10 w-1/2">
          <h1 className="text-3xl font-bold mb-4">
            {'2-1/004 地域別詳細（韓国）'}
          </h1>
          <Button
            onClick={() =>
              navigate(
                routes.home.userTrendSummary.regionalDetailKorea.wildcard,
              )
            }
          >
            {'Go to page 地域別詳細（韓国）'}
          </Button>
        </div>

        <div className="pr-10 w-1/2">
          <h1 className="text-3xl font-bold mb-4">
            {'2-1/005 地域別詳細（アメリカ）'}
          </h1>
          <Button
            onClick={() =>
              navigate(
                routes.home.userTrendSummary.regionalDetailAmerica.wildcard,
              )
            }
          >
            {'Go to page 地域別詳細（アメリカ）'}
          </Button>
        </div>

        <div className="pr-10 w-1/2">
          <h1 className="text-3xl font-bold mb-4">
            {'2-1/006 地域別詳細（UK））'}
          </h1>
          <Button
            onClick={() =>
              navigate(routes.home.userTrendSummary.regionalDetailUK.wildcard)
            }
          >
            {'Go to page 地域別詳細（UK））'}
          </Button>
        </div>

        <div className="pr-10 w-1/2">
          <h1 className="text-3xl font-bold mb-4">
            {'2-1/009 地域別詳細（スペイン））'}
          </h1>
          <Button
            onClick={() =>
              navigate(
                routes.home.userTrendSummary.regionalDetailSpain.wildcard,
              )
            }
          >
            {'Go to page 地域別詳細（スペイン））'}
          </Button>
        </div>

        <div className="pr-10 w-1/2">
          <h1 className="text-3xl font-bold mb-4">
            {'2-1/010 地域別詳細（イタリア）'}
          </h1>
          <Button
            onClick={() =>
              navigate(
                routes.home.userTrendSummary.regionalDetailItaly.wildcard,
              )
            }
          >
            {'Go to page 地域別詳細（イタリア）'}
          </Button>
        </div>
      </div>
    </>
  );
};
