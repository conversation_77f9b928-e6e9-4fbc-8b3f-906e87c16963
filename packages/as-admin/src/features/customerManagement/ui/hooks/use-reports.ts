import { useCallback, useEffect } from 'react';

import { GetReportsSortByEnum, OrderBy, ReportStatus } from '@/bundles/model';
import { DEFAULT_PAGINATION } from '@/utils/types';

import { getReports, updateReportStatus } from '../../application/report';
import { reportRepository } from '../../infrastructure/repositories/report-repository';
import { useReportStore } from '../stores/report-store';

export const useReports = () => {
  const { isLoading, filter, setReports, setIsLoading, setPagination } =
    useReportStore();

  const fetchReports = useCallback(
    async (
      statusFilter?: string,
      page = 1,
      limit = 10,
      orderBy?: OrderBy,
      sortBy?: GetReportsSortByEnum,
    ) => {
      setIsLoading(true);
      try {
        const currentStatus = statusFilter || filter.status;
        let reportStatus: ReportStatus | undefined;

        // Map status filter to ReportStatus enum
        if (currentStatus && currentStatus !== 'ALL') {
          reportStatus = currentStatus as ReportStatus;
        }

        // Use orderBy and sortBy from store filter
        const response = await getReports(reportRepository(), {
          ...filter,
          status: reportStatus,
          pagination: { page, limit },
          orderBy: orderBy || filter.orderBy,
          sortBy: sortBy || filter.sortBy,
        });

        setReports(response.data || []);

        // Handle pagination
        const newPagination = response.pagination || DEFAULT_PAGINATION;
        setPagination(newPagination);
      } catch (error) {
        setReports([]);
        setPagination(DEFAULT_PAGINATION);
      } finally {
        setIsLoading(false);
      }
    },
    [filter, setIsLoading, setReports, setPagination],
  );

  const handleUpdateReportStatus = useCallback(
    async (id: string, status: ReportStatus) => {
      setIsLoading(true);
      try {
        await updateReportStatus(reportRepository(), id, { status });
        await fetchReports();
      } catch (error) {
        await fetchReports();
      } finally {
        setIsLoading(false);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [],
  );

  useEffect(() => {
    const fetchData = async () => {
      await fetchReports();
    };
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    isLoading,
    fetchReports,
    handleUpdateReportStatus,
  };
};
