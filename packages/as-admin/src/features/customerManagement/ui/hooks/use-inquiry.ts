import { useCallback, useEffect } from 'react';

import { inquiryRepository } from '../../infrastructure/repositories/inquiry-repository';
import { useRequestSummaryStore } from '../stores/request-summary.store';
export const useRequestSummary = () => {
  const {
    summaries,
    filter,
    isLoading,
    setSummaries,
    setFilter,
    setIsLoading,
  } = useRequestSummaryStore();

  useEffect(() => {
    const fetchData = async () => {
      await fetchRequestSummaries();
    };
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchRequestSummaries = useCallback(
    async (page?: number) => {
      try {
        setIsLoading(true);
        const response = await inquiryRepository().getInquiryStatsAreaService({
          ...filter,
          pagination: {
            ...filter.pagination,
            page: page ?? filter.pagination.page,
          },
        });
        setSummaries(response.data || []);
        setFilter({
          ...filter,
          pagination: {
            ...filter.pagination,
            total: response.pagination?.totalCount ?? 0,
          },
        });
      } catch (error) {
        setSummaries([]);
        setFilter({
          pagination: {
            limit: 10,
            page: 1,
            total: 0,
          },
        });
      } finally {
        setIsLoading(false);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [filter.pagination],
  );

  return {
    // Data
    summaries,

    // UI States
    isLoading,
    filter,

    // Actions
    setFilter,
    fetchRequestSummaries,
  };
};
