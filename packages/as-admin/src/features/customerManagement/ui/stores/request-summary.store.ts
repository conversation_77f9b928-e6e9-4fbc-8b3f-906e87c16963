import { create } from 'zustand';

import { InquiryLocationStats } from '@/bundles/model';
import { DEFAULT_PAGINATION } from '@/utils';

import { RequestSummaryFilter } from '../../domain/entities/request-summary';

interface RequestSummaryState {
  // Data
  summaries: InquiryLocationStats[];

  // Filter
  filter: RequestSummaryFilter;

  // UI States
  isLoading: boolean;

  // Actions
  setSummaries: (summaries: InquiryLocationStats[]) => void;
  setFilter: (filter: RequestSummaryFilter) => void;
  setIsLoading: (isLoading: boolean) => void;
}

export const useRequestSummaryStore = create<RequestSummaryState>()((set) => ({
  // Initial Data
  summaries: [],

  // Initial Filter
  filter: {
    pagination: DEFAULT_PAGINATION,
  },

  // Initial UI States
  isLoading: false,

  // Actions
  setSummaries: (summaries) => set({ summaries }),
  setFilter: (filter) => set({ filter }),
  setIsLoading: (isLoading) => set({ isLoading }),
}));
