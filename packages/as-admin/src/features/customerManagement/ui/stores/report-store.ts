import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import { GetReportsSortByEnum } from '@/bundles/model';
import { OrderBy, Pagination, Report } from '@/bundles/model/models';
import { DEFAULT_PAGINATION } from '@/utils';

import { ReportFilter } from '../../domain/entities/report';

interface ReportState {
  // Persisted states
  reports: Report[];
  filter: ReportFilter;
  pagination: Pagination;
  currentTab: string;

  // UI states
  isLoading: boolean;

  // Actions
  setReports: (reports: Report[]) => void;
  setFilter: (filter: ReportFilter) => void;
  setPagination: (pagination?: Pagination) => void;
  setIsLoading: (isLoading: boolean) => void;
  updateFilter: (partialFilter: Partial<ReportFilter>) => void;
  setCurrentTab: (currentTab: string) => void;

  // Cache management
  clearCache: () => void;
}

const initialFilter: ReportFilter = {
  status: undefined,
  orderBy: OrderBy.Desc,
  sortBy: GetReportsSortByEnum.CreatedAt,
};

export const useReportStore = create<ReportState>()(
  persist(
    (set) => ({
      // Initial states
      reports: [],
      filter: initialFilter,
      pagination: DEFAULT_PAGINATION,
      isLoading: false,
      currentTab: 'ALL',

      // Actions
      setReports: (reports) => set({ reports }),
      setFilter: (filter) => set({ filter }),
      setPagination: (pagination) =>
        set((state) => {
          const newPagination = pagination || DEFAULT_PAGINATION;
          return {
            pagination: newPagination,
            filter: {
              ...state.filter,
              pagination: newPagination,
            },
          };
        }),
      setIsLoading: (isLoading) => set({ isLoading }),
      updateFilter: (partialFilter) =>
        set((state) => ({
          filter: { ...state.filter, ...partialFilter },
        })),
      setCurrentTab: (currentTab) => set({ currentTab }),

      // Cache management
      clearCache: () =>
        set({
          reports: [],
          currentTab: 'ALL',
          filter: initialFilter,
          pagination: DEFAULT_PAGINATION,
          isLoading: false,
        }),
    }),
    {
      name: 'report-storage',
      partialize: () => ({
        reports: [],
      }),
    },
  ),
);
