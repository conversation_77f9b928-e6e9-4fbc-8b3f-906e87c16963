import { SortingState } from '@tanstack/react-table';
import { useCallback } from 'react';

import { ReportStatus, GetReportsSortByEnum, OrderBy } from '@/bundles/model';
import { routes } from '@/routes/routes';

import { useReports } from '../../hooks/use-reports';
import { useReportStore } from '../../stores/report-store';

export const useReportsContainer = () => {
  const {
    filter,
    pagination,
    updateFilter,
    reports,
    setPagination,
    currentTab,
    setCurrentTab,
  } = useReportStore();
  const { isLoading, fetchReports, handleUpdateReportStatus } = useReports();

  const breadcrumbItems = [
    { name: 'ホーム', href: routes.home.index },
    { name: '顧客対応', href: routes.customerManagement.wildcard },
    { name: '通報一覧' },
  ];

  const handleTabChange = (_: React.SyntheticEvent, newValue: string) => {
    setCurrentTab(newValue);
    updateFilter({
      status: newValue as ReportStatus,
    });
    setPagination({ ...pagination, page: 1, limit: 10 });

    fetchReports(newValue, 1, 10);
  };

  // Map table sorting to API parameters
  const handleSortChange = useCallback(
    (sorting: SortingState) => {
      // With enableSortingRemoval: false, sorting will never be empty
      const { id: columnId, desc } = sorting[0];

      // Map column to API sortBy
      const sortBy =
        columnId === 'status'
          ? GetReportsSortByEnum.Status
          : GetReportsSortByEnum.CreatedAt;

      // Map direction
      const orderBy = desc ? OrderBy.Desc : OrderBy.Asc;

      // Update filter
      updateFilter({ orderBy, sortBy });

      // Reset page and fetch
      setPagination({ ...pagination, page: 1 });
      fetchReports(currentTab, 1, pagination.limit, orderBy, sortBy);
    },
    [currentTab, pagination, updateFilter, setPagination, fetchReports],
  );

  return {
    // Data
    reports,
    filter,
    currentTab,
    breadcrumbItems,
    pagination,

    // UI States
    isLoading,

    // Handlers
    handleTabChange,
    handleSortChange,

    // Actions
    fetchReports,
    setPagination,
    handleUpdateReportStatus,
  };
};
