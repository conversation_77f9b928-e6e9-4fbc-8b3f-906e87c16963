import { useCallback } from 'react';

import { routes } from '@/routes/routes';

import { useRequestSummary } from '../../hooks/use-inquiry';

export const useRequestSummaryContainer = () => {
  const { summaries, isLoading, filter, setFilter, fetchRequestSummaries } =
    useRequestSummary();

  const breadcrumbItems = [
    { name: 'ホーム', href: routes.home.index },
    { name: '顧客対応', href: routes.customerManagement.wildcard },
    { name: '要望一覧' },
  ];

  const onPageChange = useCallback(
    (page: number) => {
      setFilter({ ...filter, pagination: { ...filter.pagination, page } });
      fetchRequestSummaries(page);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [filter.pagination],
  );

  return {
    // Data
    summaries,

    // UI States
    isLoading,
    filter,
    breadcrumbItems,

    onPageChange,

    // Actions
    setFilter,
  };
};
