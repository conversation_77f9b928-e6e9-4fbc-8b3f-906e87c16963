import { Navigate, Route, Routes } from 'react-router-dom';

import { routes } from '@/routes/routes';

import { Reports, RequestSummary } from '../components';

import { CustomerManagement } from './CustomerManagement';

export const CustomerManagementRoutes = () => {
  return (
    <Routes>
      <Route index element={<CustomerManagement />} />
      <Route
        path={routes.customerManagement.reports.wildcard}
        element={<Reports />}
      />
      <Route
        path={routes.customerManagement.requestSummary.wildcard}
        element={<RequestSummary />}
      />
      <Route path="*" element={<Navigate to="." />} />
    </Routes>
  );
};
