import { Typography } from '@mui/material';
import { useNavigate } from 'react-router-dom';

import { Breadcrumb } from '@/components/elements';
import { Button } from '@/components/forms/button';
import { Head } from '@/components/seo/head';
import { routes } from '@/routes/routes';

export const CustomerManagement = () => {
  const navigate = useNavigate();

  const breadcrumbItems = [{ name: 'ホーム', href: '/' }, { name: '顧客対応' }];

  const router = routes.customerManagement.index;

  return (
    <>
      <Head title="顧客対応" />
      <Breadcrumb title="顧客対応" items={breadcrumbItems} />
      <div className="p-4">
        <div className="grid grid-cols-3 gap-4">
          <Button
            onClick={() =>
              navigate(`${router}${routes.customerManagement.reports.index}`)
            }
            className="p-6 bg-primary text-center hover:opacity-90 transition-opacity !w-full !h-12"
          >
            <Typography variant="h3" className="!text-onPrimary">
              通報一覧
            </Typography>
          </Button>

          <Button
            onClick={() =>
              navigate(
                `${router}${routes.customerManagement.requestSummary.index}`,
              )
            }
            className="p-6 bg-primary text-center hover:opacity-90 transition-opacity !w-full !h-12"
          >
            <Typography variant="h3" className="!text-onPrimary">
              要望一覧
            </Typography>
          </Button>
        </div>
      </div>
    </>
  );
};
