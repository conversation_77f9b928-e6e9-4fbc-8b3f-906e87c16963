import { Box, Paper, Typography } from '@mui/material';
import { ColumnDef } from '@tanstack/react-table';

import { InquiryLocationStats } from '@/bundles/model';
import { CountryFlag } from '@/components/country_flag';
import { Breadcrumb } from '@/components/elements/breadcrumb/Breadcrumb';
import { Head } from '@/components/seo/head';
import { DataTable } from '@/components/tableData/DataTable';
import { getCountryInfo } from '@/utils/country';

import { useRequestSummaryContainer } from '../../containers/requestSummary/request-summary.container';

export const RequestSummary = () => {
  const { filter, isLoading, onPageChange, summaries, breadcrumbItems } =
    useRequestSummaryContainer();

  const columns: ColumnDef<InquiryLocationStats>[] = [
    {
      id: 'country',
      header: '国・地域',
      cell: ({ row }) => {
        return (
          <Box className="flex items-center gap-4">
            <Box>
              <CountryFlag countryCode={row.original.country} />
            </Box>
            <Box className="flex items-center justify-between flex-1">
              <Typography variant="body1">
                {getCountryInfo(row.original.country)?.name}
              </Typography>
              <Typography variant="h5" className="!text-surfaceTint pr-1">
                {row.original.countryCount}
              </Typography>
            </Box>
          </Box>
        );
      },
      size: 200,
    },
    {
      id: 'state',
      header: '州・都道府県',
      cell: ({ row }) => (
        <Box className="flex items-center justify-between w-full">
          <Typography variant="body1">{row.original.state}</Typography>
          <Typography variant="h5" className="!text-surfaceTint pr-1">
            {row.original.stateCount}
          </Typography>
        </Box>
      ),
      size: 200,
    },
    {
      id: 'city',
      header: '市区町村',
      cell: ({ row }) => (
        <Box className="flex items-center justify-between w-full">
          <Typography variant="body1">{row.original.city}</Typography>
          <Typography variant="h5" className="!text-surfaceTint pr-1">
            {row.original.cityCount}
          </Typography>
        </Box>
      ),
      size: 200,
    },
  ];

  return (
    <>
      <Head title="地域別集計" />
      <Box>
        <Breadcrumb title="顧客対応" items={breadcrumbItems} />

        <Box className="flex px-6 py-4 !w-full flex-col">
          <Box className="flex items-center border-b-[2px] mb-6 border-onSurface">
            <Typography variant="h2" className="pb-5">
              要望一覧
            </Typography>
          </Box>

          <Paper
            elevation={0}
            sx={{
              boxShadow: '0px 1px 3px 1px #00000026, 0px 1px 2px 0px #0000004D',
              borderRadius: '4px',
              width: '100%',
            }}
          >
            <Box className="flex-1 px-4">
              <DataTable<InquiryLocationStats & { id: string }, typeof filter>
                data={summaries.map((item, index) => ({
                  ...item,
                  id: index.toString(),
                }))}
                columns={
                  columns as ColumnDef<InquiryLocationStats & { id: string }>[]
                }
                initialFilter={filter}
                transformData={(data) => data}
                messageEmpty="データがありません"
                isLoading={isLoading}
                pagination={filter.pagination}
                onPageChange={onPageChange}
              />
            </Box>
          </Paper>
        </Box>
      </Box>
    </>
  );
};
