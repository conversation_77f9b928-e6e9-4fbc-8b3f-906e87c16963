import ArrowRightIcon from '@mui/icons-material/ArrowRight';
import CloseIcon from '@mui/icons-material/Close';
import {
  Box,
  Drawer,
  IconButton,
  Typography,
  Select,
  MenuItem,
  Avatar,
} from '@mui/material';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { Report, ReportStatus } from '@/bundles/model/models';
import { Button } from '@/components/forms';
import { reportStatusOptions } from '@/constants';
import { DateFormat } from '@/constants/date';
import {
  dateFormat,
  getAvatarInitial,
  handleReportItem,
  handleReportReporter,
} from '@/constants/utils';
import { useUserStore } from '@/features/userManagement/ui/stores/user/user.store';
import { routes } from '@/routes/routes';

interface StatusProps {
  isLoading: boolean;
  open: boolean;
  onClose: () => void;
  onUpdate: (id: string, status: ReportStatus) => Promise<void>;
  data?: Report;
}

export const Status = ({
  isLoading,
  open,
  onClose,
  data,
  onUpdate,
}: StatusProps) => {
  const navigate = useNavigate();
  const [selectedStatus, setSelectedStatus] = useState<
    ReportStatus | undefined
  >();

  useEffect(() => {
    setSelectedStatus(data?.status);
  }, [data?.status]);

  const { itemName, itemAvatar, isImageUrl, itemType, itemDetails } =
    handleReportItem(data?.item);

  const { reporterName, reporterAvatar, reporterType } = handleReportReporter(
    data?.reporter,
  );

  const { setActiveTab } = useUserStore();

  const handleItemClick = () => {
    if (!data?.item) return;

    let userId = '';

    let tabIndex = 0;

    if (itemType === 'user') {
      tabIndex = 0; // Tab user info
      userId = data.item?.id ?? '';
    } else if (itemType === 'seller' && reporterType === 'user') {
      tabIndex = 1; // Tab 出品者情報
      userId = data?.reporter?.id ?? '';
    } else if (itemType === 'product' && reporterType === 'user') {
      tabIndex = 3; // Tab sản phẩm và mở ProductDrawer
      userId = data.reporter?.id ?? '';
    }

    if (!userId) return;

    const detailPath = `${routes.userManagement.index}${routes.userManagement.search.details.index}/${userId}`;
    // Navigate and set tab
    navigate(detailPath);
    setActiveTab(tabIndex);
  };

  const handleReporterClick = () => {
    if (!data?.reporter) return;

    let userId = '';
    let tabIndex = 0;

    if (reporterType === 'user') {
      tabIndex = 0; // Tab user info
      userId = data.reporter?.id ?? '';
    } else if (reporterType === 'seller' && itemType === 'user') {
      tabIndex = 1; // Tab 出品者情報
      userId = data.item?.id ?? '';
    }

    if (!userId) return;

    const detailPath = `${routes.userManagement.index}${routes.userManagement.search.details.index}/${userId}`;

    // Navigate and set tab
    navigate(detailPath);
    setActiveTab(tabIndex);
  };

  const handleUpdate = async () => {
    if (!data?.id || !selectedStatus || typeof onUpdate !== 'function') return;
    await onUpdate(data.id, selectedStatus);
    onClose();
  };

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          height: 'calc(100% - 90px)',
          width: '100%',
          maxWidth: '320px',
          borderRadius: '16px 0 0 16px',
          margin: '64px 0px 24px 16px',
        },
      }}
    >
      <Box className="flex flex-col h-full">
        {/* Header */}
        <Box className="flex items-center p-4">
          <IconButton onClick={onClose} size="small" className="mr-4">
            <CloseIcon className="!fill-onSurface" />
          </IconButton>
          <Typography variant="h2" className="flex-1 text-center">
            通報内容
          </Typography>
        </Box>

        {/* Content */}
        <Box className="flex-1 p-4">
          {/* Item Info */}
          <Box className="pb-5">
            <Box className="flex items-start gap-3">
              {isImageUrl ? (
                <Avatar
                  src={itemAvatar}
                  className="!w-12 !h-12"
                  sx={{
                    bgcolor: '#9E9E9E',
                    borderRadius: itemType === 'product' ? '4px' : '50%',
                  }}
                />
              ) : (
                <Avatar
                  className="!w-12 !h-12"
                  sx={{
                    bgcolor: '#9E9E9E',
                    color: 'white',
                    borderRadius: itemType === 'product' ? '4px' : '50%',
                  }}
                >
                  {getAvatarInitial(itemName)}
                </Avatar>
              )}
              <Box
                className="flex items-center justify-between p-3 rounded-lg cursor-pointer w-full"
                onClick={handleItemClick}
              >
                <Box className="flex-1">
                  <Typography variant="h4" className="!text-onSurface pb-1">
                    {itemName}
                  </Typography>
                  {/* Brand Name for Product */}
                  {itemDetails.brandName && (
                    <Typography
                      variant="h5"
                      className="!text-onSurfaceVariant pb-1"
                    >
                      {itemDetails.brandName}
                    </Typography>
                  )}

                  {/* Description for Product or About for Seller */}
                  {itemDetails.description && (
                    <Typography
                      variant="subtitle2"
                      className="!text-onSurfaceVariant truncate max-w-48 max-h-7"
                    >
                      {String(itemDetails.description)}
                    </Typography>
                  )}

                  {itemDetails.about && (
                    <Typography
                      variant="subtitle2"
                      className="!text-onSurfaceVariant truncate max-w-48 max-h-7"
                    >
                      {itemDetails.about}
                    </Typography>
                  )}

                  {/* Product Status */}
                  {itemType === 'product' && (
                    <Typography
                      variant="subtitle2"
                      className=" !text-primary py-1"
                    >
                      新品
                    </Typography>
                  )}

                  {/* Price for Product */}
                  {itemDetails.price && (
                    <Typography variant="h4" className="font-bold">
                      {itemDetails.price}
                    </Typography>
                  )}
                </Box>
                <ArrowRightIcon className="text-gray-400" />
              </Box>
            </Box>
            <Box className="pr-3">
              <Typography
                variant="body2"
                className="!text-onSurface px-3 py-[21px]"
              >
                カテゴリ・情報の誤り
              </Typography>
            </Box>
            <Box className="border-b border-dashed border-[#6A7A7A]" />
          </Box>

          {/* Reporter Info */}
          <Box className="pb-5">
            <Typography variant="h4" className="!text-tertiary mb-3">
              通報主
            </Typography>
            <Box
              className="flex items-center justify-between p-3 rounded-lg cursor-pointer"
              onClick={handleReporterClick}
            >
              <Box className="flex items-center">
                <Box className="flex items-center justify-center !w-8 !h-8 rounded-full mr-3">
                  {reporterAvatar ? (
                    <Avatar src={reporterAvatar} sx={{ bgcolor: '#9E9E9E' }}>
                      {getAvatarInitial(String(reporterName))}
                    </Avatar>
                  ) : (
                    <Avatar
                      sx={{
                        bgcolor: '#9E9E9E',
                        color: 'white',
                      }}
                    >
                      {getAvatarInitial(String(reporterName))}
                    </Avatar>
                  )}
                </Box>
                <Typography
                  variant="body2"
                  className="!text-onSurfaceVariant !h-4"
                >
                  {reporterName}
                </Typography>
              </Box>
              <ArrowRightIcon className="text-gray-400" />
            </Box>
            <Box className="border-b border-dashed border-[#6A7A7A]" />
          </Box>

          {/* Status */}
          <Box className="pb-5">
            <Typography variant="h4" className="!text-tertiary mb-3">
              処理ステータス
            </Typography>
            <Select
              value={selectedStatus || ''}
              fullWidth
              sx={{
                '.MuiOutlinedInput-notchedOutline': {
                  borderRadius: '8px',
                },
                marginTop: '10px',
              }}
              onChange={(e) => {
                setSelectedStatus(e.target.value as ReportStatus);
              }}
            >
              {reportStatusOptions
                .filter((option) => option.value !== 'ALL')
                .map((option, idx) => (
                  <MenuItem key={`${option.value}-${idx}`} value={option.value}>
                    {option.key}
                  </MenuItem>
                ))}
            </Select>
            <Box className="mt-4 border-b border-dashed border-[#6A7A7A]" />
          </Box>

          {/* Dates */}
          <Box className="space-y-4">
            <Box className="pl-3">
              <Typography className="mb-1 !text-onSurfaceVariant text-sm">
                通報日
              </Typography>
              <Typography className="font-bold pt-2">
                {dateFormat(
                  data?.createdAt,
                  DateFormat.fullDateYYYYMMDDWithDotHHmm,
                )}
              </Typography>
            </Box>
            <Box className="pl-3">
              <Typography className="mb-1 !text-onSurfaceVariant text-sm">
                最終更新日
              </Typography>
              <Typography className="font-bold pt-2">
                {dateFormat(
                  data?.updatedAt,
                  DateFormat.fullDateYYYYMMDDWithDotHHmm,
                )}
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Footer */}
        <Box className="p-4 border-t border-gray-200">
          <Button
            className="w-full py-3 !bg-primary !h-12 text-white rounded-lg font-bold text-base hover:bg-primary/80"
            onClick={handleUpdate}
            disabled={!selectedStatus}
            isLoading={isLoading}
          >
            更新
          </Button>
        </Box>
      </Box>
    </Drawer>
  );
};
