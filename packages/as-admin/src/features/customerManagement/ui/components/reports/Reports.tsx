import { Box, Typography, Tabs, Tab, Paper, Avatar } from '@mui/material';
import { ColumnDef } from '@tanstack/react-table';
import dayjs from 'dayjs';
import { useCallback, useMemo, useState } from 'react';

import { Report } from '@/bundles/model';
import { Breadcrumb } from '@/components/elements';
import { Head } from '@/components/seo/head';
import { DataTable } from '@/components/tableData/DataTable';
import {
  getAvatarInitial,
  getItemAvatar,
  getItemName,
  getItemType,
  getReporterAvatar,
  getReporterName,
  getStatusBgColor,
  getStatusLabel,
  reportStatusOptions,
} from '@/constants';
import { DateFormat } from '@/constants/date';

import { useReportsContainer } from '../../containers/reports/reports-container';
import { Status } from '../status/Status';

export const Reports = () => {
  const [selectedReport, setSelectedReport] = useState<Report>();
  const [isStatusOpen, setIsStatusOpen] = useState(false);

  const {
    // Data
    reports,
    filter,
    pagination,
    currentTab,
    breadcrumbItems,
    isLoading,

    // Handlers
    handleTabChange,
    fetchReports,
    handleUpdateReportStatus,
    handleSortChange,

    setPagination,
  } = useReportsContainer();

  const handleRowClick = (row: Report) => {
    setSelectedReport(row);
    setIsStatusOpen(true);
  };

  const handleCloseStatus = () => {
    setIsStatusOpen(false);
  };

  const onPageChange = useCallback(
    (page: number) => {
      setPagination({ ...pagination, page });
      fetchReports(currentTab, page, pagination.limit);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [pagination],
  );

  const filterWithPagination = useMemo(
    () => ({
      ...filter,
      pagination: pagination || { page: 1, limit: 10, totalCount: 0 },
    }),
    [filter, pagination],
  );

  const columns: ColumnDef<Report>[] = useMemo(
    () => [
      {
        accessorKey: 'item',
        header: '通報対象',
        enableSorting: false,
        cell: ({ row }) => {
          const item = row.original.item;
          const itemName = getItemName(item);
          const itemAvatar = getItemAvatar(item);
          const isImageUrl = itemAvatar?.startsWith('http');
          const itemType = getItemType(item);

          return (
            <Box className="flex items-center gap-2">
              <Box className="flex items-center">
                {isImageUrl ? (
                  <Avatar
                    src={itemAvatar}
                    className="!w-8 !h-8"
                    sx={{
                      bgcolor: '#9E9E9E',
                      borderRadius: itemType === 'product' ? '4px' : '50%',
                    }}
                  />
                ) : (
                  <Avatar
                    className="!w-8 !h-8"
                    sx={{
                      bgcolor: '#9E9E9E',
                      color: 'white',
                      borderRadius: itemType === 'product' ? '4px' : '50%',
                    }}
                  >
                    {getAvatarInitial(itemName)}
                  </Avatar>
                )}
                <Typography className="!pl-2">{itemName}</Typography>
              </Box>
            </Box>
          );
        },
      },
      {
        accessorKey: 'reporter',
        header: '通報主',
        enableSorting: false,
        cell: ({ row }) => {
          const reporter = row.original.reporter;
          const reporterName = getReporterName(reporter);
          const reporterAvatar = getReporterAvatar(reporter);

          return (
            <Box className="flex items-center">
              {reporterAvatar ? (
                <Avatar
                  src={reporterAvatar}
                  className="!w-8 !h-8 mr-2"
                  sx={{ bgcolor: '#9E9E9E' }}
                >
                  {getAvatarInitial(reporterName)}
                </Avatar>
              ) : (
                <Avatar
                  className="!w-8 !h-8 mr-2"
                  sx={{
                    bgcolor: '#9E9E9E',
                    color: 'white',
                  }}
                >
                  {getAvatarInitial(reporterName)}
                </Avatar>
              )}
              <Typography className="text-sm">{reporterName}</Typography>
            </Box>
          );
        },
      },
      {
        accessorKey: 'detail',
        header: '通報内容',
        enableSorting: false,
        cell: ({ row }) => (
          <Box>
            <Typography className="text-sm">{row.original.detail}</Typography>
          </Box>
        ),
      },
      {
        accessorKey: 'createdAt',
        header: '通報日',
        enableSorting: true,
        size: 120,
        cell: ({ row }) => (
          <Box className="min-w-[120px]">
            <Typography className="text-sm">
              {row.original.createdAt
                ? dayjs(row.original.createdAt).format(
                    DateFormat.fullDateYYYYMMDDWithDot,
                  )
                : '--'}
            </Typography>
          </Box>
        ),
      },
      {
        accessorKey: 'status',
        header: '処理ステータス',
        enableSorting: true,
        size: 150,
        cell: ({ row }) => (
          <Box className="flex min-w-[120px]">
            <Box
              sx={{
                backgroundColor: getStatusBgColor(row.original.status),
                color: '#FFFFFF',
                padding: '2px 12px',
                borderRadius: '12px',
                fontSize: '0.875rem',
                fontWeight: 500,
                minWidth: '72px',
                textAlign: 'center',
              }}
            >
              {getStatusLabel(row.original.status)}
            </Box>
          </Box>
        ),
      },
    ],
    [],
  );

  return (
    <>
      <Head title="通報一覧" />
      <Box>
        <Breadcrumb title="顧客対応" items={breadcrumbItems} />

        <Box className="flex px-6 py-4 !w-full flex-col">
          <Box className="flex items-center border-b-[2px] mb-6 border-onSurface">
            <Typography variant="h2" className="pb-5">
              通報一覧
            </Typography>
          </Box>

          <Paper
            elevation={0}
            sx={{
              boxShadow: '0px 1px 3px 1px #00000026, 0px 1px 2px 0px #0000004D',
              borderRadius: '4px',
              width: '100%',
            }}
          >
            <Tabs
              value={currentTab}
              onChange={handleTabChange}
              sx={{
                width: '100%',
                borderBottom: '1px solid #D2DBDA',
                backgroundColor: '#F2FBFA',
              }}
            >
              {reportStatusOptions.map((tab) => (
                <Tab key={tab.value} label={tab.key} value={tab.value} />
              ))}
            </Tabs>

            <Box className="flex-1 px-4">
              <DataTable<Report & { id: string }, typeof filterWithPagination>
                data={
                  reports.filter((r) => r.id) as (Report & { id: string })[]
                }
                columns={columns as ColumnDef<Report & { id: string }>[]}
                initialFilter={filterWithPagination}
                transformData={(data) => data}
                messageEmpty="通報はありません"
                onRowClick={handleRowClick}
                isLoading={isLoading}
                pagination={pagination}
                onPageChange={onPageChange}
                onSortChange={handleSortChange}
              />
            </Box>
          </Paper>
        </Box>
      </Box>

      <Status
        open={isStatusOpen}
        onClose={handleCloseStatus}
        data={selectedReport}
        onUpdate={handleUpdateReportStatus}
        isLoading={isLoading}
      />
    </>
  );
};
