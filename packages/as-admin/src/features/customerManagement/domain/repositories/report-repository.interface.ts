import { AxiosRequestConfig } from 'axios';

import { GetReportsResponse, UpdateReportStatusRequest } from '@/bundles/model';

import { ReportFilter } from '../entities/report';

export interface IReportRepository {
  getReports: (
    filter: ReportFilter,
    options?: AxiosRequestConfig,
  ) => Promise<GetReportsResponse>;
  updateReportStatus: (
    id: string,
    updateReportStatusRequest: UpdateReportStatusRequest,
    options?: AxiosRequestConfig,
  ) => Promise<void>;
}
