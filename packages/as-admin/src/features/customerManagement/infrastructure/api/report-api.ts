import { AxiosRequestConfig } from 'axios';

import { GetReportsResponse, UpdateReportStatusRequest } from '@/bundles/model';
import { ReportApi } from '@/bundles/model/apis/report-api';
import apiClient from '@/lib/api-client';

import { ReportFilter } from '../../domain/entities/report';

// Create API instance
const reportApi = new ReportApi(undefined, undefined, apiClient);

const getReports = async (
  filter: ReportFilter,
  options?: AxiosRequestConfig,
): Promise<GetReportsResponse> => {
  const response = await reportApi.getReports(
    filter.pagination?.page,
    filter.pagination?.limit,
    filter.orderBy,
    filter.sortBy,
    filter.status,
    undefined,
    undefined,
    options,
  );

  if (!response) {
    throw new Error();
  }

  return response.data;
};

const updateReportStatus = async (
  id: string,
  updateReportStatusRequest: UpdateReportStatusRequest,
  options?: AxiosRequestConfig,
): Promise<void> => {
  await reportApi.updateReportStatus(id, updateReportStatusRequest, options);
};

export { getReports, updateReportStatus };
