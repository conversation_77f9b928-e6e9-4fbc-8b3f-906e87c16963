import { AxiosRequestConfig } from 'axios';

import {
  GetInquiryStatsAreaServiceResponse,
  InquiryApi,
} from '@/bundles/model';
import apiClient from '@/lib/api-client';

import { RequestSummaryFilter } from '../../domain/entities/request-summary';

const inquiryApi = new InquiryApi(undefined, undefined, apiClient);

export const getInquiryStatsAreaService = async (
  filter: RequestSummaryFilter,
  options?: AxiosRequestConfig,
): Promise<GetInquiryStatsAreaServiceResponse> => {
  const response = await inquiryApi.getInquiryStatsAreaService(
    filter.pagination?.page,
    filter.pagination?.limit,
    options,
  );

  if (!response) {
    throw new Error();
  }

  return response.data;
};
