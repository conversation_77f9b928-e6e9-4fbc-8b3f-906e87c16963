import { AxiosRequestConfig } from 'axios';

import { GetInquiryStatsAreaServiceResponse } from '@/bundles/model';

import { RequestSummaryFilter } from '../../domain/entities/request-summary';
import { IInquiryRepository } from '../../domain/repositories/inquiry-repository.interface';
import { getInquiryStatsAreaService } from '../api/inquiry.api';

export const inquiryRepository = (): IInquiryRepository => {
  return {
    getInquiryStatsAreaService: async (
      filter: RequestSummaryFilter,
      options?: AxiosRequestConfig,
    ): Promise<GetInquiryStatsAreaServiceResponse> => {
      const response = await getInquiryStatsAreaService(filter, options);
      return response;
    },
  };
};
