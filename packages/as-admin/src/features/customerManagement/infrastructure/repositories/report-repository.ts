import { AxiosRequestConfig } from 'axios';

import {
  GetReportsResponse,
  UpdateReportStatusRequest,
} from '@/bundles/model/models';

import { ReportFilter } from '../../domain/entities/report';
import { IReportRepository } from '../../domain/repositories/report-repository.interface';
import { getReports, updateReportStatus } from '../api/report-api';

export const reportRepository = (): IReportRepository => {
  return {
    getReports: async (
      filter: ReportFilter,
      options?: AxiosRequestConfig,
    ): Promise<GetReportsResponse> => {
      const response = await getReports(filter, options);
      return response;
    },

    updateReportStatus: async (
      id: string,
      updateReportStatusRequest: UpdateReportStatusRequest,
      options?: AxiosRequestConfig,
    ): Promise<void> => {
      await updateReportStatus(id, updateReportStatusRequest, options);
    },
  };
};
