import { GetReportsResponse, UpdateReportStatusRequest } from '@/bundles/model';

import { ReportFilter } from '../domain/entities/report';
import { IReportRepository } from '../domain/repositories/report-repository.interface';

const getReports = async (
  reportRepository: IReportRepository,
  filter: ReportFilter,
): Promise<GetReportsResponse> => {
  return await reportRepository.getReports(filter);
};

const updateReportStatus = async (
  reportRepository: IReportRepository,
  id: string,
  updateReportStatusRequest: UpdateReportStatusRequest,
): Promise<void> => {
  return await reportRepository.updateReportStatus(
    id,
    updateReportStatusRequest,
  );
};

export { getReports, updateReportStatus };
