import { AxiosRequestConfig } from 'axios';

import { GetInquiryStatsAreaServiceResponse } from '@/bundles/model';

import { RequestSummaryFilter } from '../domain/entities/request-summary';
import { IInquiryRepository } from '../domain/repositories/inquiry-repository.interface';

export const getInquiryStatsAreaService = async (
  inquiryRepository: IInquiryRepository,
  filter: RequestSummaryFilter,
  options?: AxiosRequestConfig,
): Promise<GetInquiryStatsAreaServiceResponse> => {
  return await inquiryRepository.getInquiryStatsAreaService(filter, options);
};
