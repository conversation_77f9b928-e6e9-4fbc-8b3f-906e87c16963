import { AxiosRequestConfig } from 'axios';

import { PublishCustomNGWordsBranchRequest } from '@/bundles/model';
import { ICustomNgWordsBranchRepository } from '@/features/masterManagement/domain/repositories/custom-ng-words-branches-repository.interface';

export const publishCustomNgWordsBranch =
  (customNgWordsBranchRepository: ICustomNgWordsBranchRepository) =>
  async (
    id: string,
    publishCustomNgWordsBranchRequest?: PublishCustomNGWordsBranchRequest,
    options?: AxiosRequestConfig,
  ): Promise<void> => {
    return await customNgWordsBranchRepository.publishCustomNgWordsBranch(
      id,
      publishCustomNgWordsBranchRequest,
      options,
    );
  };
