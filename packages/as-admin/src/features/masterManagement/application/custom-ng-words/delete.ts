import { AxiosRequestConfig } from 'axios';

import { ICustomNgWordsBranchRepository } from '@/features/masterManagement/domain/repositories/custom-ng-words-branches-repository.interface';

export const deleteCustomNgWordsBranch =
  (customNgWordsBranchRepository: ICustomNgWordsBranchRepository) =>
  async (id: string, options?: AxiosRequestConfig): Promise<void> => {
    return await customNgWordsBranchRepository.deleteCustomNgWordsBranch(
      id,
      options,
    );
  };
