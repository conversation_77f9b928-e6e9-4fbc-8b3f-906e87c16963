import { AxiosRequestConfig } from 'axios';

import { CustomNGWordsBranch } from '@/bundles/model';
import { ICustomNgWordsBranchRepository } from '@/features/masterManagement/domain/repositories/custom-ng-words-branches-repository.interface';

export const updateCustomNgWordsBranch =
  (customNgWordsBranchRepository: ICustomNgWordsBranchRepository) =>
  async (
    id: string,
    customNgWordsBranch?: CustomNGWordsBranch,
    options?: AxiosRequestConfig,
  ): Promise<CustomNGWordsBranch> => {
    return await customNgWordsBranchRepository.updateCustomNgWordsBranch(
      id,
      customNgWordsBranch,
      options,
    );
  };
