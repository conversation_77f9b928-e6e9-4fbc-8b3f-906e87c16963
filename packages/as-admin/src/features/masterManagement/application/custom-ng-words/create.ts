import { AxiosRequestConfig } from 'axios';

import { CustomNGWordsBranch } from '@/bundles/model';
import { ICustomNgWordsBranchRepository } from '@/features/masterManagement/domain/repositories/custom-ng-words-branches-repository.interface';

export const createCustomNgWordsBranch =
  (customNgWordsBranchRepository: ICustomNgWordsBranchRepository) =>
  async (
    customNgWordsBranch?: CustomNGWordsBranch,
    options?: AxiosRequestConfig,
  ): Promise<CustomNGWordsBranch> => {
    return await customNgWordsBranchRepository.createCustomNgWordsBranch(
      customNgWordsBranch,
      options,
    );
  };
