import { AxiosRequestConfig } from 'axios';

import { CustomNGWordsBranch, Pagination } from '@/bundles/model';

import { ICustomNgWordsBranchRepository } from '../../domain/repositories/custom-ng-words-branches-repository.interface';

export const getCustomNgWordsBranches =
  (customNgWordsBranchRepository: ICustomNgWordsBranchRepository) =>
  async (
    pagination?: Pagination,
    options?: AxiosRequestConfig,
  ): Promise<CustomNGWordsBranch[]> => {
    return await customNgWordsBranchRepository.getCustomNgWordsBranches(
      pagination,
      options,
    );
  };
