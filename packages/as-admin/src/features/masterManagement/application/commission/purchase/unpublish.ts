import { AxiosRequestConfig } from 'axios';

import { ICommissionPurchaseBranchRepository } from '@/features/masterManagement/domain/repositories/commission-purchase-branch-repository.interface';

export const unpublishCommissionPurchaseBranch =
  (repository: ICommissionPurchaseBranchRepository) =>
  async (id: string, options?: AxiosRequestConfig) => {
    return repository.unpublishCommissionPurchaseBranch(id, options);
  };
