import { AxiosRequestConfig } from 'axios';

import { PurchaseFeesBranch } from '@/bundles/model';
import { ICommissionPurchaseBranchRepository } from '@/features/masterManagement/domain/repositories/commission-purchase-branch-repository.interface';

export const updateCommissionPurchaseBranch =
  (repository: ICommissionPurchaseBranchRepository) =>
  async (
    id: string,
    commissionPurchaseBranch?: PurchaseFeesBranch,
    options?: AxiosRequestConfig,
  ) => {
    return repository.updateCommissionPurchaseBranch(
      id,
      commissionPurchaseBranch,
      options,
    );
  };
