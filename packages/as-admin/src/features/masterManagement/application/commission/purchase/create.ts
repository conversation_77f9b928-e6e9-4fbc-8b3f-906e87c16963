import { AxiosRequestConfig } from 'axios';

import { PurchaseFeesBranch } from '@/bundles/model';
import { ICommissionPurchaseBranchRepository } from '@/features/masterManagement/domain/repositories/commission-purchase-branch-repository.interface';

export const createCommissionPurchaseBranch =
  (repository: ICommissionPurchaseBranchRepository) =>
  async (
    commissionPurchaseBranch?: PurchaseFeesBranch,
    options?: AxiosRequestConfig,
  ) => {
    return repository.createCommissionPurchaseBranch(
      commissionPurchaseBranch,
      options,
    );
  };
