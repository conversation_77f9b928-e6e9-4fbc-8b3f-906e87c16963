import { AxiosRequestConfig } from 'axios';

import { PublishPurchaseFeesBranchRequest } from '@/bundles/model';
import { ICommissionPurchaseBranchRepository } from '@/features/masterManagement/domain/repositories/commission-purchase-branch-repository.interface';

export const publishCommissionPurchaseBranch =
  (repository: ICommissionPurchaseBranchRepository) =>
  async (
    id: string,
    publishPurchaseFeesBranchRequest?: PublishPurchaseFeesBranchRequest,
    options?: AxiosRequestConfig,
  ) => {
    return repository.publishCommissionPurchaseBranch(
      id,
      publishPurchaseFeesBranchRequest,
      options,
    );
  };
