import { AxiosRequestConfig } from 'axios';

import { Pagination } from '@/bundles/model';
import { ICommissionPurchaseBranchRepository } from '@/features/masterManagement/domain/repositories/commission-purchase-branch-repository.interface';

export const getCommissionPurchaseBranches =
  (repository: ICommissionPurchaseBranchRepository) =>
  async (pagination?: Pagination, options?: AxiosRequestConfig) => {
    return repository.getCommissionPurchaseBranches(pagination, options);
  };
