import { AxiosRequestConfig } from 'axios';

import { SubscriptionFeesBranch } from '@/bundles/model';
import { ICommissionSubscriptionBranchRepository } from '@/features/masterManagement/domain/repositories/commission-subscription-branch-repository.interface';

export const updateCommissionSubscriptionBranch =
  (repository: ICommissionSubscriptionBranchRepository) =>
  async (
    id: string,
    subscriptionFeesBranch?: SubscriptionFeesBranch,
    options?: AxiosRequestConfig,
  ) => {
    return repository.updateCommissionSubscriptionBranch(
      id,
      subscriptionFeesBranch,
      options,
    );
  };
