import { AxiosRequestConfig } from 'axios';

import { SubscriptionFeesBranch } from '@/bundles/model';
import { ICommissionSubscriptionBranchRepository } from '@/features/masterManagement/domain/repositories/commission-subscription-branch-repository.interface';

export const createCommissionSubscriptionBranch =
  (repository: ICommissionSubscriptionBranchRepository) =>
  async (
    subscriptionFeesBranch?: SubscriptionFeesBranch,
    options?: AxiosRequestConfig,
  ) => {
    return repository.createCommissionSubscriptionBranch(
      subscriptionFeesBranch,
      options,
    );
  };
