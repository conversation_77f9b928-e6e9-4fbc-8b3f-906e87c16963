import { AxiosRequestConfig } from 'axios';

import { Pagination } from '@/bundles/model';
import { ICommissionSubscriptionBranchRepository } from '@/features/masterManagement/domain/repositories/commission-subscription-branch-repository.interface';

export const getCommissionSubscriptionBranches =
  (repository: ICommissionSubscriptionBranchRepository) =>
  async (pagination?: Pagination, options?: AxiosRequestConfig) => {
    return repository.getCommissionSubscriptionBranches(pagination, options);
  };
