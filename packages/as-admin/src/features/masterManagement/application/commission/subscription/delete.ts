import { AxiosRequestConfig } from 'axios';

import { ICommissionSubscriptionBranchRepository } from '@/features/masterManagement/domain/repositories/commission-subscription-branch-repository.interface';

export const deleteCommissionSubscriptionBranch =
  (repository: ICommissionSubscriptionBranchRepository) =>
  async (id: string, options?: AxiosRequestConfig) => {
    return repository.deleteCommissionSubscriptionBranch(id, options);
  };
