import { AxiosRequestConfig } from 'axios';

import { PublishSubscriptionFeesBranchRequest } from '@/bundles/model';
import { ICommissionSubscriptionBranchRepository } from '@/features/masterManagement/domain/repositories/commission-subscription-branch-repository.interface';

export const publishCommissionSubscriptionBranch =
  (repository: ICommissionSubscriptionBranchRepository) =>
  async (
    id: string,
    publishSubscriptionFeesBranchRequest?: PublishSubscriptionFeesBranchRequest,
    options?: AxiosRequestConfig,
  ) => {
    return repository.publishCommissionSubscriptionBranch(
      id,
      publishSubscriptionFeesBranchRequest,
      options,
    );
  };
