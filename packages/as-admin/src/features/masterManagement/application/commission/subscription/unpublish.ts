import { AxiosRequestConfig } from 'axios';

import { ICommissionSubscriptionBranchRepository } from '@/features/masterManagement/domain/repositories/commission-subscription-branch-repository.interface';

export const unpublishCommissionSubscriptionBranch =
  (repository: ICommissionSubscriptionBranchRepository) =>
  async (id: string, options?: AxiosRequestConfig) => {
    return repository.unpublishCommissionSubscriptionBranch(id, options);
  };
