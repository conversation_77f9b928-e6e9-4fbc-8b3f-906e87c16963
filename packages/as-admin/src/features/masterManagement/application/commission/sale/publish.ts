import { AxiosRequestConfig } from 'axios';

import { PublishCommissionSalesBranchRequest } from '@/bundles/model';
import { ICommissionSaleBranchRepository } from '@/features/masterManagement/domain/repositories/commission-sale-branch-repository.interface';

export const publishCommissionSaleBranch =
  (repository: ICommissionSaleBranchRepository) =>
  async (
    id: string,
    publishCommissionSalesBranchRequest?: PublishCommissionSalesBranchRequest,
    options?: AxiosRequestConfig,
  ) => {
    return repository.publishCommissionSaleBranch(
      id,
      publishCommissionSalesBranchRequest,
      options,
    );
  };
