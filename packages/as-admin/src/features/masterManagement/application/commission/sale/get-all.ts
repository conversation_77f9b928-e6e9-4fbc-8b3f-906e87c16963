import { AxiosRequestConfig } from 'axios';

import { Pagination } from '@/bundles/model';
import { ICommissionSaleBranchRepository } from '@/features/masterManagement/domain/repositories/commission-sale-branch-repository.interface';

export const getCommissionSaleBranches =
  (repository: ICommissionSaleBranchRepository) =>
  async (pagination?: Pagination, options?: AxiosRequestConfig) => {
    return repository.getCommissionSaleBranches(pagination, options);
  };
