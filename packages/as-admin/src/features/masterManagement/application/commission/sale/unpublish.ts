import { AxiosRequestConfig } from 'axios';

import { ICommissionSaleBranchRepository } from '@/features/masterManagement/domain/repositories/commission-sale-branch-repository.interface';

export const unpublishCommissionSaleBranch =
  (repository: ICommissionSaleBranchRepository) =>
  async (id: string, options?: AxiosRequestConfig) => {
    return repository.unpublishCommissionSaleBranch(id, options);
  };
