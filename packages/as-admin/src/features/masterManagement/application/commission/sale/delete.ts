import { AxiosRequestConfig } from 'axios';

import { ICommissionSaleBranchRepository } from '@/features/masterManagement/domain/repositories/commission-sale-branch-repository.interface';

export const deleteCommissionSaleBranch =
  (repository: ICommissionSaleBranchRepository) =>
  async (id: string, options?: AxiosRequestConfig) => {
    return repository.deleteCommissionSaleBranch(id, options);
  };
