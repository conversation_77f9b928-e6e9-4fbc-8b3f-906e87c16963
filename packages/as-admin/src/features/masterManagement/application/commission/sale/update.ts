import { AxiosRequestConfig } from 'axios';

import { CommissionSalesBranch } from '@/bundles/model';
import { ICommissionSaleBranchRepository } from '@/features/masterManagement/domain/repositories/commission-sale-branch-repository.interface';

export const updateCommissionSaleBranch =
  (repository: ICommissionSaleBranchRepository) =>
  async (
    id: string,
    commissionSaleBranch?: CommissionSalesBranch,
    options?: AxiosRequestConfig,
  ) => {
    return repository.updateCommissionSaleBranch(
      id,
      commissionSaleBranch,
      options,
    );
  };
