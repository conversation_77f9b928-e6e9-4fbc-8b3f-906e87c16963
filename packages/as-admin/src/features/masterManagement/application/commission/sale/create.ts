import { AxiosRequestConfig } from 'axios';

import { CommissionSalesBranch } from '@/bundles/model';
import { ICommissionSaleBranchRepository } from '@/features/masterManagement/domain/repositories/commission-sale-branch-repository.interface';

export const createCommissionSaleBranch =
  (repository: ICommissionSaleBranchRepository) =>
  async (
    commissionSaleBranch?: CommissionSalesBranch,
    options?: AxiosRequestConfig,
  ) => {
    return repository.createCommissionSaleBranch(commissionSaleBranch, options);
  };
