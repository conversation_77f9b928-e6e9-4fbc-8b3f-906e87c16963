import { AxiosRequestConfig } from 'axios';

import { ICommissionTransferBranchRepository } from '@/features/masterManagement/domain/repositories/commission-transfer-branch-repository.interface';

export const unpublishCommissionTransferBranch =
  (repository: ICommissionTransferBranchRepository) =>
  async (id: string, options?: AxiosRequestConfig) => {
    return repository.unpublishCommissionTransferBranch(id, options);
  };
