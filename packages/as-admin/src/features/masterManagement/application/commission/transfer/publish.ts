import { AxiosRequestConfig } from 'axios';

import { PublishTransferFeesBranchRequest } from '@/bundles/model';
import { ICommissionTransferBranchRepository } from '@/features/masterManagement/domain/repositories/commission-transfer-branch-repository.interface';

export const publishCommissionTransferBranch =
  (repository: ICommissionTransferBranchRepository) =>
  async (
    id: string,
    publishTransferFeesBranchRequest?: PublishTransferFeesBranchRequest,
    options?: AxiosRequestConfig,
  ) => {
    return repository.publishCommissionTransferBranch(
      id,
      publishTransferFeesBranchRequest,
      options,
    );
  };
