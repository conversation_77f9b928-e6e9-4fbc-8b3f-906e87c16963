import { AxiosRequestConfig } from 'axios';

import { TransferFeesBranch } from '@/bundles/model';
import { ICommissionTransferBranchRepository } from '@/features/masterManagement/domain/repositories/commission-transfer-branch-repository.interface';

export const updateCommissionTransferBranch =
  (repository: ICommissionTransferBranchRepository) =>
  async (
    id: string,
    commissionTransferBranch?: TransferFeesBranch,
    options?: AxiosRequestConfig,
  ) => {
    return repository.updateCommissionTransferBranch(
      id,
      commissionTransferBranch,
      options,
    );
  };
