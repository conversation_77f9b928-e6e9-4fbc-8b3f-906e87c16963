import { AxiosRequestConfig } from 'axios';

import { TransferFeesBranch } from '@/bundles/model';
import { ICommissionTransferBranchRepository } from '@/features/masterManagement/domain/repositories/commission-transfer-branch-repository.interface';

export const createCommissionTransferBranch =
  (repository: ICommissionTransferBranchRepository) =>
  async (
    commissionTransferBranch?: TransferFeesBranch,
    options?: AxiosRequestConfig,
  ) => {
    return repository.createCommissionTransferBranch(
      commissionTransferBranch,
      options,
    );
  };
