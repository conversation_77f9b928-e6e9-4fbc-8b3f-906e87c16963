import { AxiosRequestConfig } from 'axios';

import { Pagination } from '@/bundles/model';
import { ICommissionTransferBranchRepository } from '@/features/masterManagement/domain/repositories/commission-transfer-branch-repository.interface';

export const getCommissionTransferBranches =
  (repository: ICommissionTransferBranchRepository) =>
  async (pagination?: Pagination, options?: AxiosRequestConfig) => {
    return repository.getCommissionTransferBranches(pagination, options);
  };
