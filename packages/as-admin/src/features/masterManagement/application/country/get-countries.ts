import { AxiosRequestConfig } from 'axios';

import { GetCountriesResponse } from '@/bundles/model';
import { IBrandCountryRepository } from '@/features/masterManagement/domain/repositories/country-branch-repository.interface';

export const getCountries =
  (countryBranchRepository: IBrandCountryRepository) =>
  async (
    active?: boolean,
    options?: AxiosRequestConfig,
  ): Promise<GetCountriesResponse> => {
    return await countryBranchRepository.getCountries(active, options);
  };
