import { AxiosRequestConfig } from 'axios';

import { IBrandCountryRepository } from '@/features/masterManagement/domain/repositories/country-branch-repository.interface';

export const unpublishCountryBranch =
  (countryBranchRepository: IBrandCountryRepository) =>
  async (id: string, options?: AxiosRequestConfig): Promise<void> => {
    return await countryBranchRepository.unpublishCountryBranch(id, options);
  };
