import { AxiosRequestConfig } from 'axios';

import { CountryBranch } from '@/bundles/model';
import { IBrandCountryRepository } from '@/features/masterManagement/domain/repositories/country-branch-repository.interface';

export const updateCountryBranch =
  (countryBranchRepository: IBrandCountryRepository) =>
  async (
    id: string,
    countryBranch?: CountryBranch,
    options?: AxiosRequestConfig,
  ): Promise<CountryBranch> => {
    return await countryBranchRepository.updateCountryBranch(
      id,
      countryBranch,
      options,
    );
  };
