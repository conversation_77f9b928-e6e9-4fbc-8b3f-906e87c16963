import { AxiosRequestConfig } from 'axios';

import { CountryBranch } from '@/bundles/model';
import { IBrandCountryRepository } from '@/features/masterManagement/domain/repositories/country-branch-repository.interface';

export const createCountryBranch =
  (IBrandCountryRepository: IBrandCountryRepository) =>
  async (
    brandsBranch?: CountryBranch,
    options?: AxiosRequestConfig,
  ): Promise<CountryBranch> => {
    return await IBrandCountryRepository.createCountryBranch(
      brandsBranch,
      options,
    );
  };
