import { AxiosRequestConfig } from 'axios';

import { PublishCountryBranchRequest } from '@/bundles/model';
import { IBrandCountryRepository } from '@/features/masterManagement/domain/repositories/country-branch-repository.interface';

export const publishCountryBranch =
  (countryBranchRepository: IBrandCountryRepository) =>
  async (
    id: string,
    publishCountryBranchRequest?: PublishCountryBranchRequest,
    options?: AxiosRequestConfig,
  ): Promise<void> => {
    return await countryBranchRepository.publishCountryBranch(
      id,
      publishCountryBranchRequest,
      options,
    );
  };
