import { AxiosRequestConfig } from 'axios';

import { IBrandCountryRepository } from '@/features/masterManagement/domain/repositories/country-branch-repository.interface';

export const deleteCountryBranch =
  (countryBranchRepository: IBrandCountryRepository) =>
  async (id: string, options?: AxiosRequestConfig): Promise<void> => {
    return await countryBranchRepository.deleteCountryBranch(id, options);
  };
