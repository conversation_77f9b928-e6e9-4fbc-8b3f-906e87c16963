import { AxiosRequestConfig } from 'axios';

import { CountryBranch, Pagination } from '@/bundles/model';
import { IBrandCountryRepository } from '@/features/masterManagement/domain/repositories/country-branch-repository.interface';

export const getCountryBranches =
  (countryBranchRepository: IBrandCountryRepository) =>
  async (
    countryCode: string,
    pagination?: Pagination,
    options?: AxiosRequestConfig,
  ): Promise<CountryBranch[]> => {
    return await countryBranchRepository.getCountryBranches(
      countryCode,
      pagination,
      options,
    );
  };
