import { AxiosRequestConfig } from 'axios';

import { IBrandBranchRepository } from '@/features/masterManagement/domain/repositories/brands-branch-repository.interface';

export const unpublishBrandsBranch =
  (brandsBranchRepository: IBrandBranchRepository) =>
  async (id: string, options?: AxiosRequestConfig): Promise<void> => {
    return await brandsBranchRepository.unpublishBrandsBranch(id, options);
  };
