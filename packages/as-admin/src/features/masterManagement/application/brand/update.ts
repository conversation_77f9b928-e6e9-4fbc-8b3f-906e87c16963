import { AxiosRequestConfig } from 'axios';

import { BrandsBranch } from '@/bundles/model';
import { IBrandBranchRepository } from '@/features/masterManagement/domain/repositories/brands-branch-repository.interface';

export const updateBrandsBranch =
  (brandsBranchRepository: IBrandBranchRepository) =>
  async (
    id: string,
    brandsBranch?: BrandsBranch,
    options?: AxiosRequestConfig,
  ): Promise<BrandsBranch> => {
    return await brandsBranchRepository.updateBrandsBranch(
      id,
      brandsBranch,
      options,
    );
  };
