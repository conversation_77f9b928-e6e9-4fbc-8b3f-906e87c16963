import { AxiosRequestConfig } from 'axios';

import { BrandsBranch, Pagination } from '@/bundles/model';

import { IBrandBranchRepository } from '../../domain/repositories/brands-branch-repository.interface';

export const getBrandsBranches =
  (brandsBranchRepository: IBrandBranchRepository) =>
  async (
    pagination?: Pagination,
    options?: AxiosRequestConfig,
  ): Promise<BrandsBranch[]> => {
    return await brandsBranchRepository.getBrandsBranches(pagination, options);
  };
