import { AxiosRequestConfig } from 'axios';

import { PublishBrandsBranchRequest } from '@/bundles/model';
import { IBrandBranchRepository } from '@/features/masterManagement/domain/repositories/brands-branch-repository.interface';

export const publishBrandsBranch =
  (brandsBranchRepository: IBrandBranchRepository) =>
  async (
    id: string,
    publishBrandsBranchRequest?: PublishBrandsBranchRequest,
    options?: AxiosRequestConfig,
  ): Promise<void> => {
    return await brandsBranchRepository.publishBrandsBranch(
      id,
      publishBrandsBranchRequest,
      options,
    );
  };
