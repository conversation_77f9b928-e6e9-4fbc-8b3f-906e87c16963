import { AxiosRequestConfig } from 'axios';

import { BrandsBranch } from '@/bundles/model';
import { IBrandBranchRepository } from '@/features/masterManagement/domain/repositories/brands-branch-repository.interface';

export const createBrandsBranch =
  (brandsBranchRepository: IBrandBranchRepository) =>
  async (
    brandsBranch?: BrandsBranch,
    options?: AxiosRequestConfig,
  ): Promise<BrandsBranch> => {
    return await brandsBranchRepository.createBrandsBranch(
      brandsBranch,
      options,
    );
  };
