import { AuthRequestGrantTypeEnum, AuthResponse } from '@/bundles/model';

import { LoginRequest } from '../domain/entities/auth-entity';
import { AuthRepository } from '../domain/repositories/auth-repository';

const login =
  (authRepository: AuthRepository) =>
  async (loginRequest: LoginRequest): Promise<AuthResponse> => {
    return await authRepository.login({
      username: loginRequest.email,
      password: loginRequest.password,
      grantType: AuthRequestGrantTypeEnum.Password,
    });
  };

export { login };
