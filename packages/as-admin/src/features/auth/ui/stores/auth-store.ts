import { createStore } from 'zustand';
import { devtools } from 'zustand/middleware';
import { shallow } from 'zustand/shallow';
import { useStoreWithEqualityFn } from 'zustand/traditional';

import { ExtractState, Params, User } from '@/types';

interface AuthStoreState {
  authUser?: User;
  actions: {
    updateAuthUser: (authUser?: User) => void;
  };
}

const authStore = createStore<AuthStoreState>()(
  devtools(
    (set) => ({
      authUser: undefined,
      actions: {
        updateAuthUser: (authUser?: User) => {
          set({
            authUser,
          });
        },
      },
    }),
    {
      name: 'auth-store',
    },
  ),
);

const useAuthStore = <U>(
  selector: Params<U, typeof authStore>[1],
  equalityFn?: Params<U, typeof authStore>[2],
) => {
  return useStoreWithEqualityFn(authStore, selector, equalityFn);
};

// Selectors
const authUserSelector = (state: ExtractState<typeof authStore>) =>
  state.authUser;

const authUserActionsSelector = (state: ExtractState<typeof authStore>) =>
  state.actions;

// Getters
const getAuthUser = () => authUserSelector(authStore.getState());

const getAuthUserAction = () => authUserActionsSelector(authStore.getState());

// Hooks
const useHookAuth = () => useAuthStore(authUserSelector, shallow);

export {
  // Getters
  getAuthUser,
  getAuthUserAction,
  // Hooks
  useHookAuth,
};
