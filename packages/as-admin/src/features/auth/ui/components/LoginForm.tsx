import LoginOutlinedIcon from '@mui/icons-material/LoginOutlined';
import { Divider, Paper, Typography } from '@mui/material';
import { makeStyles, createStyles } from '@mui/styles';
import { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import { ErrorResponse } from '@/bundles/model';
import { Button, Form } from '@/components/forms';
import { InputTextField } from '@/components/input-text';
import { EmailRegex, ErrorCode } from '@/constants';

import { useLogin } from '../hooks/use-login';
import { LoginValues } from '../types';

const useStyles = makeStyles(() =>
  createStyles({
    password: {
      '& .MuiInputBase-adornedEnd': {
        paddingRight: 'unset',
      },
    },
  }),
);

const ALLOW_SPECIAL_CHARACTER = '!"#$%&\'()*+,-./:;<=>?@[\\]^_`{|}~';

const loginSchema = z.object({
  email: z
    .string()
    .min(1, { message: 'auth.login.validate.emailIsRequired' })
    .refine((value) => !value || value === '' || EmailRegex.test(value), {
      message: 'auth.login.validate.emailNotValidFormat',
    }),
  password: z
    .string()
    .min(1, 'auth.passwordRecover.reset.validate.newPassword.isRequired')
    .regex(
      /\d/,
      'auth.passwordRecover.reset.validate.newPassword.shouldHaveNumber',
    )
    .regex(
      /[a-z]/,
      'auth.passwordRecover.reset.validate.newPassword.shouldHaveLowerLetter',
    )
    .regex(
      /[A-Z]/,
      'auth.passwordRecover.reset.validate.newPassword.shouldHaveUpperLetter',
    )
    .regex(
      /[!"#$%&'()*+,-./:;<=>?@[\\\]^_`{|}~]+/,
      'auth.passwordRecover.reset.validate.newPassword.shouldHaveSpecialCharacter',
    )
    .refine(
      (value) => value.length >= 8 && value.length <= 64,
      'auth.passwordRecover.reset.validate.newPassword.validLength',
    ),
});

export const LoginForm: React.FC = () => {
  const classes = useStyles();
  const { t } = useTranslation();
  const loginMutation = useLogin();
  const [loginError, setLoginError] = useState<string>('');
  const watchRef = useRef<any>(null);

  // Clear error when form values change
  useEffect(() => {
    if (!watchRef.current) return;

    const subscription = watchRef.current((values: LoginValues) => {
      if (loginError && (values.email || values.password)) {
        setLoginError('');
      }
    });

    return () => subscription.unsubscribe();
  }, [loginError]);

  const submitLogin = (formValues: LoginValues) => {
    // Clear previous error
    setLoginError('');

    loginMutation.mutate(
      {
        email: formValues.email,
        password: formValues.password,
      },
      {
        onError: (error) => {
          // Handle error and set error message for form display
          if (
            error.response &&
            error.response.data &&
            (error.response.data as ErrorResponse).code === ErrorCode.E000
          ) {
            setLoginError(
              t(
                `errorCode.custom.${(error.response.data as ErrorResponse).code}`,
              ),
            );
          } else {
            setLoginError(t('auth.login.toast.loginFailed'));
          }
        },
      },
    );
  };

  return (
    <Paper className="p-5 max-w-[448px] w-full" elevation={5}>
      <Form<typeof loginSchema, LoginValues>
        onSubmit={submitLogin}
        className="flex flex-col"
        schema={loginSchema}
        options={{
          mode: 'all',
        }}
      >
        {({ register, formState, watch, resetField }) => {
          // Set watch ref for useEffect
          watchRef.current = watch;

          const hasValueEmail =
            watch('email') !== '' && watch('email') !== undefined;
          const hasValuePassword =
            watch('password') !== '' && watch('password') !== undefined;

          const idDisable =
            Object.keys(formState.errors).length > 0 ||
            formState.isSubmitting ||
            !hasValueEmail ||
            !hasValuePassword ||
            !!loginError;

          const iconDisable = !idDisable ? '!fill-onPrimary' : '!fill-disable';
          const textDisable = !idDisable ? '!text-onPrimary' : '!text-disable';

          return (
            <>
              <Typography variant="h2" className="text-center">
                {t('auth.login.title')}
              </Typography>
              <Divider />
              <InputTextField
                id="email"
                hasValue={hasValueEmail}
                label={t('auth.login.input.email')}
                placeholder={t('auth.login.input.email')}
                error={!!formState.errors.email || !!loginError}
                helperText={t(formState.errors.email?.message || '')}
                registrationForm={register('email')}
                autoComplete="email"
                startIcon="person"
                onClear={() => {
                  resetField('email', {
                    defaultValue: '',
                  });
                  setLoginError('');
                }}
              />
              <InputTextField
                id="password"
                hasValue={hasValuePassword}
                label={t('auth.login.input.password')}
                placeholder={t('auth.login.input.password')}
                type="password"
                className={classes.password}
                error={!!formState.errors.password || !!loginError}
                helperText={t(formState.errors.password?.message || '', {
                  characters: ALLOW_SPECIAL_CHARACTER,
                })}
                registrationForm={register('password')}
                startIcon="lock"
                onClear={() => {
                  resetField('password', {
                    defaultValue: '',
                  });
                  setLoginError('');
                }}
              />
              {/* Display login error */}
              {loginError && (
                <Typography className="!text-error">{loginError}</Typography>
              )}
              <Button
                startIcon={
                  <LoginOutlinedIcon className={`${iconDisable} !pr-1`} />
                }
                type="submit"
                disabled={idDisable}
                className="w-full !h-12"
              >
                <Typography variant="h3" className={`${textDisable}`}>
                  {t('auth.login.button.login')}
                </Typography>
              </Button>
            </>
          );
        }}
      </Form>
    </Paper>
  );
};
