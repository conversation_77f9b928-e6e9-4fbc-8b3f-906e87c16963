import { Announcement } from '../../domain/announcement/types';

const now = new Date();
const tomorrow = new Date(now);
tomorrow.setDate(now.getDate() + 1);
const yesterday = new Date(now);
yesterday.setDate(now.getDate() - 1);

export const mockAnnouncements: Announcement[] = [
  {
    id: 'announcement-1',
    title: {
      ja: '新機能リリースのお知らせ',
      en: 'New Feature Release Announcement',
    },
    content: {
      ja: '<p>本日、素晴らしい新機能をリリースしました！詳細は<b>こちら</b>をご覧ください。</p>',
      en: '<p>We released an amazing new feature today! See <b>details</b> here.</p>',
    },
    status: 'published',
    category: 'campaign',
    targetCountries: ['JP', 'US', 'FR'],
    keyImage: {
      url: 'https://via.placeholder.com/300x200.png?text=Key+Image+1',
    },
    articleImages: [
      { url: 'https://via.placeholder.com/150.png?text=Article+Img+1A' },
      { url: 'https://via.placeholder.com/150.png?text=Article+Img+1B' },
    ],
    publicationDate: yesterday.toISOString(),
    createdAt: yesterday.toISOString(),
    updatedAt: now.toISOString(),
    isPublic: true,
  },
  {
    id: 'announcement-2',
    title: {
      ja: 'システムメンテナンス（下書き）',
      en: 'System Maintenance (Draft)',
    },
    content: {
      ja: '<p>今後のシステムメンテナンスについて通知します。</p>',
      en: '<p>We will notify you about upcoming system maintenance.</p>',
    },
    status: 'draft',
    category: 'important_notice',
    targetCountries: ['ALL'],
    publicationDate: tomorrow.toISOString(),
    createdAt: now.toISOString(),
    updatedAt: now.toISOString(),
    isPublic: false,
  },
];

let nextId = mockAnnouncements.length + 1;

export const getMockAnnouncements = (
  filters?: any,
): Promise<Announcement[]> => {
  console.log('Fetching announcements with filters:', filters);
  return Promise.resolve(mockAnnouncements);
};

export const getMockAnnouncementById = (
  id: string,
): Promise<Announcement | undefined> => {
  return Promise.resolve(mockAnnouncements.find((ann) => ann.id === id));
};

export const createMockAnnouncement = (
  data: Partial<Announcement>,
): Promise<Announcement> => {
  const newAnnouncement: Announcement = {
    id: `announcement-${nextId++}`,
    title: data.title || { ja: '新規タイトル', en: 'New Title' },
    content: data.content || {
      ja: '<p>新規コンテンツ</p>',
      en: '<p>New Content</p>',
    },
    status: data.status || 'draft',
    category: data.category || 'other',
    targetCountries: data.targetCountries || [],
    keyImage: data.keyImage,
    articleImages: data.articleImages || [],
    publicationDate: data.publicationDate || new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    isPublic: data.isPublic || false,
    ...data,
  } as Announcement;
  mockAnnouncements.push(newAnnouncement);
  return Promise.resolve(newAnnouncement);
};

export const updateMockAnnouncement = (
  id: string,
  data: Partial<Announcement>,
): Promise<Announcement> => {
  const index = mockAnnouncements.findIndex((ann) => ann.id === id);
  if (index === -1) {
    return Promise.reject(new Error('Announcement not found'));
  }
  mockAnnouncements[index] = {
    ...mockAnnouncements[index],
    ...data,
    updatedAt: new Date().toISOString(),
  };
  return Promise.resolve(mockAnnouncements[index]);
};

export const deleteMockAnnouncement = (id: string): Promise<void> => {
  const index = mockAnnouncements.findIndex((ann) => ann.id === id);
  if (index === -1) {
    return Promise.reject(new Error('Announcement not found'));
  }
  mockAnnouncements.splice(index, 1);
  return Promise.resolve();
};
