import { SpecialFeature } from '../../domain/specialFeature/types';

const now = new Date();
const tomorrow = new Date(now);
tomorrow.setDate(now.getDate() + 1);
const yesterday = new Date(now);
yesterday.setDate(now.getDate() - 2);
const oneWeekAgo = new Date(now);
oneWeekAgo.setDate(now.getDate() - 7);
const threeDaysAgo = new Date(now);
threeDaysAgo.setDate(now.getDate() - 3);

export const mockSpecialFeatures: SpecialFeature[] = [
  {
    id: 'sf-001',
    status: 'published',
    category: 'campaign',
    targetCountries: ['JP', 'US'],
    queryActionType: 'query',
    queryValue:
      "target='#sale', category='#electronics', brand='#sony', keyword='#new,#tv'",
    queryTarget: '#sale',
    queryCategory: '#electronics',
    queryBrand: '#sony',
    queryKeyword: '#new,#tv',
    translations: {
      en: {
        title: 'Summer Sale on Electronics!',
        content:
          '<p>Get the hottest deals on the latest electronics this summer. Up to <strong>50% off</strong> on select items!</p>',
        isPublicForLang: true,
        bannerImageUrl:
          'https://via.placeholder.com/1200x400.png?text=Summer+Sale+Banner+EN',
        bannerImageAltText: 'Summer Sale Banner - English',
        heroImageUrl:
          'https://via.placeholder.com/800x600.png?text=Featured+Electronics+Hero+EN',
        heroImageAltText:
          'Hero image showing various electronics on sale - English',
        relatedItems: [
          { id: 'tv-001', label: 'Sony Bravia XR A95L', checked: true },
          { id: 'cam-002', label: 'Canon EOS R5', checked: false },
          { id: 'ph-003', label: 'iPhone 17 Pro', checked: true },
        ],
      },
      ja: {
        title: '夏の家電セール開催中！',
        content:
          '<p>この夏、最新の家電製品をお得な価格で手に入れましょう。対象商品が最大<strong>50%オフ</strong>！</p>',
        isPublicForLang: true,
        bannerImageUrl:
          'https://via.placeholder.com/1200x400.png?text=夏のセールバナー+JA',
        bannerImageAltText: '夏のセールバナー - 日本語',
        heroImageUrl:
          'https://via.placeholder.com/800x600.png?text=注目家電ヒーロー+JA',
        heroImageAltText:
          'セール中の様々な家電製品を示すヒーローイメージ - 日本語',
        relatedItems: [
          { id: 'tv-001-ja', label: 'ソニー Bravia XR A95L', checked: true },
          { id: 'cam-002-ja', label: 'キヤノン EOS R5', checked: false },
          { id: 'ph-003-ja', label: 'iPhone 17 Pro', checked: true },
        ],
      },
    },
    publicationDate: yesterday.toISOString(),
    createdAt: oneWeekAgo.toISOString(),
    updatedAt: threeDaysAgo.toISOString(),
    isPublic: true,
  },
  {
    id: 'sf-002',
    status: 'draft',
    category: 'important_notice',
    targetCountries: ['ALL'],
    queryActionType: 'url',
    queryValue: 'https://example.com/maintenance-info',
    translations: {
      en: {
        title: 'Upcoming System Maintenance (Draft)',
        content:
          '<p>We will be performing system maintenance on <strong>June 15th, 2025, from 02:00 to 04:00 JST</strong>. The service may be temporarily unavailable.</p>',
        isPublicForLang: false, // It's a draft
        bannerImageUrl:
          'https://via.placeholder.com/1200x400.png?text=Maintenance+Banner+EN+(Draft)',
        bannerImageAltText: 'System Maintenance Banner - English (Draft)',
        heroImageUrl:
          'https://via.placeholder.com/800x600.png?text=Maintenance+Hero+EN+(Draft)',
        heroImageAltText: 'System Maintenance Hero - English (Draft)',
        relatedItems: [
          { id: 'info-link-1', label: 'More details here', checked: true },
        ],
      },
      ja: {
        title: 'システムメンテナンスのお知らせ（下書き）',
        content:
          '<p><strong>2025年6月15日 02:00～04:00 (JST)</strong>にシステムメンテナンスを実施します。期間中、サービスが一時的に利用できなくなる可能性があります。</p>',
        isPublicForLang: false,
        bannerImageUrl:
          'https://via.placeholder.com/1200x400.png?text=メンテナンスバナー+JA+(下書き)',
        bannerImageAltText: 'システムメンテナンスバナー - 日本語（下書き）',
        heroImageUrl:
          'https://via.placeholder.com/800x600.png?text=メンテナンスヒーロー+JA+(下書き)',
        heroImageAltText: 'システムメンテナンスヒーロー - 日本語（下書き）',
        relatedItems: [
          { id: 'info-link-1-ja', label: '詳細はこちら', checked: true },
        ],
      },
    },
    publicationDate: tomorrow.toISOString(), // Scheduled for future
    createdAt: now.toISOString(),
    updatedAt: now.toISOString(),
    isPublic: false,
  },
  {
    id: 'sf-003',
    status: 'published',
    category: 'campaign',
    targetCountries: ['FR', 'IT', 'ES'],
    queryActionType: 'shop',
    queryValue: 'shopId-european-fashion-boutique', // Example Shop ID
    translations: {
      en: {
        // English might be a fallback or for internal use
        title: 'New European Fashion Collection',
        content:
          '<p>Discover the latest trends from top European designers. Available now!</p>',
        isPublicForLang: true,
        bannerImageUrl:
          'https://via.placeholder.com/1200x400.png?text=Euro+Fashion+Banner+EN',
        bannerImageAltText: 'European Fashion Collection Banner - EN',
        heroImageUrl:
          'https://via.placeholder.com/800x600.png?text=Euro+Fashion+Hero+EN',
        heroImageAltText: 'Models showcasing new European fashion - EN',
        relatedItems: [
          {
            id: 'designer-A',
            label: 'Featured Designer: Chanel',
            checked: true,
          },
          {
            id: 'designer-B',
            label: 'Featured Designer: Gucci',
            checked: true,
          },
        ],
      },
      fr: {
        title: 'Nouvelle Collection de Mode Européenne',
        content:
          '<p>Découvrez les dernières tendances des meilleurs designers européens. Disponible dès maintenant !</p>',
        isPublicForLang: true,
        bannerImageUrl:
          'https://via.placeholder.com/1200x400.png?text=Mode+Europe+Bannière+FR',
        bannerImageAltText: 'Bannière Collection Mode Européenne - FR',
        heroImageUrl:
          'https://via.placeholder.com/800x600.png?text=Mode+Europe+Héros+FR',
        heroImageAltText:
          'Mannequins présentant la nouvelle mode européenne - FR',
        relatedItems: [
          {
            id: 'designer-A-fr',
            label: 'Créateur en vedette : Chanel',
            checked: true,
          },
          {
            id: 'designer-B-fr',
            label: 'Créateur en vedette : Gucci',
            checked: true,
          },
        ],
      },
      it: {
        title: 'Nuova Collezione di Moda Europea',
        content:
          '<p>Scopri le ultime tendenze dei migliori stilisti europei. Disponibile ora!</p>',
        isPublicForLang: true,
        bannerImageUrl:
          'https://via.placeholder.com/1200x400.png?text=Moda+Europea+Banner+IT',
        bannerImageAltText: 'Banner Collezione Moda Europea - IT',
        heroImageUrl:
          'https://via.placeholder.com/800x600.png?text=Moda+Europea+Eroe+IT',
        heroImageAltText: 'Modelle che presentano la nuova moda europea - IT',
        relatedItems: [
          {
            id: 'designer-A-it',
            label: 'Stilista in primo piano: Chanel',
            checked: true,
          },
          {
            id: 'designer-B-it',
            label: 'Stilista in primo piano: Gucci',
            checked: true,
          },
        ],
      },
    },
    publicationDate: oneWeekAgo.toISOString(),
    createdAt: new Date(oneWeekAgo.getTime() - 86400000 * 2).toISOString(), // 2 days before publication
    updatedAt: oneWeekAgo.toISOString(),
    isPublic: true,
  },
  {
    id: 'sf-004',
    status: 'archived',
    category: 'campaign',
    targetCountries: ['US', 'GB'],
    queryActionType: 'no_action', // No specific action, content is informational
    translations: {
      en: {
        title: 'Archived: Winter Wonderland Deals (2024)',
        content:
          '<p>This promotion has ended. Thank you for your participation!</p>',
        isPublicForLang: false, // Archived, so individual lang also not public
        bannerImageUrl:
          'https://via.placeholder.com/1200x400.png?text=Archived+Winter+Banner',
        bannerImageAltText: 'Archived Winter Wonderland Banner',
        heroImageUrl:
          'https://via.placeholder.com/800x600.png?text=Archived+Winter+Hero',
        heroImageAltText: 'Archived Winter Hero Image',
        relatedItems: [],
      },
    },
    publicationDate: new Date('2024-12-01T00:00:00Z').toISOString(),
    createdAt: new Date('2024-11-20T00:00:00Z').toISOString(),
    updatedAt: new Date('2025-01-15T00:00:00Z').toISOString(), // Date it was archived
    isPublic: false, // Overall status is not public because it's archived
  },
  {
    id: 'sf-005',
    status: 'published',
    category: 'important_notice',
    targetCountries: ['JP'],
    queryActionType: 'app_url',
    queryValue: 'myapp://feature/new_security_settings', // Example App URL
    translations: {
      ja: {
        title: '重要：セキュリティ設定更新のお願い',
        content:
          '<p>お客様のアカウント保護のため、新しいセキュリティ設定への更新をお願いいたします。詳細はアプリ内通知をご確認ください。</p>',
        isPublicForLang: true,
        bannerImageUrl:
          'https://via.placeholder.com/1200x400.png?text=セキュリティ更新バナー+JA',
        bannerImageAltText: 'セキュリティ更新のお願いバナー - 日本語',
        heroImageUrl:
          'https://via.placeholder.com/800x600.png?text=セキュリティヒーロー+JA',
        heroImageAltText: 'セキュリティ関連のヒーローイメージ - 日本語',
        relatedItems: [
          { id: 'security-faq', label: 'セキュリティFAQ', checked: true },
          { id: 'update-guide', label: '更新ガイド', checked: true },
        ],
      },
      en: {
        // Fallback/internal English version
        title: 'Important: Security Settings Update Request',
        content:
          '<p>To protect your account, please update to the new security settings. Check in-app notifications for details.</p>',
        isPublicForLang: false, // Only JA version is public-facing for this specific feature
        bannerImageUrl:
          'https://via.placeholder.com/1200x400.png?text=Security+Update+Banner+EN',
        bannerImageAltText: 'Security Update Request Banner - English',
        heroImageUrl:
          'https://via.placeholder.com/800x600.png?text=Security+Hero+EN',
        heroImageAltText: 'Security related hero image - English',
        relatedItems: [
          { id: 'security-faq-en', label: 'Security FAQ (EN)', checked: true },
          { id: 'update-guide-en', label: 'Update Guide (EN)', checked: true },
        ],
      },
    },
    publicationDate: threeDaysAgo.toISOString(),
    createdAt: oneWeekAgo.toISOString(),
    updatedAt: threeDaysAgo.toISOString(),
    isPublic: true,
  },
];

let nextIdCounter = mockSpecialFeatures.length + 1;

// Ensure these mock functions align with the new SpecialFeature structure if they directly manipulate the array.
// The getMockSpecialFeatures and getMockSpecialFeatureById should just return from the updated mockSpecialFeatures array.
// create/update/delete might need adjustments if they build objects from scratch.

export const getMockSpecialFeatures = (): Promise<SpecialFeature[]> => {
  console.log(
    'Fetching mock special features. Count:',
    mockSpecialFeatures.length,
  );
  return Promise.resolve(JSON.parse(JSON.stringify(mockSpecialFeatures))); // Return a deep copy
};

export const getMockSpecialFeatureById = (
  id: string,
): Promise<SpecialFeature | undefined> => {
  const feature = mockSpecialFeatures.find((sf) => sf.id === id);
  console.log(`Fetching mock special feature by ID: ${id}. Found:`, !!feature);
  return Promise.resolve(
    feature ? JSON.parse(JSON.stringify(feature)) : undefined,
  );
};

export const createMockSpecialFeature = (
  data: Partial<SpecialFeature>, // Data here should conform to SpecialFeature structure
): Promise<SpecialFeature> => {
  const newId = `sf-${String(nextIdCounter++).padStart(3, '0')}`;
  const nowISO = new Date().toISOString();

  const newSpecialFeature: SpecialFeature = {
    id: newId,
    status: data.status || 'draft',
    category: data.category || 'campaign',
    targetCountries: data.targetCountries || ['ALL'],
    queryActionType: data.queryActionType || 'no_action',
    queryValue: data.queryValue,
    queryTarget: data.queryTarget,
    queryCategory: data.queryCategory,
    queryBrand: data.queryBrand,
    queryKeyword: data.queryKeyword,
    translations: data.translations || {
      en: {
        title: 'New Draft Feature',
        content: '<p>Content coming soon.</p>',
        isPublicForLang: false,
        relatedItems: [],
      },
    },
    publicationDate:
      data.publicationDate || new Date(Date.now() + 86400000 * 7).toISOString(), // Default to one week from now
    createdAt: nowISO,
    updatedAt: nowISO,
    isPublic: data.isPublic || false, // Overall isPublic
    ...data, // Spread the rest of the partial data
  };
  mockSpecialFeatures.push(newSpecialFeature);
  console.log('Created mock special feature:', newSpecialFeature);
  return Promise.resolve(JSON.parse(JSON.stringify(newSpecialFeature)));
};

export const updateMockSpecialFeature = (
  id: string,
  data: Partial<SpecialFeature>,
): Promise<SpecialFeature> => {
  const index = mockSpecialFeatures.findIndex((sf) => sf.id === id);
  if (index === -1) {
    console.error(`Mock: Special feature with ID ${id} not found for update.`);
    return Promise.reject(new Error('SpecialFeature not found'));
  }
  mockSpecialFeatures[index] = {
    ...mockSpecialFeatures[index],
    ...data,
    updatedAt: new Date().toISOString(),
  };
  console.log('Updated mock special feature:', mockSpecialFeatures[index]);
  return Promise.resolve(
    JSON.parse(JSON.stringify(mockSpecialFeatures[index])),
  );
};

export const deleteMockSpecialFeature = (id: string): Promise<void> => {
  const initialLength = mockSpecialFeatures.length;
  const filtered = mockSpecialFeatures.filter((sf) => sf.id !== id);
  if (filtered.length < initialLength) {
    mockSpecialFeatures.length = 0; // Clear array
    mockSpecialFeatures.push(...filtered); // Add back remaining
    console.log(`Mock: Deleted special feature with ID ${id}.`);
    return Promise.resolve();
  } else {
    console.error(
      `Mock: Special feature with ID ${id} not found for deletion.`,
    );
    return Promise.reject(new Error('SpecialFeature not found for deletion'));
  }
};
