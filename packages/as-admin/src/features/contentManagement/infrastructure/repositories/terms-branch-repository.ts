import dayjs from 'dayjs';

import { TermsBranch } from '@/bundles/model';
import { CmsApi } from '@/bundles/model/apis/cms-api';
import { GetTermsBranchesParams } from '@/features/contentManagement/domain/terms/types';

const cmsApi = new CmsApi();

export const termsBranchRepository = () => ({
  /**
   * Fetches a list of terms branches based on type and pagination.
   */
  getTermsBranches: async (
    params: GetTermsBranchesParams,
  ): Promise<TermsBranch[]> => {
    const response = await cmsApi.getTermsBranches(
      params.type,
      params.page,
      params.limit,
    );
    return response.data;
  },

  /**
   * Fetches a single terms branch by its ID.
   */
  getTermsBranchById: async (id: string): Promise<TermsBranch> => {
    const response = await cmsApi.getTermsBranchById(id);
    return response.data;
  },

  /**
   * Creates a new terms branch.
   */
  createTermsBranch: async (
    data: Partial<TermsBranch>,
  ): Promise<TermsBranch> => {
    const response = await cmsApi.createTermsBranch(data);
    return response.data;
  },

  /**
   * Updates an existing terms branch.
   */
  updateTermsBranch: async (
    id: string,
    data: TermsBranch,
  ): Promise<TermsBranch> => {
    const response = await cmsApi.updateTermsBranch(id, data);
    return response.data;
  },

  /**
   * Deletes a terms branch.
   */
  deleteTermsBranch: async (id: string): Promise<void> => {
    await cmsApi.deleteTermsBranch(id);
  },

  /**
   * Publishes a terms branch.
   */
  publishTermsBranch: async (id: string, date: string): Promise<void> => {
    await cmsApi.publishTermsBranch(id, {
      publishedAt: dayjs(new Date(date)).format('YYYY-MM-DD'),
    });
  },

  /**
   * Unpublishes a terms branch.
   */
  unpublishTermsBranch: async (id: string): Promise<void> => {
    await cmsApi.unpublishTermsBranch(id);
  },
});
