import {
  GetListEmailResponse,
  NotificationSettingsType,
  UserApi,
} from '@/bundles/model';
import { ApiConfiguration } from '@/lib/swagger';

const userApi = new UserApi(new ApiConfiguration().configuration);

/**
 * Interface for the request parameters for the GET /emails endpoint.
 */
export interface GetEmailsRequest {
  notificationSettings?: NotificationSettingsType[];
  regionIds?: string[];
  countryCodes?: string[];
  languages?: string[];
}

/**
 * Interface for the email repository.
 */
export interface IEmailRepository {
  getEmails(params: GetEmailsRequest): Promise<GetListEmailResponse>;
}

/**
 * Fetches a list of emails from the API based on the provided filters.
 * @param params - The filter parameters for the request.
 * @returns A promise that resolves to the API response.
 */
export const getEmails = async (
  params: GetEmailsRequest,
): Promise<GetListEmailResponse> => {
  // Destructure params and pass as individual arguments
  const { notificationSettings, regionIds, countryCodes, languages } = params;
  const { data } = await userApi.getListEmails(
    notificationSettings ? notificationSettings[0] : undefined,
    regionIds,
    countryCodes,
    languages,
  );
  return data;
};

/**
 * Factory function to create an instance of the email repository.
 */
export const emailRepository = (): IEmailRepository => {
  return {
    getEmails,
  };
};
