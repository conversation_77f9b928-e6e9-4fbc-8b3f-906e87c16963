import { AxiosRequestConfig } from 'axios';

import { CmsApi, Content } from '@/bundles/model';

import { ContentSearchQuery } from '../../ui/types';
import { IContentRepository } from '../repositories/content-repository.interface';

const cmsApi = new CmsApi(undefined, undefined);

export const contentRepository = (): IContentRepository => {
  return {
    getContents: async (
      searchQuery: ContentSearchQuery,
      options?: AxiosRequestConfig,
    ) => {
      const response = await cmsApi.getContents(
        searchQuery.contentCategoryCode ?? '',
        searchQuery.page,
        searchQuery.limit,
        options,
      );
      return response.data;
    },
    getContentById: async (id: string, options?: AxiosRequestConfig) => {
      const response = await cmsApi.getContentById(id, options);
      return response.data;
    },
    createContent: async (content: Content, options?: AxiosRequestConfig) => {
      const response = await cmsApi.createContent(content, options);
      return response.data;
    },
    updateContent: async (
      id: string,
      content: Content,
      options?: AxiosRequestConfig,
    ) => {
      const response = await cmsApi.updateContent(id, content, options);
      return response.data;
    },
    deleteContent: async (id: string, options?: AxiosRequestConfig) => {
      await cmsApi.deleteContent(id, options);
    },
    getLanguages: async (options?: AxiosRequestConfig) => {
      const response = await cmsApi.getLanguages(options);
      return response.data;
    },
    getContentCategories: async (options?: AxiosRequestConfig) => {
      const response = await cmsApi.getContentCategories(options);
      return response.data;
    },
  };
};
