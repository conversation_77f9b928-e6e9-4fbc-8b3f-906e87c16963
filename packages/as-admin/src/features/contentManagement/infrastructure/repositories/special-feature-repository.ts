import {
  SearchBannersResponse,
  Banner,
  BannerActionType,
  BannerAction,
  BannerQuery,
  BannerTranslation,
} from '@/bundles/model';
import { dateFormat } from '@/constants';
import { DateFormat } from '@/constants/date';

import {
  SpecialFeature,
  SpecialFeatureFilters,
  SpecialFeatureFormData,
  MultilingualString,
  SpecialFeatureTranslationData,
} from '../../domain/specialFeature/types';
import {
  createBanner,
  deleteBanner,
  getBannerByID,
  getBanners,
  updateBanner,
} from '../api/banners-api';

import { ISpecialFeatureRepository } from './special-feature-repository.interface';

// Convert special feature form data to banner
const convertFormDataToBanner = (
  id: string | null,
  data: SpecialFeatureFormData,
): Banner => {
  // Convert special feature query fields to banner action
  const convertQueryToBannerAction = (
    data: SpecialFeatureFormData,
  ): BannerAction => {
    switch (data.queryActionType) {
      case 'query': {
        const query: BannerQuery = {};

        // Parse queryValue format: target='#,##', category='#,##', brand='#,##', keyword='#,##,##'
        if (data.queryValue) {
          const parseQueryValue = (queryValue: string) => {
            const result: any = {};

            // Regular expressions to match different parameter patterns
            const patterns = {
              target: /target\s*=\s*['"](.*?)['"]/gi,
              category: /category\s*=\s*['"](.*?)['"]/gi,
              brand: /brand\s*=\s*['"](.*?)['"]/gi,
              keyword: /keyword\s*=\s*['"](.*?)['"]/gi,
              minPrice: /minPrice\s*=\s*['"]*(\d+)['"]*\s*/gi,
              maxPrice: /maxPrice\s*=\s*['"]*(\d+)['"]*\s*/gi,
              condition: /condition\s*=\s*['"](.*?)['"]/gi,
              size: /size\s*=\s*['"](.*?)['"]/gi,
              country: /country\s*=\s*['"](.*?)['"]/gi,
              saleStatus: /saleStatus\s*=\s*['"](.*?)['"]/gi,
            };

            // Extract target values
            const targetMatch = patterns.target.exec(queryValue);
            if (targetMatch && targetMatch[1]) {
              result.targets = targetMatch[1]
                .split(',')
                .map((t) => t.trim())
                .filter((t) => t && t !== '#' && t !== '##');
            }

            // Reset regex lastIndex for reuse
            patterns.target.lastIndex = 0;

            // Extract category values
            const categoryMatch = patterns.category.exec(queryValue);
            if (categoryMatch && categoryMatch[1]) {
              result.categoryIds = categoryMatch[1]
                .split(',')
                .map((c) => c.trim())
                .filter((c) => c && c !== '#' && c !== '##');
            }
            patterns.category.lastIndex = 0;

            // Extract brand values
            const brandMatch = patterns.brand.exec(queryValue);
            if (brandMatch && brandMatch[1]) {
              result.brandNames = brandMatch[1]
                .split(',')
                .map((b) => b.trim())
                .filter((b) => b && b !== '#' && b !== '##');
            }
            patterns.brand.lastIndex = 0;

            // Extract keyword
            const keywordMatch = patterns.keyword.exec(queryValue);
            if (keywordMatch && keywordMatch[1]) {
              const keywordValue = keywordMatch[1].trim();
              if (
                keywordValue &&
                keywordValue !== '#' &&
                keywordValue !== '##' &&
                keywordValue !== '#,##,##'
              ) {
                result.keyword = keywordValue;
              }
            }
            patterns.keyword.lastIndex = 0;

            // Extract minPrice
            const minPriceMatch = patterns.minPrice.exec(queryValue);
            if (minPriceMatch && minPriceMatch[1]) {
              result.minPrice = parseInt(minPriceMatch[1], 10);
            }
            patterns.minPrice.lastIndex = 0;

            // Extract maxPrice
            const maxPriceMatch = patterns.maxPrice.exec(queryValue);
            if (maxPriceMatch && maxPriceMatch[1]) {
              result.maxPrice = parseInt(maxPriceMatch[1], 10);
            }
            patterns.maxPrice.lastIndex = 0;

            // Extract conditions
            const conditionMatch = patterns.condition.exec(queryValue);
            if (conditionMatch && conditionMatch[1]) {
              result.conditions = conditionMatch[1]
                .split(',')
                .map((c) => c.trim())
                .filter((c) => c && c !== '#' && c !== '##');
            }
            patterns.condition.lastIndex = 0;

            // Extract sizes
            const sizeMatch = patterns.size.exec(queryValue);
            if (sizeMatch && sizeMatch[1]) {
              result.sizes = sizeMatch[1]
                .split(',')
                .map((s) => s.trim())
                .filter((s) => s && s !== '#' && s !== '##');
            }
            patterns.size.lastIndex = 0;

            // Extract country codes
            const countryMatch = patterns.country.exec(queryValue);
            if (countryMatch && countryMatch[1]) {
              result.countryCodes = countryMatch[1]
                .split(',')
                .map((c) => c.trim())
                .filter((c) => c && c !== '#' && c !== '##');
            }
            patterns.country.lastIndex = 0;

            // Extract sale statuses
            const saleStatusMatch = patterns.saleStatus.exec(queryValue);
            if (saleStatusMatch && saleStatusMatch[1]) {
              result.saleStatuses = saleStatusMatch[1]
                .split(',')
                .map((s) => s.trim())
                .filter((s) => s && s !== '#' && s !== '##');
            }
            patterns.saleStatus.lastIndex = 0;

            return result;
          };

          const parsedQuery = parseQueryValue(data.queryValue);
          Object.assign(query, parsedQuery);
        }

        // Note: Individual fields are no longer used as fallback since we parse queryValue directly

        return {
          type: BannerActionType.Query,
          query,
        };
      }
      case 'url':
        return {
          type: BannerActionType.Url,
          url: data.queryValue,
        };

      case 'app_url':
        return {
          type: BannerActionType.InAppUrl,
          inAppUrl: data.queryValue,
        };

      case 'shop':
        return {
          type: BannerActionType.Shop,
          shop: data.queryValue,
        };

      case 'no_action':
      default:
        return {
          type: BannerActionType.NoAction,
        };
    }
  };

  // Convert special feature translations to banner translations
  const convertTranslationsToBanner = (
    translations: SpecialFeatureFormData['translations'],
  ): { [key: string]: BannerTranslation } => {
    const bannerTranslations: { [key: string]: BannerTranslation } = {};

    Object.keys(translations).forEach((langKey) => {
      const translation = translations[langKey as keyof MultilingualString];
      if (translation) {
        bannerTranslations[langKey] = {
          status: translation.isPublicForLang ? 'published' : 'draft',
          title: translation.title,
          position: [], // Default empty array as Banner doesn't map this directly
          bannerImageUrl:
            typeof translation.bannerFile === 'string'
              ? translation.bannerFile
              : undefined,
          heroImageUrl:
            typeof translation.heroFile === 'string'
              ? translation.heroFile
              : undefined,
        };
      }
    });

    return bannerTranslations;
  };

  const action = convertQueryToBannerAction(data);
  const bannerTranslations = convertTranslationsToBanner(data.translations);

  return {
    ...(id && { id }),
    status: data.status === 'published' ? 'published' : 'draft',
    action,
    publishDate: dateFormat(
      data.publicationDate.toString(),
      DateFormat.fullDateWithHyphen,
    ),
    countryCodes: data.targetCountries,
    bannerTranslations,
  };
};

const convertBannerToSpecialFeature = (banner: Banner): SpecialFeature => {
  // Map banner action to special feature query fields
  const mapBannerActionToQuery = (banner: Banner) => {
    const action = banner.action;
    if (!action) {
      return {
        queryActionType: 'no_action' as const,
      };
    }

    switch (action.type) {
      case BannerActionType.Query: {
        // Convert BannerQuery object back to queryValue string format
        const convertQueryToString = (query?: BannerQuery): string => {
          if (!query) return '';

          const parts: string[] = [];

          // Convert targets array to string
          if (query.targets && query.targets.length > 0) {
            parts.push(`target='${query.targets.join(',')}'`);
          }

          // Convert categoryIds array to string
          if (query.categoryIds && query.categoryIds.length > 0) {
            parts.push(`category='${query.categoryIds.join(',')}'`);
          }

          // Convert brandNames array to string
          if (query.brandNames && query.brandNames.length > 0) {
            parts.push(`brand='${query.brandNames.join(',')}'`);
          }

          // Convert keyword to string
          if (query.keyword) {
            parts.push(`keyword='${query.keyword}'`);
          }

          // Convert minPrice to string
          if (query.minPrice !== undefined) {
            parts.push(`minPrice='${query.minPrice}'`);
          }

          // Convert maxPrice to string
          if (query.maxPrice !== undefined) {
            parts.push(`maxPrice='${query.maxPrice}'`);
          }

          // Convert conditions array to string
          if (query.conditions && query.conditions.length > 0) {
            parts.push(`condition='${query.conditions.join(',')}'`);
          }

          // Convert sizes array to string
          if (query.sizes && query.sizes.length > 0) {
            parts.push(`size='${query.sizes.join(',')}'`);
          }

          // Convert countryCodes array to string
          if (query.countryCodes && query.countryCodes.length > 0) {
            parts.push(`country='${query.countryCodes.join(',')}'`);
          }

          // Convert saleStatuses array to string
          if (query.saleStatuses && query.saleStatuses.length > 0) {
            parts.push(`saleStatus='${query.saleStatuses.join(',')}'`);
          }

          return parts.join(', ');
        };

        return {
          queryActionType: 'query' as const,
          queryValue: convertQueryToString(action.query),
        };
      }
      case BannerActionType.Url:
        return {
          queryActionType: 'url' as const,
          queryValue: action.url,
        };
      case BannerActionType.InAppUrl:
        return {
          queryActionType: 'app_url' as const,
          queryValue: action.inAppUrl,
        };
      case BannerActionType.Shop:
        return {
          queryActionType: 'shop' as const,
          queryValue: action.shop,
        };
      case BannerActionType.NoAction:
      default:
        return {
          queryActionType: 'no_action' as const,
        };
    }
  };

  // Convert banner translations to special feature translations
  const convertTranslations = (bannerTranslations?: { [key: string]: any }) => {
    const translations: Partial<
      Record<keyof MultilingualString, SpecialFeatureTranslationData>
    > = {};

    if (bannerTranslations) {
      Object.keys(bannerTranslations).forEach((langKey) => {
        const key = langKey as keyof MultilingualString;
        const bannerTranslation = bannerTranslations[langKey];

        if (bannerTranslation) {
          translations[key] = {
            title: bannerTranslation.title || '',
            content: '', // Banner doesn't have content, so default to empty
            isPublicForLang: bannerTranslation.status === 'published',
            bannerImageUrl: bannerTranslation.bannerImageUrl,
            bannerImageAltText: '', // Banner doesn't have alt text, so default to empty
            heroImageUrl: bannerTranslation.heroImageUrl,
            heroImageAltText: '', // Banner doesn't have alt text, so default to empty
            relatedItems: [], // Banner doesn't have related items, so default to empty
          };
        }
      });
    }

    return translations;
  };

  const queryFields = mapBannerActionToQuery(banner);
  const translations = convertTranslations(banner.bannerTranslations);

  // Get primary language for fallback images
  const primaryTranslation =
    translations.en || translations.ja || Object.values(translations)[0];

  return {
    id: banner.id || '',
    status:
      banner.status === 'published'
        ? 'published'
        : banner.status === 'draft'
          ? 'draft'
          : 'archived',
    category: 'campaign', // Default to campaign since Banner doesn't have category
    targetCountries: banner.countryCodes || [],
    publicationDate: banner.publishDate || new Date().toISOString(),
    createdAt: banner.createdAt || new Date().toISOString(),
    updatedAt: banner.updatedAt || new Date().toISOString(),
    isPublic: banner.status === 'published',
    translations,
    ...queryFields,
    // Set fallback images from primary translation if available
    ...(primaryTranslation?.bannerImageUrl && {
      keyImage: {
        url: primaryTranslation.bannerImageUrl,
        altText: translations.en?.bannerImageAltText
          ? { en: translations.en.bannerImageAltText }
          : undefined,
      },
    }),
    ...(primaryTranslation?.heroImageUrl && {
      articleImages: [
        {
          url: primaryTranslation.heroImageUrl,
          altText: translations.en?.heroImageAltText
            ? { en: translations.en.heroImageAltText }
            : undefined,
        },
      ],
    }),
  };
};

// Convert banners API response to special features
const convertBannersToSpecialFeatures = (
  bannersResponse: SearchBannersResponse,
): SpecialFeature[] => {
  if (!bannersResponse.data) {
    return [];
  }

  return bannersResponse.data.map((banner: Banner): SpecialFeature => {
    return convertBannerToSpecialFeature(banner);
  });
};

export const specialFeatureRepository = (): ISpecialFeatureRepository => {
  return {
    getSpecialFeatures: async (filters: SpecialFeatureFilters) => {
      console.log('Fetching specialFeatures with filters:', filters);
      const banners = await getBanners(filters.page, filters.limit);
      const specialFeatures = convertBannersToSpecialFeatures(banners);
      return specialFeatures;
    },
    getSpecialFeatureById: async (id: string) => {
      console.log('Fetching specialFeature by ID:', id);
      const banner = await getBannerByID(id);
      const specialFeature = convertBannerToSpecialFeature(banner);
      return specialFeature;
    },
    createSpecialFeature: async (data: SpecialFeatureFormData) => {
      console.log('Creating specialFeature with data:', data);
      const banner = convertFormDataToBanner(null, data);
      const res = await createBanner(banner);
      const specialFeature = convertBannerToSpecialFeature(res);
      return specialFeature;
    },
    updateSpecialFeature: async (id: string, data: SpecialFeatureFormData) => {
      console.log('Updating specialFeature with ID:', id, 'and data:', data);
      const banner = convertFormDataToBanner(id, data);
      return await updateBanner(id, banner);
    },
    deleteSpecialFeature: async (id: string) => {
      console.log('Deleting specialFeature with ID:', id);
      return await deleteBanner(id);
    },
  };
};
