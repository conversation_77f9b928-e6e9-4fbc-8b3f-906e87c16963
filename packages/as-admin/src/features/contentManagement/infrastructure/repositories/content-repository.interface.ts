import { AxiosRequestConfig } from 'axios';

import { Content, Language, ContentCategory } from '@/bundles/model';

import { ContentSearchQuery } from '../../ui/types';

export interface IContentRepository {
  getContents(
    searchQuery: ContentSearchQuery,
    options?: AxiosRequestConfig,
  ): Promise<Content[]>;
  getContentById(id: string, options?: AxiosRequestConfig): Promise<Content>;
  createContent(
    content: Content,
    options?: AxiosRequestConfig,
  ): Promise<Content>;
  updateContent(
    id: string,
    content: Content,
    options?: AxiosRequestConfig,
  ): Promise<void>;
  deleteContent(id: string, options?: AxiosRequestConfig): Promise<void>;
  getLanguages(options?: AxiosRequestConfig): Promise<Language[]>;
  getContentCategories(
    options?: AxiosRequestConfig,
  ): Promise<ContentCategory[]>;
}
