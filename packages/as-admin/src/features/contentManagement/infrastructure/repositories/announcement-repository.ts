import { AxiosRequestConfig } from 'axios';

import { Notification, GetNotificationsResponse } from '@/bundles/model';
import { CmsApi } from '@/bundles/model/apis/cms-api';
import { IAnnouncementRepository } from '@/features/contentManagement/domain/announcement/announcement-repository.interface';
import { ApiConfiguration } from '@/lib/swagger';

const cmsApi = new CmsApi(new ApiConfiguration().configuration);

export const announcementRepository = (): IAnnouncementRepository => {
  return {
    getAnnouncements: async (
      page?: number,
      limit?: number,
      options?: AxiosRequestConfig,
    ): Promise<GetNotificationsResponse> => {
      const response = await cmsApi.getNotifications(page, limit, options);
      return response.data;
    },
    getAnnouncementById: async (
      id: string,
      options?: AxiosRequestConfig,
    ): Promise<Notification> => {
      const response = await cmsApi.getNotificationById(id, options);
      return response.data;
    },
    createAnnouncement: async (
      notification: Notification,
      options?: AxiosRequestConfig,
    ): Promise<Notification> => {
      const response = await cmsApi.createNotification(notification, options);
      return response.data;
    },
    updateAnnouncement: async (
      id: string,
      notification: Notification,
      options?: AxiosRequestConfig,
    ): Promise<Notification> => {
      const response = await cmsApi.updateNotification(
        id,
        notification,
        options,
      );
      return response.data;
    },
    deleteAnnouncement: async (
      id: string,
      options?: AxiosRequestConfig,
    ): Promise<void> => {
      await cmsApi.deleteNotification(id, options);
    },
  };
};
