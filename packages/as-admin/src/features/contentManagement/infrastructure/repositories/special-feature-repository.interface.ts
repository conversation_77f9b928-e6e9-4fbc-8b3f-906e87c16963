import { AxiosRequestConfig } from 'axios';

import {
  SpecialFeature,
  SpecialFeatureFilters,
  SpecialFeatureFormData,
} from '../../domain/specialFeature/types';

export interface ISpecialFeatureRepository {
  getSpecialFeatures(
    filters?: SpecialFeatureFilters,
    options?: AxiosRequestConfig,
  ): Promise<SpecialFeature[]>;
  getSpecialFeatureById(id: string): Promise<SpecialFeature | undefined>;
  createSpecialFeature(data: SpecialFeatureFormData): Promise<SpecialFeature>;
  updateSpecialFeature(
    id: string,
    data: Partial<SpecialFeatureFormData>,
  ): Promise<void>;
  deleteSpecialFeature(id: string): Promise<void>;
}
