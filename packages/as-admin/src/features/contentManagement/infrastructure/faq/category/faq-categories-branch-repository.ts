import { CmsApi } from '@/bundles/model/apis/cms-api';
import { FaqCategoriesBranch } from '@/bundles/model/models/faq-categories-branch';
import { Pagination } from '@/bundles/model/models/pagination';

const cmsApi = new CmsApi(undefined, undefined);

export const faqCategoriesBranchRepository = () => ({
  getFaqCategoriesBranches: async (params?: Pagination) => {
    const response = await cmsApi.getFaqCategoriesBranches(
      params?.page,
      params?.limit,
    );
    return response.data;
  },
  getFaqCategoriesBranchById: async (id: string) => {
    const response = await cmsApi.getFaqCategoriesBranchById(id);
    return response.data;
  },
  createFaqCategoriesBranch: async (
    faqCategoriesBranch: FaqCategoriesBranch,
  ) => {
    const response =
      await cmsApi.createFaqCategoriesBranch(faqCategoriesBranch);
    return response.data;
  },
});
