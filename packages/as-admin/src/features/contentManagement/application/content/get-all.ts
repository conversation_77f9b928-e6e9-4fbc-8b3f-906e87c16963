import { AxiosRequestConfig } from 'axios';

import { Content } from '@/bundles/model';

import { IContentRepository } from '../../infrastructure/repositories/content-repository.interface';
import { ContentSearchQuery } from '../../ui/types';

export const getContents =
  (repository: IContentRepository) =>
  async (
    searchQuery: ContentSearchQuery,
    options?: AxiosRequestConfig,
  ): Promise<Content[]> => {
    return repository.getContents(searchQuery, options);
  };
