import { AxiosRequestConfig } from 'axios';

import { Content } from '@/bundles/model';

import { IContentRepository } from '../../infrastructure/repositories/content-repository.interface';

export const updateContent =
  (repository: IContentRepository) =>
  async (
    id: string,
    content: Content,
    options?: AxiosRequestConfig,
  ): Promise<void> => {
    return repository.updateContent(id, content, options);
  };
