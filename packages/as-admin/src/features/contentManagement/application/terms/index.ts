import { TermsBranch } from '@/bundles/model';
import { GetTermsBranchesParams } from '@/features/contentManagement/domain/terms/types';
import { termsBranchRepository } from '@/features/contentManagement/infrastructure/repositories/terms-branch-repository';

type TermsRepository = ReturnType<typeof termsBranchRepository>;

export const getTermsBranchesUseCase =
  (repo: TermsRepository) => async (params: GetTermsBranchesParams) => {
    return await repo.getTermsBranches(params);
  };

export const createTermsBranchUseCase =
  (repo: TermsRepository) => async (data: Partial<TermsBranch>) => {
    return await repo.createTermsBranch(data);
  };

export const updateTermsBranchUseCase =
  (repo: TermsRepository) => async (id: string, data: TermsBranch) => {
    return await repo.updateTermsBranch(id, data);
  };

export const publishTermsBranchUseCase =
  (repo: TermsRepository) => async (id: string, date: string) => {
    return await repo.publishTermsBranch(id, date);
  };

// Add this export
export const unpublishTermsBranchUseCase =
  (repo: TermsRepository) => async (id: string) => {
    return await repo.unpublishTermsBranch(id);
  };

export const deleteTermsBranchUseCase =
  (repo: TermsRepository) => async (id: string) => {
    return await repo.deleteTermsBranch(id);
  };
