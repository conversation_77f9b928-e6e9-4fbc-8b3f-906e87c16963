import { GetNotificationsResponse } from '@/bundles/model';
import { IAnnouncementRepository } from '@/features/contentManagement/domain/announcement/announcement-repository.interface';

export const getAnnouncements =
  (announcementRepository: IAnnouncementRepository) =>
  async (page?: number, limit?: number): Promise<GetNotificationsResponse> => {
    return await announcementRepository.getAnnouncements(page, limit);
  };
