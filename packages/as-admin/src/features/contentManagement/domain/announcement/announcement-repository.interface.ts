import { AxiosRequestConfig } from 'axios';

import { Notification, GetNotificationsResponse } from '@/bundles/model';

export interface IAnnouncementRepository {
  getAnnouncements(
    page?: number,
    limit?: number,
    options?: AxiosRequestConfig,
  ): Promise<GetNotificationsResponse>;
  getAnnouncementById(
    id: string,
    options?: AxiosRequestConfig,
  ): Promise<Notification>;
  createAnnouncement(
    notification: Notification,
    options?: AxiosRequestConfig,
  ): Promise<Notification>;
  updateAnnouncement(
    id: string,
    notification: Notification,
    options?: AxiosRequestConfig,
  ): Promise<Notification>;
  deleteAnnouncement(id: string, options?: AxiosRequestConfig): Promise<void>;
}
