export interface DisplayName {
  [languageCode: string]: string;
}

export interface FaqCategory {
  id: string;
  displayName: DisplayName;
  active: boolean;
  count: number;

  subCategories: string[] | FaqCategory[];

  children?: FaqCategory[];
  parentId?: string | null;

  // Additional fields that may exist in API response
  createdAt?: string;
  updatedAt?: string;
  status?: 'draft' | 'published' | string;
}

export interface FaqCategoriesBranch {
  id: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string | null;
  status: 'draft' | 'published' | string;
  faqCategories: FaqCategory[];
}

export type Category = FaqCategory;
