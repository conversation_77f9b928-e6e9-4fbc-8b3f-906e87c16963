import { useNavigate } from 'react-router-dom';

import { But<PERSON> } from '@/components/forms/button';
import { Head } from '@/components/seo/head';
import { routes } from '@/routes/routes';

export const ContentManagementSpecialFeatureDelete = () => {
  const navigate = useNavigate();
  const router = `${routes.contentManagement.index}${routes.contentManagement.specialFeature.index}${routes.contentManagement.specialFeature.addOrEdit.index}`;

  return (
    <>
      <Head title="記事削除" />
      <div className="flex flex-col justify-center items-center h-screen bg-white">
        <h1 className="text-6xl font-bold mb-4">
          {'9-4-1/003 記事削除 - Delete Article'}
        </h1>
        <Button onClick={() => navigate(router)}>
          {
            'Go back to the previous page 記事詳細追加編集 - Add/Edit Article Details'
          }
        </Button>
      </div>
    </>
  );
};
