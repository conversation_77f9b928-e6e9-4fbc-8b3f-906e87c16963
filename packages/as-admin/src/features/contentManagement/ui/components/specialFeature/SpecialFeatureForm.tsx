import {
  AddPhotoAlternate,
  Check,
  DeleteOutline as Delete<PERSON><PERSON>,
  Edit as EditIcon,
  Search,
} from '@mui/icons-material';
import {
  Box,
  Button as MuiButton,
  Checkbox,
  Chip,
  FormControlLabel,
  Grid,
  IconButton,
  Paper,
  Radio,
  RadioGroup,
  Tab,
  Tabs,
  TextField,
  Typography,
  InputAdornment,
} from '@mui/material';
import { format, isValid, parseISO } from 'date-fns';
import React, { ChangeEvent, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import {
  MultilingualString,
  QueryActionType,
  SpecialFeature,
  SpecialFeatureFormData,
  SpecialFeatureFormDataTranslation,
} from '@/features/contentManagement/domain/specialFeature/types';
import { CloseIcon } from '@/features/userManagement/ui/components';
import { getImagePreviewUrl } from '@/utils/image-utils';

import { SpecialFeaturePreviewModal } from './SpecialFeaturePreviewModal';

const availableLanguages: Array<{
  code: keyof MultilingualString;
  name: string;
}> = [
  { code: 'en', name: 'EN' },
  { code: 'es', name: 'ES' },
  { code: 'fr', name: 'FR' },
  { code: 'it', name: 'IT' },
  { code: 'ja', name: 'JA' },
  { code: 'ko', name: 'KO' },
  { code: 'zhCN', name: 'zhCN' },
];

// TODO: Change to master country from DB
const allDisplayCountries = [
  { code: 'AUS', name: 'Australia' },
  { code: 'FRA', name: 'France' },
  { code: 'HKG', name: 'Hong Kong' },
  { code: 'ITA', name: 'Italy' },
  { code: 'JPN', name: 'Japan' },
  { code: 'MCO', name: 'Monaco' },
  { code: 'SAU', name: 'Saudi Arabia' },
  { code: 'SGP', name: 'Singapore' },
  { code: 'KOR', name: 'South Korea' },
  { code: 'ESP', name: 'Spain' },
  { code: 'ARE', name: 'United Arab Emirates' },
  { code: 'GBR', name: 'United Kingdom' },
  { code: 'USA', name: 'United States' },
];

const queryActionTypeOptions: Array<{
  value: QueryActionType;
  labelKey: string;
  defaultLabel: string;
}> = [
  {
    value: 'query',
    labelKey: 'specialFeature.queryAction.query',
    defaultLabel: 'Query',
  },
  {
    value: 'url',
    labelKey: 'specialFeature.queryAction.url',
    defaultLabel: 'URL',
  },
  {
    value: 'app_url',
    labelKey: 'specialFeature.queryAction.appUrl',
    defaultLabel: 'アプリ内URL',
  },
  {
    value: 'shop',
    labelKey: 'specialFeature.queryAction.shop',
    defaultLabel: 'ショップ',
  },
  {
    value: 'no_action',
    labelKey: 'specialFeature.queryAction.noAction',
    defaultLabel: 'No Action',
  },
];

const formatDate = (dateString?: string | Date): string => {
  if (!dateString) return '-';
  const date =
    typeof dateString === 'string' ? parseISO(dateString) : dateString;
  return isValid(date) ? format(date, 'yyyy.MM.dd HH:mm') : '-';
};

interface SpecialFeatureFormProps {
  specialFeature: SpecialFeature;
  handleHeaderAction: () => void;
  isEditing: boolean;
  formValues: SpecialFeatureFormData;
  setFormValue: (fieldName: string, value: any) => void;
}

export const ContentManagementSpecialFeatureForm: React.FC<
  SpecialFeatureFormProps
> = ({
  specialFeature,
  handleHeaderAction,
  isEditing,
  formValues,
  setFormValue,
}) => {
  const { t } = useTranslation();
  const [activeLangTab, setActiveLangTab] =
    useState<keyof MultilingualString>('en');
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);
  const [errors, setErrors] = useState<Record<string, string | undefined>>({});

  const validateField = (fieldPath: string, value: string) => {
    let message: string | undefined;

    if (fieldPath.includes('.title')) {
      if (value.length > 300) {
        message = t(
          'common.validation.maxLength',
          'Title cannot exceed 300 characters.',
          { count: 300 },
        );
      }
    }
    setErrors((prev) => ({ ...prev, [fieldPath]: message }));
  };

  const hasErrors = useMemo(
    () => Object.values(errors).some((error) => !!error),
    [errors],
  );

  const handleTabChange = (
    _event: React.SyntheticEvent,
    newValue: keyof MultilingualString,
  ) => {
    setActiveLangTab(newValue);
  };

  const handleInputChange = useCallback(
    (fieldPath: string, value: any) => {
      setFormValue(fieldPath, value);
    },
    [setFormValue],
  );

  const handleTranslationInputChange = <
    K extends keyof SpecialFeatureFormDataTranslation,
  >(
    lang: keyof MultilingualString,
    field: K,
    value: SpecialFeatureFormDataTranslation[K],
  ) => {
    const fieldPath = `translations.${lang}.${field}`;
    handleInputChange(fieldPath, value);
    if (typeof value === 'string') {
      validateField(fieldPath, value);
    }
  };

  const handleImageUpload = (
    event: ChangeEvent<HTMLInputElement>,
    lang: keyof MultilingualString,
    imageType: 'bannerFile' | 'heroFile',
  ) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      handleInputChange(`translations.${lang}.${imageType}`, file);
    }
  };

  const removeImage = (
    lang: keyof MultilingualString,
    imageType: 'bannerFile' | 'heroFile',
  ) => {
    handleInputChange(`translations.${lang}.${imageType}`, undefined);
    if (imageType === 'bannerFile') {
      handleInputChange(`translations.${lang}.bannerAltText`, '');
    } else if (imageType === 'heroFile') {
      handleInputChange(`translations.${lang}.heroAltText`, '');
    }
  };

  const getPreviewUrl = getImagePreviewUrl;

  const getRelatedItemsForLang = (
    lang: keyof MultilingualString,
  ): { id: string; label: string; checked: boolean }[] => {
    const items = formValues.translations?.[lang]?.relatedItems;
    if (items) return items;
    return [
      {
        id: `related-${lang}-1`,
        label: t('specialFeature.relatedItemDefault1', 'あのイーハ１'),
        checked: false,
      },
      {
        id: `related-${lang}-2`,
        label: t('specialFeature.relatedItemDefault2', 'あのイーハ２'),
        checked: false,
      },
      {
        id: `related-${lang}-3`,
        label: t('specialFeature.relatedItemDefault3', 'あのイーハ３'),
        checked: false,
      },
    ];
  };

  const handleRelatedItemChange = (
    lang: keyof MultilingualString,
    itemId: string,
    checked: boolean,
  ) => {
    const currentItems = getRelatedItemsForLang(lang);
    const updatedItems = currentItems.map((item) =>
      item.id === itemId ? { ...item, checked } : item,
    );
    handleInputChange(`translations.${lang}.relatedItems`, updatedItems);
  };

  const headerButtonLabel = useMemo(() => {
    if (!isEditing) return t('common.edit', '編集');
    return t('common.done', '完了');
  }, [isEditing, t]);

  const currentTitleForDisplayOrEdit = useMemo(() => {
    if (isEditing) {
      return formValues.translations?.['en']?.title ?? '';
    }
    return (
      specialFeature.translations?.['en']?.title ||
      t('specialFeature.untitled', 'Untitled Special Feature')
    );
  }, [specialFeature, formValues.translations, isEditing, t]);

  const currentQueryActionType = formValues.queryActionType || 'query';

  const handleOpenPreviewModal = () => {
    setIsPreviewModalOpen(true);
  };

  const handleClosePreviewModal = () => {
    setIsPreviewModalOpen(false);
  };

  return (
    <>
      <Paper elevation={0} sx={{ m: { xs: 0 } }}>
        <Box
          display="flex"
          justifyContent="space-between"
          alignItems="flex-end"
          flexWrap="wrap"
          mb={2}
          pb={2}
          sx={{ borderBottom: 2, borderColor: 'divider' }}
        >
          {isEditing ? (
            <TextField
              fullWidth
              multiline
              label={t('specialFeature.titleLabelFull', '特集タイトル')}
              variant="outlined"
              value={formValues.translations?.['en']?.title ?? ''}
              onChange={(e) =>
                handleTranslationInputChange('en', 'title', e.target.value)
              }
              error={!!errors['translations.en.title']}
              helperText={
                <Box
                  component="span"
                  sx={{ display: 'flex', justifyContent: 'space-between' }}
                >
                  <span>{errors['translations.en.title']}</span>
                  <span>
                    {formValues.translations?.['en']?.title?.length || 0} / 300
                  </span>
                </Box>
              }
              InputProps={{
                endAdornment: formValues.translations?.[activeLangTab]
                  ?.title && (
                  <InputAdornment position="start">
                    <CloseIcon
                      onClick={() =>
                        handleTranslationInputChange(activeLangTab, 'title', '')
                      }
                      className="cursor-pointer mr-2 !fill-onSurface"
                    />
                  </InputAdornment>
                ),
              }}
              sx={{
                flexGrow: 1,
                mr: { sm: 2 },
                mb: { xs: 1, sm: 0 },
                maxWidth: 'calc(100% - 150px)',
              }}
            />
          ) : (
            <Typography
              variant="h1"
              component="h1"
              gutterBottom
              sx={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                flexGrow: 1,
                maxWidth: 'calc(100% - 150px)',
              }}
              title={currentTitleForDisplayOrEdit}
            >
              {currentTitleForDisplayOrEdit}
            </Typography>
          )}
          <Box
            display="flex"
            gap={1}
            alignItems="center"
            sx={{ flexShrink: 0 }}
          >
            {specialFeature.status === 'draft' && !isEditing && (
              <Chip
                label={t('specialFeature.status.draft', '下書き')}
                color="default"
                sx={{
                  backgroundColor: '#49454F',
                  color: '#F4EFF4',
                  height: '40px',
                }}
              />
            )}
            <MuiButton
              variant="contained"
              size="medium"
              onClick={handleHeaderAction}
              startIcon={isEditing ? <Check /> : <EditIcon />}
              disabled={isEditing && hasErrors}
              sx={{
                height: '40px',
                minWidth: '100px',
                backgroundColor: isEditing ? 'secondary.main' : '#DBE4E3',
                color: isEditing ? 'common.white' : '#00201F',
                '&:hover': {
                  backgroundColor: isEditing ? 'secondary.dark' : '#CADEDA',
                },
              }}
            >
              {headerButtonLabel}
            </MuiButton>
          </Box>
        </Box>

        <Grid
          container
          spacing={2}
          sx={{
            color: 'text.secondary',
            fontSize: '0.875rem',
            mb: 1,
          }}
        >
          <Grid item xs={12} sm="auto" display="flex" alignItems="center">
            <Typography variant="caption" sx={{ mr: 0.5 }}>
              <span className="font-bold">
                {t('common.createdAt', '作成日')}:
              </span>
            </Typography>
            <Typography variant="caption">
              {formatDate(specialFeature.createdAt)}
            </Typography>
          </Grid>
          <Grid item xs={12} sm="auto" display="flex" alignItems="center">
            <Typography variant="caption" sx={{ mr: 0.5 }}>
              <span className="font-bold">
                {t('common.updatedAt', '最終更新日')}:
              </span>
            </Typography>
            <Typography variant="caption">
              {isEditing ? '--' : formatDate(specialFeature.updatedAt)}
            </Typography>
          </Grid>
          <Grid item xs={12} sm="auto" display="flex" alignItems="center">
            <Typography variant="caption" sx={{ mr: 0.5 }}>
              <span className="font-bold">
                {t('common.publicationDate', '更新適用日')}:
              </span>
            </Typography>
            <Typography variant="caption">
              {specialFeature.status !== 'draft' && !isEditing
                ? formatDate(specialFeature.publicationDate)
                : '--'}
            </Typography>
          </Grid>
        </Grid>

        <Paper elevation={0} sx={{ p: 2 }}>
          <FormControlLabel
            control={
              <RadioGroup
                row
                value={
                  isEditing
                    ? formValues.queryActionType
                    : specialFeature.queryActionType
                }
                name="query-action-type-group"
                onChange={(e) =>
                  isEditing &&
                  handleInputChange(
                    'queryActionType',
                    e.target.value as QueryActionType,
                  )
                }
              >
                {queryActionTypeOptions.map((opt) => (
                  <FormControlLabel
                    key={opt.value}
                    value={opt.value}
                    control={<Radio readOnly={!isEditing} />}
                    label={t(opt.labelKey, opt.defaultLabel)}
                    disabled={!isEditing}
                  />
                ))}
              </RadioGroup>
            }
            label=""
          />
        </Paper>

        <Paper
          elevation={0}
          sx={{ p: 2, mb: 2, backgroundColor: 'grey.50', borderRadius: 1 }}
        >
          {isEditing ? (
            <>
              {(currentQueryActionType === 'url' ||
                currentQueryActionType === 'app_url' ||
                currentQueryActionType === 'shop') && (
                <TextField
                  fullWidth
                  label={t(
                    `specialFeature.queryPlaceholder.${currentQueryActionType}`,
                    'Enter Value',
                  )}
                  variant="outlined"
                  value={formValues.queryValue || ''}
                  onChange={(e) =>
                    handleInputChange('queryValue', e.target.value)
                  }
                  sx={{ mt: 1 }}
                />
              )}
              {currentQueryActionType === 'query' && (
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'flex-end',
                    gap: 2,
                    mt: 1,
                  }}
                >
                  <TextField
                    fullWidth
                    multiline
                    minRows={4}
                    label={t('specialFeature.queryInputLabel', 'Query')}
                    placeholder="target='#,##', category='#,##', brand='#,##', keyword='#,##,##'"
                    variant="outlined"
                    value={formValues.queryValue || ''}
                    onChange={(e) =>
                      handleInputChange('queryValue', e.target.value)
                    }
                  />
                  <MuiButton
                    variant="contained"
                    startIcon={<Search />}
                    sx={{ minWidth: '124px', height: '45px' }}
                    className="!bg-primaryContainer !text-black"
                    onClick={handleOpenPreviewModal}
                  >
                    {t('common.preview', 'プレビュー')}
                  </MuiButton>
                </Box>
              )}
            </>
          ) : (
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 2,
                mt: 1,
                borderBottom: 2,
                borderColor: 'divider',
              }}
            >
              {currentQueryActionType !== 'no_action' && (
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    width: '100%',
                  }}
                >
                  <div>
                    <Typography>{currentQueryActionType}</Typography>
                    <Typography
                      sx={{
                        mt: 1,
                        minHeight: '40px',
                        overflowWrap: 'break-word',
                      }}
                    >
                      {specialFeature.queryActionType === 'query'
                        ? specialFeature.queryValue
                        : specialFeature.queryActionType !== 'no_action'
                          ? specialFeature.queryValue
                          : t(
                              'specialFeature.queryAction.noAction',
                              'No Action',
                            )}
                    </Typography>
                  </div>
                  <MuiButton
                    variant="contained"
                    startIcon={<Search />}
                    sx={{ minWidth: '120px', height: '45px' }}
                    className="!bg-primaryContainer !text-black"
                    onClick={handleOpenPreviewModal}
                  >
                    {t('common.preview', 'プレビュー')}
                  </MuiButton>
                </Box>
              )}
            </Box>
          )}
          <Grid container spacing={1} mt={1}>
            {allDisplayCountries.map((country) => (
              <Grid item xs={6} sm={4} md={2} key={country.code}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={(isEditing
                        ? formValues.targetCountries
                        : specialFeature.targetCountries
                      )?.includes(country.code)}
                      disabled={!isEditing}
                      onChange={(e) => {
                        if (isEditing) {
                          const currentCountries = new Set(
                            formValues.targetCountries || [],
                          );
                          if (e.target.checked)
                            currentCountries.add(country.code);
                          else currentCountries.delete(country.code);
                          handleInputChange(
                            'targetCountries',
                            Array.from(currentCountries),
                          );
                        }
                      }}
                    />
                  }
                  label={country.name}
                  disabled={!isEditing}
                />
              </Grid>
            ))}
          </Grid>
        </Paper>

        <Box
          sx={{
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderRadius: 2,
            mt: 3,
          }}
        >
          <Tabs
            value={activeLangTab}
            onChange={handleTabChange}
            aria-label="language tabs"
            variant="scrollable"
            scrollButtons="auto"
            sx={{
              borderBottom: 1,
              borderColor: 'divider',
              backgroundColor: 'action.hover',
            }}
          >
            {availableLanguages.map((lang) => {
              const isPublished =
                formValues.translations?.[lang.code]?.isPublicForLang;
              return (
                <Tab
                  key={lang.code}
                  label={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {lang.name}
                      {isPublished && (
                        <Box
                          sx={{
                            width: 8,
                            height: 8,
                            borderRadius: '50%',
                            backgroundColor: 'error.main',
                          }}
                        />
                      )}
                    </Box>
                  }
                  value={lang.code}
                  disabled={
                    !isEditing &&
                    !specialFeature.translations?.[lang.code]?.title &&
                    !specialFeature.translations?.[lang.code]?.content
                  }
                />
              );
            })}
          </Tabs>

          <Box p={{ xs: 2, md: 3 }}>
            <Grid container spacing={3}>
              <Grid
                item
                xs={12}
                md={6}
                sx={{
                  borderRight: { md: '1px dashed' },
                  borderColor: { md: 'divider' },
                  pr: { md: 2 },
                  mt: 2,
                }}
              >
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={
                        isEditing
                          ? formValues.translations?.[activeLangTab]
                              ?.isPublicForLang ?? false
                          : specialFeature.translations?.[activeLangTab]
                              ?.isPublicForLang ?? false
                      }
                      disabled={!isEditing}
                      onChange={(e) =>
                        isEditing &&
                        handleTranslationInputChange(
                          activeLangTab,
                          'isPublicForLang',
                          e.target.checked,
                        )
                      }
                    />
                  }
                  label={t('specialFeature.isPublicForLangLabel', '公開')}
                  sx={{ mb: 1 }}
                />

                <Typography variant="h6" component="h3" gutterBottom>
                  {t(
                    'specialFeature.sectionTitle.contentTitle',
                    '特集タイトル',
                  )}
                </Typography>
                {isEditing ? (
                  <TextField
                    fullWidth
                    multiline
                    minRows={1}
                    value={
                      formValues.translations?.[activeLangTab]?.title || ''
                    }
                    onChange={(e) =>
                      handleTranslationInputChange(
                        activeLangTab,
                        'title',
                        e.target.value,
                      )
                    }
                    error={!!errors[`translations.${activeLangTab}.title`]}
                    helperText={
                      <Box
                        component="span"
                        sx={{
                          display: 'flex',
                          justifyContent: 'space-between',
                        }}
                      >
                        <span>
                          {errors[`translations.${activeLangTab}.title`]}
                        </span>
                        <span>
                          {formValues.translations?.[activeLangTab]?.title
                            ?.length || 0}{' '}
                          / 300
                        </span>
                      </Box>
                    }
                    placeholder={t('common.pleaseEnter', '入力してください')}
                    variant="outlined"
                    InputProps={{
                      endAdornment: formValues.translations?.[activeLangTab]
                        ?.title && (
                        <InputAdornment position="start">
                          <CloseIcon
                            onClick={() =>
                              handleTranslationInputChange(
                                activeLangTab,
                                'title',
                                '',
                              )
                            }
                            className="cursor-pointer mr-2 !fill-onSurface"
                          />
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      mb: 2,
                      borderBottom: 2,
                      borderColor: 'divider',
                      pb: 2,
                    }}
                  />
                ) : (
                  <Typography
                    paragraph
                    sx={{
                      color: 'text.secondary',
                      mb: 2,
                      borderBottom: 2,
                      pb: 2,
                      borderColor: 'divider',
                    }}
                  >
                    {specialFeature.translations?.[activeLangTab]?.title ||
                      t(
                        'specialFeature.noTitleAvailable',
                        'No title available',
                      )}
                  </Typography>
                )}
                <Box className="flex flex-col">
                  {getRelatedItemsForLang(activeLangTab).map((item) => (
                    <FormControlLabel
                      key={item.id}
                      control={
                        <Checkbox
                          checked={item.checked}
                          disabled={!isEditing}
                          onChange={(e) =>
                            handleRelatedItemChange(
                              activeLangTab,
                              item.id,
                              e.target.checked,
                            )
                          }
                        />
                      }
                      label={item.label}
                      disabled={!isEditing}
                    />
                  ))}
                </Box>
              </Grid>

              <Grid item xs={12} md={6}>
                <Box
                  mb={3}
                  className={isEditing ? 'border p-3 rounded' : ''}
                  sx={{ borderColor: 'divider' }}
                >
                  <Typography
                    gutterBottom
                    variant="subtitle1"
                    fontWeight="medium"
                  >
                    {t('specialFeature.bannerImageLabel', 'Banner')}
                  </Typography>
                  {isEditing ? (
                    <Paper
                      variant="outlined"
                      sx={{
                        p: 1.5,
                        textAlign: 'center',
                        borderStyle: 'dashed',
                        minHeight: { xs: 150, md: 200 },
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        alignItems: 'center',
                        mb: 1,
                        position: 'relative',
                      }}
                    >
                      {getPreviewUrl(
                        formValues.translations?.[activeLangTab]?.bannerFile,
                      ) ? (
                        <img
                          src={
                            getPreviewUrl(
                              formValues.translations?.[activeLangTab]
                                ?.bannerFile,
                            )!
                          }
                          alt="Banner Preview"
                          style={{
                            maxHeight: 100,
                            maxWidth: '100%',
                            objectFit: 'contain',
                            marginBottom: '8px',
                          }}
                        />
                      ) : (
                        <AddPhotoAlternate
                          sx={{ fontSize: 40, color: 'text.disabled' }}
                        />
                      )}
                      <MuiButton
                        component="label"
                        size="small"
                        variant="outlined"
                        sx={{ mt: 1, textTransform: 'none' }}
                      >
                        {t('common.button.upload', 'Upload')}
                        <input
                          type="file"
                          hidden
                          accept="image/*"
                          onChange={(e) =>
                            handleImageUpload(e, activeLangTab, 'bannerFile')
                          }
                        />
                      </MuiButton>
                      {formValues.translations?.[activeLangTab]?.bannerFile && (
                        <IconButton
                          size="small"
                          onClick={() =>
                            removeImage(activeLangTab, 'bannerFile')
                          }
                          sx={{ position: 'absolute', top: 8, right: 8 }}
                        >
                          <DeleteIcon fontSize="small" color="error" />
                        </IconButton>
                      )}
                    </Paper>
                  ) : specialFeature.translations?.[activeLangTab]
                      ?.bannerImageUrl ? (
                    <Box
                      component="img"
                      src={
                        getPreviewUrl(
                          specialFeature.translations?.[activeLangTab]
                            ?.bannerImageUrl,
                        ) || ''
                      }
                      alt={
                        specialFeature.translations?.[activeLangTab]
                          ?.bannerImageAltText ||
                        t('specialFeature.bannerImageAlt', 'Banner Image')
                      }
                      sx={{
                        width: '100%',
                        maxHeight: 200,
                        objectFit: 'cover',
                        borderRadius: 1,
                        border: '1px solid',
                        borderColor: 'divider',
                      }}
                    />
                  ) : (
                    <Typography variant="caption" color="textSecondary">
                      {t('common.noImage', '画像がありません')}
                    </Typography>
                  )}
                </Box>
                <Box
                  className={isEditing ? 'border p-3 rounded' : ''}
                  sx={{ borderColor: 'divider' }}
                >
                  <Typography
                    gutterBottom
                    variant="subtitle1"
                    fontWeight="medium"
                  >
                    {t('specialFeature.heroImageLabel', 'Hero')}
                  </Typography>
                  {isEditing ? (
                    <Paper
                      variant="outlined"
                      sx={{
                        p: 1.5,
                        textAlign: 'center',
                        borderStyle: 'dashed',
                        minHeight: { xs: 150, md: 200 },
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        alignItems: 'center',
                        mb: 1,
                        position: 'relative',
                      }}
                    >
                      {getPreviewUrl(
                        formValues.translations?.[activeLangTab]?.heroFile,
                      ) ? (
                        <img
                          src={
                            getPreviewUrl(
                              formValues.translations?.[activeLangTab]
                                ?.heroFile,
                            )!
                          }
                          alt="Hero Preview"
                          style={{
                            maxHeight: 100,
                            maxWidth: '100%',
                            objectFit: 'contain',
                            marginBottom: '8px',
                          }}
                        />
                      ) : (
                        <AddPhotoAlternate
                          sx={{ fontSize: 40, color: 'text.disabled' }}
                        />
                      )}
                      <MuiButton
                        component="label"
                        size="small"
                        variant="outlined"
                        sx={{ mt: 1, textTransform: 'none' }}
                      >
                        {t('common.button.upload', 'Upload')}
                        <input
                          type="file"
                          hidden
                          accept="image/*"
                          onChange={(e) =>
                            handleImageUpload(e, activeLangTab, 'heroFile')
                          }
                        />
                      </MuiButton>
                      {formValues.translations?.[activeLangTab]?.heroFile && (
                        <IconButton
                          size="small"
                          onClick={() => removeImage(activeLangTab, 'heroFile')}
                          sx={{ position: 'absolute', top: 8, right: 8 }}
                        >
                          <DeleteIcon fontSize="small" color="error" />
                        </IconButton>
                      )}
                    </Paper>
                  ) : specialFeature.translations?.[activeLangTab]
                      ?.heroImageUrl ? (
                    <Box
                      component="img"
                      src={
                        getPreviewUrl(
                          specialFeature.translations?.[activeLangTab]
                            ?.heroImageUrl,
                        ) || ''
                      }
                      alt={
                        specialFeature.translations?.[activeLangTab]
                          ?.heroImageAltText ||
                        t('specialFeature.heroImageAlt', 'Hero Image')
                      }
                      sx={{
                        width: '100%',
                        maxHeight: 200,
                        objectFit: 'cover',
                        borderRadius: 1,
                        border: '1px solid',
                        borderColor: 'divider',
                      }}
                    />
                  ) : (
                    <Typography variant="caption" color="textSecondary">
                      {t('common.noImage', '画像がありません')}
                    </Typography>
                  )}
                </Box>
              </Grid>
            </Grid>
          </Box>
        </Box>
      </Paper>
      <SpecialFeaturePreviewModal
        open={isPreviewModalOpen}
        onClose={handleClosePreviewModal}
      />
    </>
  );
};
