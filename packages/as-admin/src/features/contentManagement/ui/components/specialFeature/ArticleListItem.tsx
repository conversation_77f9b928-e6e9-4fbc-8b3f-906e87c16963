import { ArrowRight, ListAlt } from '@mui/icons-material';
import { format, isValid } from 'date-fns';
import React from 'react';

import { SpecialFeature } from '@/features/contentManagement/domain/specialFeature/types';

interface ArticleListItemProps {
  article: SpecialFeature;
  onViewDetails: (specialFeatureId: string) => void;
}

export const ArticleListItem: React.FC<ArticleListItemProps> = ({
  article,
  onViewDetails,
}) => {
  const parsedDate = Date.parse(article.updatedAt);
  let displayDate = article.updatedAt;
  if (isValid(parsedDate)) {
    displayDate = format(parsedDate, 'yyyy.MM.dd HH:mm');
  } else {
    console.error(
      `Invalid date string for article ID: ${article.id}. Value: "${article.updatedAt}"`,
    );
    displayDate = 'Invalid Date';
  }

  return (
    <div
      className="bg-gray-50 p-4 rounded-md border border-gray-200 cursor-pointer hover:bg-gray-100 transition-colors"
      onClick={() => onViewDetails(article.id)}
    >
      <div className="flex items-start border-b border-dashed pb-2">
        <div className="flex-shrink-0 mr-4 text-gray-500">
          <ListAlt fontSize="medium" />
        </div>
        <div className="flex-grow">
          <h3 className="text-lg font-semibold text-gray-900">
            {article.translations.en?.title}
          </h3>
          <p className="text-xs text-gray-500">{displayDate}</p>
        </div>
        <button>
          <span className="text-sm text-secondary">
            {article.status === 'draft' && 'Draft'}
          </span>
          <ArrowRight fontSize="medium" />
        </button>
      </div>
      <div className="flex gap-2 mt-2">
        {article.targetCountries.map((country) => (
          <div key={country} className="px-6 py-1 border border-gray-200">
            {country}
          </div>
        ))}
      </div>
    </div>
  );
};
