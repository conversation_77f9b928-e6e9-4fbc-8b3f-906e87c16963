import { ListAlt } from '@mui/icons-material';
import { Box, Button, Typography } from '@mui/material';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { Breadcrumb } from '@/components/elements';
import { Head } from '@/components/seo/head';
import { SpecialFeature } from '@/features/contentManagement/domain/specialFeature/types';
import { routes } from '@/routes/routes';

import { useSpecialFeatureArticleStore } from '../../stores/special-feature/special-feature-article-store';

import { ArticleListItem } from './ArticleListItem';
import { ArticlePagination } from './ArticlePagination';
import { useGetSpecialFeatureArticles } from './hook/use-create-special-features';

export const ContentManagementSpecialFeature = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const {
    articles,
    isLoading,
    error,
    currentPage,
    totalPages,
    setCurrentPage,
    fetchArticles,
  } = useSpecialFeatureArticleStore();

  const {
    mutateAsync: useGetSpecialFeatureArticlesMutate,
    isPending: isFetchingList,
  } = useGetSpecialFeatureArticles();

  useEffect(() => {
    fetchArticles(useGetSpecialFeatureArticlesMutate);
  }, [currentPage, fetchArticles, useGetSpecialFeatureArticlesMutate]);

  const handleAddNewArticle = () => {
    navigate(
      `${routes.contentManagement.index}${routes.contentManagement.specialFeature.index}${routes.contentManagement.specialFeature.create.index}`,
    );
  };

  const handleViewArticleDetails = (specialFeatureId: string) => {
    navigate(
      `${routes.contentManagement.index}${routes.contentManagement.specialFeature.index}/${specialFeatureId}`,
    );
  };

  if (isLoading || isFetchingList) {
    return (
      <div className="flex justify-center items-center h-screen bg-gray-100">
        <p className="text-2xl text-gray-700">Loading articles...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-screen bg-gray-100">
        <p className="text-2xl text-red-600">Error: {error}</p>
      </div>
    );
  }

  const breadcrumbItems = [
    { name: t('menu.home'), href: routes.home.index },
    { name: t('menu.contentManagement'), href: routes.contentManagement.index },
    { name: t('announcement.title', '特集') },
  ];

  return (
    <>
      <Head title="特集" />
      <Breadcrumb title="特集" items={breadcrumbItems} />
      <Box className="mx-auto p-6 bg-white shadow-md">
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            borderBottom: 2,
            borderColor: 'divider',
            mb: 2,
            pt: { xs: 1, md: 1 },
            pb: 2,
          }}
        >
          <Typography variant="h2" component="h1">
            記事一覧
          </Typography>
          <Box
            sx={{
              ml: 'auto',
              display: 'flex',
              alignItems: 'center',
              gap: 1,
            }}
          >
            <Button
              variant="contained"
              color="inherit"
              startIcon={<ListAlt />}
              className="!bg-[#DBE4E3]"
              sx={{ height: '40px' }}
              onClick={handleAddNewArticle}
            >
              新規追加
            </Button>
          </Box>
        </Box>
        <div className="space-y-4">
          {articles.length > 0 ? (
            articles.map((article: SpecialFeature) => (
              <ArticleListItem
                key={article.id}
                article={article}
                onViewDetails={handleViewArticleDetails}
              />
            ))
          ) : (
            <p className="text-center text-gray-600 py-8">No articles found.</p>
          )}
        </div>

        <ArticlePagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setCurrentPage}
        />
      </Box>
    </>
  );
};
