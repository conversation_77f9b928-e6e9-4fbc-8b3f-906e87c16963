import { DeleteOutline, Publish, SaveAs } from '@mui/icons-material';
import { Box, Button as Mu<PERSON><PERSON>utton, Paper, Typography } from '@mui/material';
import { Divider } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';

import { CommonDialog } from '@/components/dialog/CommonDialog';
import { ApplicationDatePicker } from '@/components/forms/date-picker/ApplicationDatePicker';
import { SpecialFeatureStatus } from '@/features/contentManagement/domain/specialFeature/types';

interface SpecialFeatureRightPanelProps {
  type: 'create' | 'edit';
  initialPublicationDate?: string | Date | null;
  initialStatus?: SpecialFeatureStatus;
  formIsValid: boolean;
  onSave: (publicationDate: Date, status: SpecialFeatureStatus) => void;
  onDelete?: () => void;
  isSubmitting?: boolean;
}

export const SpecialFeatureRightPanel: React.FC<
  SpecialFeatureRightPanelProps
> = ({
  type,
  initialPublicationDate,
  formIsValid,
  onSave,
  onDelete,
  isSubmitting,
}) => {
  const { t } = useTranslation();
  const [publicationDate, setPublicationDate] = useState<Dayjs | null>(
    initialPublicationDate ? dayjs(initialPublicationDate) : dayjs(),
  );

  const [dialogState, setDialogState] = useState<{
    open: boolean;
    type: 'alert' | 'success' | 'error' | 'confirm-delete';
    message: string;
  } | null>(null);

  useEffect(() => {
    setPublicationDate(
      initialPublicationDate ? dayjs(initialPublicationDate) : dayjs(),
    );
  }, [initialPublicationDate]);

  const handleSaveDraft = () => {
    onSave(publicationDate ? publicationDate.toDate() : new Date(), 'draft');
  };

  const handlePublish = () => {
    if (!publicationDate) {
      toast.error(
        t(
          'specialFeature.error.publicationDateRequired',
          '公開するには更新適用日を指定してください。',
        ),
      );
      return;
    }
    onSave(publicationDate.toDate(), 'published');
  };

  const handleDeleteConfirm = () => {
    if (onDelete) {
      onDelete();
    }
    setDialogState(null);
  };

  const handleDialogCancel = () => {
    setDialogState(null);
  };

  const showDeleteDialog = () => {
    setDialogState({
      open: true,
      type: 'confirm-delete',
      message: t(
        'specialFeature.confirmDelete',
        'この特集記事を削除しますか？',
      ),
    });
  };

  return (
    <Paper
      sx={{
        p: 2.5,
        border: (theme) => `1px solid ${theme.palette.divider}`,
        boxShadow: 1,
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
      }}
    >
      <Box>
        <Typography
          variant="subtitle2"
          sx={{ mb: 1, fontWeight: 'medium', color: 'text.primary' }}
        >
          {t('specialFeature.publicationSettings', '公開設定')}
        </Typography>
        <ApplicationDatePicker
          label={t('specialFeature.updateApplicationDateLabel', '更新適用日')}
          value={publicationDate}
          onChange={(date) => setPublicationDate(date ? dayjs(date) : null)}
        />
      </Box>

      <Divider style={{ margin: '0' }} />

      <Box>
        <Box className="flex w-full gap-2">
          {' '}
          {/* MUI recommends gap with theme.spacing */}
          <MuiButton
            variant="outlined"
            startIcon={<SaveAs />}
            onClick={handleSaveDraft}
            fullWidth
            disabled={!formIsValid || isSubmitting}
            sx={{ color: 'text.primary' }}
          >
            {t('common.button.saveDraft', '下書き保存')}
          </MuiButton>
          <MuiButton
            variant="contained"
            startIcon={<Publish />}
            onClick={handlePublish}
            fullWidth
            disabled={!formIsValid || isSubmitting || !publicationDate}
            color="primary" // Standard primary color for publish
          >
            {t('common.button.publish', '公開する')}
          </MuiButton>
        </Box>

        {type === 'edit' && onDelete && (
          <>
            <Divider style={{ margin: '8px 0' }} />
            <MuiButton
              variant="text"
              onClick={showDeleteDialog}
              fullWidth
              disabled={isSubmitting}
              color="error"
              startIcon={<DeleteOutline />}
            >
              {t('common.button.delete', '削除する')}
            </MuiButton>
          </>
        )}
      </Box>
      {dialogState && (
        <CommonDialog
          open={dialogState.open}
          type={dialogState.type}
          message={dialogState.message}
          onOk={handleDeleteConfirm}
          onCancel={handleDialogCancel}
          okText="削除"
          cancelText="キャンセル"
        />
      )}
    </Paper>
  );
};
