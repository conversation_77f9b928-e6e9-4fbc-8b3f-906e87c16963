import { Box, CircularProgress, Typography, Grid } from '@mui/material';
import { useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import { Breadcrumb } from '@/components/elements';
import { Head } from '@/components/seo/head';
import {
  SpecialFeature,
  SpecialFeatureFormData,
  MultilingualString,
  SpecialFeatureStatus,
} from '@/features/contentManagement/domain/specialFeature/types';
import { useUploadFile } from '@/features/uploadFile/ui/hooks/use-upload-file';
import { routes } from '@/routes/routes';

import { useCreateSpecialFeatureArticle } from './hook/use-create-special-features';
import { ContentManagementSpecialFeatureForm } from './SpecialFeatureForm';
import { SpecialFeatureRightPanel } from './SpecialFeatureRightPanel';

// Helper to deeply set values in a nested object (immutable way)
const setNestedValue = (obj: any, path: string, value: any): any => {
  const keys = path.split('.');
  const newObj = JSON.parse(JSON.stringify(obj)); // Deep clone
  let current = newObj;
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!current[key] || typeof current[key] !== 'object') {
      // Check if next key is a number (array index)
      if (!isNaN(parseInt(keys[i + 1], 10))) {
        current[key] = [];
      } else {
        current[key] = {};
      }
    }
    current = current[key];
  }
  current[keys[keys.length - 1]] = value;
  return newObj;
};

export const ContentManagementSpecialFeatureNew = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [showRightPanel, setShowRightPanel] = useState(false);

  const createSpecialFeatureMutation = useCreateSpecialFeatureArticle();

  const [formValues, setFormValues] = useState<SpecialFeatureFormData>({
    category: 'campaign',
    targetCountries: [],
    publicationDate: new Date(),
    status: 'draft',
    queryActionType: 'query',
    queryValue: '',
    translations: {
      en: {
        title: '',
        content: '',
        isPublicForLang: true,
        relatedItems: [],
      },
    },
  });

  const { mutateAsync: uploadFile } = useUploadFile(() => {
    toast.info('ファイルのアップロードに失敗しました。');
  });

  const handleMainActionButtonClick = useCallback(() => {
    setShowRightPanel(true); // Show the right panel when "Done" is clicked
  }, []);

  const handleSetFormValue = useCallback((fieldName: string, value: any) => {
    setFormValues((prev) => setNestedValue(prev, fieldName, value));
  }, []);

  // Upload files and replace File objects with URLs in form data
  const uploadFilesFromTranslations = useCallback(
    async (
      formData: SpecialFeatureFormData,
    ): Promise<SpecialFeatureFormData> => {
      const updatedTranslations = { ...formData.translations };

      // Process each translation
      for (const [langKey, translation] of Object.entries(
        formData.translations,
      )) {
        if (!translation) continue;

        const updatedTranslation = { ...translation };

        // Upload banner file if it's a File object
        if (translation.bannerFile && translation.bannerFile instanceof File) {
          try {
            const uploadResponse = await uploadFile(translation.bannerFile);
            updatedTranslation.bannerFile = uploadResponse.key;
          } catch (error) {
            console.error(
              `Failed to upload banner file for ${langKey}:`,
              error,
            );
            toast.error(`Failed to upload banner image for ${langKey}`);
            throw error;
          }
        }

        // Upload hero file if it's a File object
        if (translation.heroFile && translation.heroFile instanceof File) {
          try {
            const uploadResponse = await uploadFile(translation.heroFile);
            updatedTranslation.heroFile = uploadResponse.key;
          } catch (error) {
            console.error(`Failed to upload hero file for ${langKey}:`, error);
            toast.error(`Failed to upload hero image for ${langKey}`);
            throw error;
          }
        }

        updatedTranslations[langKey as keyof MultilingualString] =
          updatedTranslation;
      }

      return {
        ...formData,
        translations: updatedTranslations,
      };
    },
    [uploadFile],
  );

  const handleSaveFromPanel = useCallback(
    async (publicationDate: Date, status: SpecialFeatureStatus) => {
      try {
        // Upload files and get updated form data with file URLs
        const formDataWithUploadedFiles = await uploadFilesFromTranslations({
          ...formValues,
          publicationDate,
          status,
        });

        createSpecialFeatureMutation.mutate(formDataWithUploadedFiles, {
          onSuccess: (createdData: SpecialFeature) => {
            toast.success(
              t(
                'specialFeature.createSuccess',
                'Special Feature created successfully.',
              ),
            );
            // Navigate to the created item's details page
            if (createdData?.id) {
              navigate(
                `${routes.contentManagement.index}${routes.contentManagement.specialFeature.index}/${createdData.id}`,
              );
            } else {
              // Fallback to list if no ID is returned
              navigate(routes.contentManagement.specialFeature.index);
            }
          },
          onError: (error: Error) => {
            console.error('Failed to create special feature:', error);
            toast.error(
              t(
                'specialFeature.createError',
                'Failed to create special feature.',
              ),
            );
          },
        });
      } catch (error) {
        console.error('Upload error:', error);
        // Error already handled in uploadFilesFromTranslations
      }
    },
    [
      formValues,
      createSpecialFeatureMutation,
      t,
      navigate,
      uploadFilesFromTranslations,
    ],
  );

  const isFormValid = useMemo(() => {
    if (!formValues.translations) return false;
    // Basic validation: at least one language has a title
    const hasAtLeastOneTitle = Object.values(formValues.translations).some(
      (langData) => langData?.title && langData.title.trim() !== '',
    );
    // Add more validation as needed (e.g., for query fields based on type)
    return hasAtLeastOneTitle;
  }, [formValues]);

  const breadcrumbItems = useMemo(
    () => [
      { name: t('menu.home'), href: routes.home.index },
      {
        name: t('menu.contentManagement'),
        href: routes.contentManagement.index,
      },
      {
        name: t('specialFeature.titleLong', '特集'),
        href: routes.contentManagement.specialFeature.index,
      },
      {
        name: t('specialFeature.newTitle', '新規追加'),
      },
    ],
    [t],
  );

  // Create a mock empty special feature for the form component
  const emptySpecialFeature: SpecialFeature = {
    id: '',
    status: 'draft',
    category: 'campaign',
    targetCountries: ['ALL'],
    publicationDate: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    isPublic: false,
    queryActionType: 'query',
    translations: {},
  };

  if (createSpecialFeatureMutation.isPending) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="80vh"
      >
        <CircularProgress />
        <Typography ml={2}>
          {t('specialFeature.creating', 'Creating Special Feature...')}
        </Typography>
      </Box>
    );
  }

  return (
    <>
      <Head title={t('specialFeature.newTitle', 'New Special Feature')} />
      <Breadcrumb
        title={t('specialFeature.titleLong', '特集管理')}
        items={breadcrumbItems}
      />
      <Grid container spacing={2} sx={{ p: { xs: 1, md: 3 } }}>
        <Grid item xs={12} md={showRightPanel ? 8.5 : 12}>
          <ContentManagementSpecialFeatureForm
            specialFeature={emptySpecialFeature}
            isEditing={true} // Always in editing mode for new items
            handleHeaderAction={handleMainActionButtonClick}
            formValues={formValues}
            setFormValue={handleSetFormValue}
          />
        </Grid>
        {showRightPanel && (
          <Grid item xs={12} md={3.5}>
            <SpecialFeatureRightPanel
              type="create"
              initialPublicationDate={formValues.publicationDate}
              initialStatus={formValues.status}
              formIsValid={isFormValid}
              onSave={handleSaveFromPanel}
              onDelete={undefined} // No delete option for new items
              isSubmitting={createSpecialFeatureMutation.isPending}
            />
          </Grid>
        )}
      </Grid>
    </>
  );
};
