import CloseIcon from '@mui/icons-material/Close';
import ImageIcon from '@mui/icons-material/Image'; // Placeholder icon
import {
  Modal,
  Box,
  Typography,
  IconButton,
  Paper,
  Grid,
  Chip,
} from '@mui/material';
import React from 'react';
import { useTranslation } from 'react-i18next';

// Define a type for the preview item data for clarity
interface PreviewItemData {
  id: string;
  imageUrl?: string; // Optional: if you have actual image URLs
  discount?: string; // e.g., "45% OFF"
  title: string;
  subtext: string;
}

interface SpecialFeaturePreviewModalProps {
  open: boolean;
  onClose: () => void;
  //   formData: SpecialFeatureFormData; // Pass relevant form data for a more dynamic preview
  //   For now, we'll use mocked preview items based on the image P9-4-1-002.jpg
}

// Mock data for preview items based on P9-4-1-002.jpg
const mockPreviewItems: PreviewItemData[] = [
  {
    id: 'prev1',
    discount: '出品予定',
    title: 'あのイーハトーヴォの…',
    subtext: 'Ｓ,Ｍ,Ｌ,ＸＬ,ＸＸＬ',
  },
  {
    id: 'prev2',
    title: 'あのイーハトーヴォの…',
    subtext: 'Ｓ,Ｍ,Ｌ,ＸＬ,ＸＸＬ',
  },
  {
    id: 'prev3',
    discount: '45% OFF',
    title: 'あのイーハトーヴォの…',
    subtext: 'Ｓ,Ｍ,Ｌ,ＸＬ,ＸＸＬ',
  },
  {
    id: 'prev4',
    title: 'あのイーハトーヴォの…',
    subtext: 'Ｓ,Ｍ,Ｌ,ＸＬ,ＸＸＬ',
  },
  {
    id: 'prev5',
    discount: '45% OFF',
    title: 'あのイーハトーヴォの…',
    subtext: 'Ｓ,Ｍ,Ｌ,ＸＬ,ＸＸＬ',
  },
  {
    id: 'prev6',
    title: 'あのイーハトーヴォの…',
    subtext: 'Ｓ,Ｍ,Ｌ,ＸＬ,ＸＸＬ',
  },
  {
    id: 'prev7',
    discount: '45% OFF',
    title: 'あのイーハトーヴォの…',
    subtext: 'Ｓ,Ｍ,Ｌ,ＸＬ,ＸＸＬ',
  },
  {
    id: 'prev8',
    discount: '45% OFF',
    title: 'あのイーハトーヴォの…',
    subtext: 'Ｓ,Ｍ,Ｌ,ＸＬ,ＸＸＬ',
  },
  {
    id: 'prev9',
    discount: '45% OFF',
    title: 'あのイーハトーヴォの…',
    subtext: 'Ｓ,Ｍ,Ｌ,ＸＬ,ＸＸＬ',
  },
];

const modalStyle = {
  position: 'absolute' as const,
  top: '50%',
  left: 'auto',
  right: '-5px',
  transform: 'translateY(-50%)',
  width: 'calc(100% - 70% - 100px)',
  maxWidth: 450,
  minWidth: 320,
  bgcolor: 'background.paper',
  boxShadow: 24,
  p: 0,
  borderRadius: '8px',
  display: 'flex',
  flexDirection: 'column',
  maxHeight: '90vh',
};

const headerStyle = {
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  p: '12px 16px',
};

const contentStyle = {
  p: 2,
  overflowY: 'auto', // Make content scrollable if it overflows
};

const itemStyle = {
  p: 1.5,
  textAlign: 'center',
  position: 'relative', // For discount chip positioning
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'space-between',
};

const imagePlaceholderStyle = {
  width: '100%',
  paddingBottom: '75%', // Aspect ratio 4:3
  backgroundColor: 'grey.200',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  borderRadius: '4px',
  mb: 1,
};

export const SpecialFeaturePreviewModal: React.FC<
  SpecialFeaturePreviewModalProps
> = ({
  open,
  onClose,
  // formData,
}) => {
  const { t } = useTranslation();

  return (
    <Modal
      open={open}
      onClose={onClose}
      aria-labelledby="special-feature-preview-modal-title"
      aria-describedby="special-feature-preview-modal-description"
    >
      <Box sx={modalStyle}>
        <Box sx={headerStyle} className="relative">
          <div className="absolute bottom-0 left-0">
            <IconButton onClick={onClose} aria-label="close preview modal">
              <CloseIcon />
            </IconButton>
          </div>
          <Typography
            id="special-feature-preview-modal-title"
            variant="h3"
            component="h2"
          >
            {t('specialFeature.previewModal.title', 'プレビュー')}
          </Typography>
        </Box>

        <Box sx={contentStyle}>
          <Grid container spacing={2}>
            {mockPreviewItems.map((item) => (
              <Grid item xs={6} sm={4} key={item.id}>
                {' '}
                <Paper elevation={1} sx={itemStyle}>
                  <Box>
                    {item.discount && (
                      <Chip
                        label={item.discount}
                        className={
                          item.discount === '出品予定'
                            ? '!bg-[#E6E6E6] !text-black'
                            : '!bg-[#FA0000] !text-black'
                        }
                        size="small"
                        sx={{
                          position: 'absolute',
                          top: 8,
                          left: 8,
                          fontWeight: 'bold',
                          fontSize: '0.7rem',
                          height: 'auto',
                          '& .MuiChip-label': { py: '2px', px: '6px' },
                        }}
                      />
                    )}
                    <Box sx={imagePlaceholderStyle}>
                      <ImageIcon sx={{ fontSize: 40, color: 'grey.400' }} />
                    </Box>
                    <Typography
                      variant="caption"
                      display="block"
                      sx={{ fontWeight: 'medium', minHeight: '2.5em' }}
                    >
                      {item.title}
                    </Typography>
                  </Box>
                  <Typography
                    variant="caption"
                    color="text.secondary"
                    display="block"
                    sx={{ mt: 0.5, fontSize: '0.65rem' }}
                  >
                    {item.subtext}
                  </Typography>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </Box>
      </Box>
    </Modal>
  );
};
