import { useNavigate } from 'react-router-dom';

import { But<PERSON> } from '@/components/forms/button';
import { Head } from '@/components/seo/head';
import { routes } from '@/routes/routes';

export const ContentManagementSpecialFeatureAddEdit = () => {
  const navigate = useNavigate();
  const router = `${routes.contentManagement.index}${routes.contentManagement.specialFeature.index}`;

  return (
    <>
      <Head title="記事詳細追加編集" />
      <div className="flex flex-col justify-center items-center h-screen bg-white">
        <h1 className="text-6xl font-bold mb-4">
          {'9-4-1/001 記事詳細追加編集 - Add/Edit Article Details'}
        </h1>
        <Button onClick={() => navigate(router)}>
          {'Go back to the previous page 特集管理 - Special feature management'}
        </Button>

        <div className="flex flex-row justify-center items-center bg-white pt-5 w-full">
          <div className="pr-10 w-1/2">
            <h1 className="text-3xl font-bold mb-4">
              {'9-4-1/002 対象製品一覧'}
            </h1>
            <Button
              onClick={() =>
                navigate(
                  `${router}${routes.contentManagement.specialFeature.addOrEdit.index}${routes.contentManagement.specialFeature.addOrEdit.list.index}`,
                )
              }
            >
              {'Go to page 対象製品一覧'}
            </Button>
          </div>
        </div>

        <div className="flex flex-row justify-center items-center bg-white pt-5 w-full">
          <div className="pr-10 w-1/2">
            <h1 className="text-3xl font-bold mb-4">{'9-4-1/003 記事削除'}</h1>
            <Button
              onClick={() =>
                navigate(
                  `${router}${routes.contentManagement.specialFeature.addOrEdit.index}${routes.contentManagement.specialFeature.addOrEdit.delete.index}`,
                )
              }
            >
              {'Go to page 記事削除'}
            </Button>
          </div>
        </div>
      </div>
    </>
  );
};
