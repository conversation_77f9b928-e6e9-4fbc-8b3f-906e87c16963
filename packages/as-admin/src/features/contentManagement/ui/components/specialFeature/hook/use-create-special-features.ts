import { useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError, AxiosRequestConfig } from 'axios';
import { toast } from 'react-toastify';

import {
  SpecialFeature,
  SpecialFeatureFilters,
  SpecialFeatureFormData,
} from '@/features/contentManagement/domain/specialFeature/types';
import { specialFeatureRepository } from '@/features/contentManagement/infrastructure/repositories/special-feature-repository';

const repository = specialFeatureRepository();

export const useGetSpecialFeatureArticles = () => {
  return useMutation<
    SpecialFeature[],
    AxiosError,
    {
      filters: SpecialFeatureFilters;
      options?: AxiosRequestConfig;
    }
  >({
    mutationFn: ({ filters, options }) =>
      repository.getSpecialFeatures(filters, options),
    onSuccess: (data) => {
      console.log('Fetched banners successfully:', data);
    },
    onError: (error) => {
      console.error('Failed to fetch banners:', error);
      return [];
    },
  });
};

export const useGetSpecialFeatureArticlesById = () => {
  return useMutation<
    SpecialFeature,
    AxiosError,
    { id: string; options?: AxiosRequestConfig }
  >({
    mutationFn: async ({ id }) => {
      const banner = await repository.getSpecialFeatureById(id);
      console.log('Fetched banner by ID:', id, banner);
      if (!banner) {
        throw new Error('Announcement not found');
      }
      return banner;
    },
    onSuccess: (data) => {
      console.log('Fetched banner by ID successfully:', data);
    },
    onError: (error) => {
      console.error('Failed to fetch banner by ID:', error);
    },
  });
};

export const useCreateSpecialFeatureArticle = () => {
  const queryClient = useQueryClient();
  return useMutation<SpecialFeature, AxiosError, SpecialFeatureFormData>({
    mutationFn: (data) => repository.createSpecialFeature(data),
    onSuccess: () => {
      toast.success('Special feature created successfully!');
      queryClient.refetchQueries({ queryKey: ['specialFeatures'] });
    },
    onError: (error) => {
      toast.error(`Failed to create special feature: ${error.message}`);
      console.error('Failed to create special feature:', error);
    },
  });
};

export const useUpdateSpecialFeatureArticle = () => {
  const queryClient = useQueryClient();
  return useMutation<
    void,
    AxiosError,
    { id: string; data: SpecialFeatureFormData }
  >({
    mutationFn: ({ id, data }) => repository.updateSpecialFeature(id, data),
    onSuccess: () => {
      toast.success('Special feature updated successfully!');
      queryClient.refetchQueries({ queryKey: ['specialFeatures'] });
    },
    onError: (error) => {
      toast.error(`Failed to update special feature: ${error.message}`);
      console.error('Failed to update special feature:', error);
    },
  });
};

// New: Hook for deleting an banner
export const useDeleteSpecialFeatureArticle = () => {
  const queryClient = useQueryClient();
  return useMutation<void, AxiosError, string>({
    mutationFn: (id: string) => repository.deleteSpecialFeature(id),
    onSuccess: () => {
      toast.success('Announcement deleted successfully!');
      queryClient.refetchQueries({ queryKey: ['banners'] });
    },
    onError: (error) => {
      toast.error(`Failed to delete banner: ${error.message}`);
      console.error('Failed to delete banner:', error);
    },
  });
};
