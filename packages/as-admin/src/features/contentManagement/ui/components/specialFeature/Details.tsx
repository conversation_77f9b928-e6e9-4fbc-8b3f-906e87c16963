import { Warning } from '@mui/icons-material';
import {
  Box,
  Button as Mu<PERSON><PERSON>utton,
  CircularProgress,
  Paper,
  Typography,
  Grid,
} from '@mui/material';
import { useEffect, useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';

import { Breadcrumb } from '@/components/elements';
import { Head } from '@/components/seo/head';
import {
  SpecialFeature,
  SpecialFeatureFormData,
  MultilingualString,
  SpecialFeatureStatus,
  SpecialFeatureFormDataTranslation,
} from '@/features/contentManagement/domain/specialFeature/types';
import { useUploadFile } from '@/features/uploadFile/ui/hooks/use-upload-file';
import { routes } from '@/routes/routes';

import {
  useGetSpecialFeatureArticlesById,
  useUpdateSpecialFeatureArticle, // Renamed from useUpdateAnnouncementArticle
  useDeleteSpecialFeatureArticle, // Renamed from useDeleteAnnouncementArticle
} from './hook/use-create-special-features'; // Ensure correct hook import path
import { ContentManagementSpecialFeatureForm } from './SpecialFeatureForm';
import { SpecialFeatureRightPanel } from './SpecialFeatureRightPanel';

// Helper to deeply set values in a nested object (immutable way)
const setNestedValue = (obj: any, path: string, value: any): any => {
  const keys = path.split('.');
  const newObj = JSON.parse(JSON.stringify(obj)); // Deep clone
  let current = newObj;
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!current[key] || typeof current[key] !== 'object') {
      // Check if next key is a number (array index)
      if (!isNaN(parseInt(keys[i + 1], 10))) {
        current[key] = [];
      } else {
        current[key] = {};
      }
    }
    current = current[key];
  }
  current[keys[keys.length - 1]] = value;
  return newObj;
};

export const ContentManagementSpecialFeatureDetails = () => {
  const { specialFeatureId } = useParams<{ specialFeatureId: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [isEditing, setIsEditing] = useState(false);
  const [showRightPanel, setShowRightPanel] = useState(false);

  const {
    data: specialFeature,
    isPending: isLoadingSpecialFeature,
    error: fetchError,
    mutate: fetchSpecialFeatureById,
  } = useGetSpecialFeatureArticlesById();

  const updateSpecialFeatureMutation = useUpdateSpecialFeatureArticle();
  const deleteSpecialFeatureMutation = useDeleteSpecialFeatureArticle();

  const [formValues, setFormValues] = useState<SpecialFeatureFormData>({
    category: 'campaign',
    targetCountries: [],
    publicationDate: new Date(),
    status: 'draft',
    queryActionType: 'query',
    queryValue: '',
    translations: {},
  });

  const { mutateAsync: uploadFile } = useUploadFile(() => {
    toast.info('ファイルのアップロードに失敗しました。');
  });

  useEffect(() => {
    if (specialFeatureId) {
      fetchSpecialFeatureById({ id: specialFeatureId });
    } else {
      setIsEditing(true);
      setFormValues({
        category: 'campaign',
        targetCountries: ['ALL'],
        publicationDate: new Date(),
        status: 'draft',
        queryActionType: 'query',
        queryValue: '',
        translations: {
          en: {
            title: '',
            content: '',
            isPublicForLang: true,
            relatedItems: [],
          },
        },
      });
      setShowRightPanel(false);
    }
  }, [specialFeatureId, fetchSpecialFeatureById]);

  useEffect(() => {
    if (specialFeature) {
      const newFormTranslations: Partial<
        Record<keyof MultilingualString, SpecialFeatureFormDataTranslation>
      > = {};
      (
        Object.keys(specialFeature.translations || {}) as Array<
          keyof MultilingualString
        >
      ).forEach((lang) => {
        const domainTranslation = specialFeature.translations?.[lang];
        if (domainTranslation) {
          newFormTranslations[lang] = {
            title: domainTranslation.title,
            content: domainTranslation.content,
            isPublicForLang: domainTranslation.isPublicForLang,
            bannerFile: domainTranslation.bannerImageUrl, // Keep as URL string initially
            bannerAltText: domainTranslation.bannerImageAltText,
            heroFile: domainTranslation.heroImageUrl, // Keep as URL string initially
            heroAltText: domainTranslation.heroImageAltText,
            relatedItems: domainTranslation.relatedItems || [],
          };
        }
      });

      setFormValues({
        id: specialFeature.id,
        category: specialFeature.category,
        targetCountries: specialFeature.targetCountries,
        publicationDate: new Date(specialFeature.publicationDate),
        status: specialFeature.status,
        queryActionType: specialFeature.queryActionType || 'query',
        queryValue: specialFeature.queryValue,
        translations: newFormTranslations,
      });
      if (!isEditing) {
        setShowRightPanel(false);
      }
    }
  }, [specialFeature, isEditing]);

  const handleMainActionButtonClick = useCallback(() => {
    if (!isEditing) {
      setIsEditing(true);
      setShowRightPanel(false);
    } else {
      setShowRightPanel(true); // Clicking "Done" shows the right panel
    }
  }, [isEditing]);

  const handleSetFormValue = useCallback((fieldName: string, value: any) => {
    setFormValues((prev) => setNestedValue(prev, fieldName, value));
  }, []);

  // Upload files and replace File objects with URLs in form data
  const uploadFilesFromTranslations = useCallback(
    async (
      formData: SpecialFeatureFormData,
    ): Promise<SpecialFeatureFormData> => {
      const updatedTranslations = { ...formData.translations };

      // Process each translation
      for (const [langKey, translation] of Object.entries(
        formData.translations,
      )) {
        if (!translation) continue;

        const updatedTranslation = { ...translation };

        // Upload banner file if it's a File object
        if (translation.bannerFile && translation.bannerFile instanceof File) {
          try {
            const uploadResponse = await uploadFile(translation.bannerFile);
            updatedTranslation.bannerFile = uploadResponse.key;
          } catch (error) {
            console.error(
              `Failed to upload banner file for ${langKey}:`,
              error,
            );
            toast.error(`Failed to upload banner image for ${langKey}`);
            throw error;
          }
        }

        // Upload hero file if it's a File object
        if (translation.heroFile && translation.heroFile instanceof File) {
          try {
            const uploadResponse = await uploadFile(translation.heroFile);
            updatedTranslation.heroFile = uploadResponse.key;
          } catch (error) {
            console.error(`Failed to upload hero file for ${langKey}:`, error);
            toast.error(`Failed to upload hero image for ${langKey}`);
            throw error;
          }
        }

        updatedTranslations[langKey as keyof MultilingualString] =
          updatedTranslation;
      }

      return {
        ...formData,
        translations: updatedTranslations,
      };
    },
    [uploadFile],
  );

  const handleSaveFromPanel = useCallback(
    async (publicationDate: Date, status: SpecialFeatureStatus) => {
      if (!specialFeatureId && !formValues.id) {
        toast.info('Missing ID.');
        return;
      }

      const idToUpdate = specialFeatureId || formValues.id;
      if (!idToUpdate || !specialFeature) {
        // specialFeature might be undefined if creating new
        toast.error(
          t(
            'specialFeature.error.cannotSaveMissingData',
            'Cannot save: Special Feature data is missing.',
          ),
        );
        return;
      }

      // Upload files and get updated form data with file URLs
      const formDataWithUploadedFiles = await uploadFilesFromTranslations({
        ...formValues,
        id: idToUpdate, // Ensure ID is part of the data for update
        publicationDate,
        status,
      });

      updateSpecialFeatureMutation.mutate(
        {
          id: idToUpdate,
          data: formDataWithUploadedFiles,
        },
        {
          onSuccess: () => {
            setIsEditing(false);
            setShowRightPanel(false);
            if (specialFeatureId)
              fetchSpecialFeatureById({ id: specialFeatureId }); // Refetch
            else if (formDataWithUploadedFiles?.id)
              navigate(
                `${routes.contentManagement.specialFeature.index}/${formDataWithUploadedFiles.id}`,
              ); // Navigate to new ID
          },
        },
      );
    },
    [
      specialFeatureId,
      formValues,
      updateSpecialFeatureMutation,
      fetchSpecialFeatureById,
      specialFeature,
      t,
      navigate,
      uploadFilesFromTranslations,
    ],
  );

  const handleDeleteFromPanel = useCallback(async () => {
    if (!specialFeatureId) {
      toast.error(
        t(
          'specialFeature.error.cannotDeleteMissingId',
          'Cannot delete: Special Feature ID is missing.',
        ),
      );
      return;
    }
    deleteSpecialFeatureMutation.mutate(specialFeatureId, {
      onSuccess: () => {
        toast.success(
          t(
            'specialFeature.deleteSuccess',
            'Special Feature deleted successfully.',
          ),
        );
        navigate(routes.contentManagement.specialFeature.index);
      },
    });
  }, [specialFeatureId, deleteSpecialFeatureMutation, navigate, t]);

  const isFormValid = useMemo(() => {
    if (!formValues.translations) return false;
    // Basic validation: at least one language has a title
    const hasAtLeastOneTitle = Object.values(formValues.translations).some(
      (langData) => langData?.title && langData.title.trim() !== '',
    );
    // Add more validation as needed (e.g., for query fields based on type)
    return hasAtLeastOneTitle;
  }, [formValues]);

  const currentDisplayTitle = useMemo(() => {
    const langToUse = 'en' as keyof MultilingualString; // Or i18n.language
    if (isEditing) {
      return (
        formValues.translations?.[langToUse]?.title ||
        t('specialFeature.untitled', 'Untitled Special Feature')
      );
    }
    return (
      specialFeature?.translations?.[langToUse]?.title ||
      specialFeature?.translations?.en?.title ||
      t('specialFeature.untitled', 'Untitled Special Feature')
    );
  }, [specialFeature, formValues.translations, isEditing, t]);

  const breadcrumbItems = useMemo(
    () => [
      { name: t('menu.home'), href: routes.home.index },
      {
        name: t('menu.contentManagement'),
        href: routes.contentManagement.index,
      },
      {
        name: t('specialFeature.titleLong', '特集管理'),
        href: routes.contentManagement.specialFeature.index,
      },
      {
        name: t('specialFeature.newTitle', '記事'),
      },
    ],
    [t],
  );

  if (isLoadingSpecialFeature && specialFeatureId) {
    // Only show full loading if fetching existing
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="80vh"
      >
        <CircularProgress />
        <Typography ml={2}>{t('common.loading', 'Loading...')}</Typography>
      </Box>
    );
  }

  if (fetchError && specialFeatureId) {
    return (
      <Box p={3}>
        <Head
          title={t(
            'specialFeature.detailsError',
            'Error Loading Special Feature',
          )}
        />
        <Breadcrumb
          title={t('specialFeature.detailsError', 'Error')}
          items={breadcrumbItems.slice(0, -1)}
        />
        <Paper elevation={3} sx={{ p: 3, mt: 2, textAlign: 'center' }}>
          <Warning color="error" sx={{ fontSize: 48 }} />
          <Typography variant="h6" color="error" mt={1}>
            {t(
              'specialFeature.fetchErrorMsg',
              'Could not load special feature details.',
            )}
          </Typography>
          <Typography>{fetchError.message}</Typography>
          <MuiButton
            variant="contained"
            onClick={() =>
              navigate(routes.contentManagement.specialFeature.index)
            }
            sx={{ mt: 2 }}
          >
            {t('specialFeature.backToList', 'Back to List')}
          </MuiButton>
        </Paper>
      </Box>
    );
  }

  // For create new (no specialFeatureId) or if specialFeature is loaded
  if (!specialFeature && specialFeatureId) {
    // Should not happen if isLoadingSpecialFeature handles it
    return (
      <Box p={3}>
        <Head
          title={t('specialFeature.notFound', 'Special Feature Not Found')}
        />
        <Breadcrumb
          title={t('specialFeature.notFound', 'Not Found')}
          items={breadcrumbItems.slice(0, -1)}
        />
        <Paper elevation={3} sx={{ p: 3, mt: 2, textAlign: 'center' }}>
          <Warning sx={{ fontSize: 48 }} />
          <Typography variant="h6" mt={1}>
            {t(
              'specialFeature.notFoundMsg',
              'The requested special feature could not be found.',
            )}
          </Typography>
          <MuiButton
            variant="contained"
            onClick={() =>
              navigate(routes.contentManagement.specialFeature.index)
            }
            sx={{ mt: 2 }}
          >
            {t('specialFeature.backToList', 'Back to List')}
          </MuiButton>
        </Paper>
      </Box>
    );
  }

  return (
    <>
      <Head title={currentDisplayTitle} />
      <Breadcrumb
        title={t('specialFeature.titleLong', '特集管理')}
        items={breadcrumbItems}
      />
      <Grid container spacing={2} sx={{ p: { xs: 1, md: 3 } }}>
        <Grid item xs={12} md={isEditing && showRightPanel ? 8.5 : 12}>
          {(specialFeature || !specialFeatureId) && (
            <ContentManagementSpecialFeatureForm
              specialFeature={specialFeature || ({} as SpecialFeature)}
              isEditing={isEditing}
              handleHeaderAction={handleMainActionButtonClick}
              formValues={formValues}
              setFormValue={handleSetFormValue}
            />
          )}
        </Grid>
        {isEditing && showRightPanel && (
          <Grid item xs={12} md={3.5}>
            <SpecialFeatureRightPanel
              type={specialFeatureId || formValues.id ? 'edit' : 'create'}
              initialPublicationDate={formValues.publicationDate}
              initialStatus={formValues.status}
              formIsValid={isFormValid}
              onSave={handleSaveFromPanel}
              onDelete={
                specialFeatureId || formValues.id
                  ? handleDeleteFromPanel
                  : undefined
              } // Only allow delete if editing existing
              isSubmitting={
                updateSpecialFeatureMutation.isPending ||
                deleteSpecialFeatureMutation.isPending
              }
            />
          </Grid>
        )}
      </Grid>
    </>
  );
};
