import DownloadIcon from '@mui/icons-material/Download';
import {
  Box,
  Button as MuiButton,
  Checkbox,
  FormControl,
  FormControlLabel,
  Grid,
  Paper,
  Radio,
  RadioGroup,
  Typography,
} from '@mui/material';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { NotificationSettingsType } from '@/bundles/model';
import { Breadcrumb } from '@/components/elements';
import { Button } from '@/components/forms/button';
import { Item } from '@/components/select';
import { Head } from '@/components/seo/head';
import { getDisplayNameDefault } from '@/constants';
import { GetEmailsRequest } from '@/features/contentManagement/infrastructure/repositories/email-repository';
import { useCountries } from '@/features/masterManagement/ui/hooks/country/use-countries';
import { routes } from '@/routes/routes';

import { useGetEmails } from '../../hooks/email/use-get-emails';

const languageOptions = [
  { code: 'en', name: 'English' },
  { code: 'es', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
  { code: 'fr', name: 'Fran<PERSON>' },
  { code: 'it', name: 'Italiano' },
  { code: 'ja', name: '日本語' },
  { code: 'ko', name: '한국어' },
  { code: 'zhCN', name: '简体中文' },
];

export const ContentManagementEmail = () => {
  const { t } = useTranslation();
  const getEmailsMutation = useGetEmails();
  const { data: fetchedCountries, isLoading: isLoadingCountries } =
    useCountries(true);
  const [optionCountries, setOptionCountries] = useState<Item[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // State for filters
  const [selectedType, setSelectedType] = useState('ALL');
  const [selectedCountries, setSelectedCountries] = useState<string[]>(
    optionCountries.map((c) => c.value),
  );
  const [selectedLanguages, setSelectedLanguages] = useState<string[]>(
    languageOptions.map((l) => l.code),
  );
  const [emailList, setEmailList] = useState<string[]>([]);
  const [resultCount, setResultCount] = useState(0);

  const handleCountryChange = (code: string) => {
    setSelectedCountries((prev) =>
      prev.includes(code) ? prev.filter((c) => c !== code) : [...prev, code],
    );
  };

  const handleLanguageChange = (code: string) => {
    setSelectedLanguages((prev) =>
      prev.includes(code) ? prev.filter((l) => l !== code) : [...prev, code],
    );
  };

  useEffect(() => {
    const items: Item[] =
      fetchedCountries && fetchedCountries.items
        ? fetchedCountries.items.map((country) => ({
            key: getDisplayNameDefault(country.displayName),
            value: country.code ?? '',
          }))
        : [];

    setOptionCountries(items);
    setSelectedCountries(items.map((c) => c.value));
  }, [fetchedCountries]);

  const breadcrumbItems = [
    { name: t('menu.home', 'ホーム'), href: routes.home.index },
    {
      name: t('menu.contentManagement', 'コンテンツ管理'),
      href: routes.contentManagement.index,
    },
    { name: t('emailDistribution.emailNews', 'メールニュース') },
  ];

  const handleApplyClick = () => {
    const params: GetEmailsRequest = {
      notificationSettings: [selectedType as NotificationSettingsType],
      countryCodes: selectedCountries,
      languages: selectedLanguages,
    };

    setIsLoading(true);
    getEmailsMutation.mutate(params, {
      onSuccess: (response) => {
        const emails =
          (response.data
            ?.map((item) => item.email)
            .filter(Boolean) as string[]) || [];

        setEmailList(emails);
        setResultCount(emails.length);
      },
      onError: () => {
        setEmailList([]);
        setResultCount(0);
      },
    });
    setIsLoading(false);
  };

  const handleDownloadClick = () => {
    if (emailList.length === 0) return;

    const csvContent = 'email\n' + emailList.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    link.setAttribute('href', url);
    link.setAttribute('download', 'email_list.csv');
    link.style.visibility = 'hidden';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return (
    <>
      <Head title={t('emailDistribution.emailNews', 'メールニュース')} />
      <Breadcrumb
        title={t('emailDistribution.emailNews', 'メールニュース')}
        items={breadcrumbItems}
      />

      <Paper elevation={0} sx={{ m: { xs: 1, md: 0 }, p: { xs: 2, md: 3 } }}>
        <Box
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          mb={3}
          pb={1}
          sx={{ borderBottom: 1.5, borderColor: 'black' }}
        >
          <Typography variant="h2" component="h1" fontWeight="bold">
            {t('emailDistribution.distributionList', '配信先リスト')}
          </Typography>
          <MuiButton
            variant="outlined"
            startIcon={
              <DownloadIcon
                sx={{
                  fontSize: '20px !important',
                  padding: '0',
                  margin: '0',
                  color:
                    !emailList.length || isLoadingCountries || isLoading
                      ? 'text.disabled'
                      : 'text.primary',
                }}
              />
            }
            size="large"
            disabled={!emailList.length || isLoadingCountries || isLoading}
            onClick={handleDownloadClick}
            className={
              !emailList.length || isLoadingCountries || isLoading
                ? '!text-onSurface !opacity-50 !bg-[#CDCDCD]'
                : '!text-onSurface  !bg-surfaceContainerHighest'
            }
            sx={{
              borderColor: 'divider',
              padding: '0',
              width: '138px',
              height: '48px',
              fontSize: '16px !important',
              fontWeight: 'bold',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              '&:hover': {
                borderColor: 'text.primary',
                backgroundColor: 'action.hover',
              },
            }}
          >
            ダウンロード
          </MuiButton>
        </Box>

        <Paper
          sx={{
            p: 3,
            boxShadow: 2,
          }}
        >
          <Box
            mb={4}
            sx={{
              borderBottom: 2,
              borderColor: 'divider',
              borderStyle: 'dashed',
              pb: 3,
            }}
          >
            <FormControl component="fieldset">
              <RadioGroup
                row
                aria-label="filter-type"
                name="filter-type"
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="flex gap-3"
                sx={{
                  '& .MuiFormControlLabel-root': {
                    '& .MuiFormControlLabel-label': {
                      fontSize: '14px !important',
                      color: 'text.onSurface',
                      fontWeight: 'bold',
                    },
                  },
                }}
              >
                <FormControlLabel
                  value="ALL"
                  control={
                    <Radio
                      sx={{
                        '& .MuiSvgIcon-root': {
                          color: '#006A6A !important',
                        },
                      }}
                    />
                  }
                  label={t('emailDistribution.filterType.all', 'ALL')}
                />
                <FormControlLabel
                  value="important"
                  control={<Radio />}
                  label={t(
                    'emailDistribution.filterType.important',
                    '重要なお知らせ',
                  )}
                  sx={{
                    '& .MuiSvgIcon-root': {
                      color: '#006A6A !important',
                    },
                  }}
                />
                <FormControlLabel
                  value="campaign"
                  control={<Radio />}
                  label={t(
                    'emailDistribution.filterType.campaign',
                    'キャンペーン・プロモーション情報',
                  )}
                  sx={{
                    '& .MuiSvgIcon-root': {
                      color: '#006A6A !important',
                    },
                  }}
                />
              </RadioGroup>
            </FormControl>
          </Box>

          <Box mb={4}>
            <Typography
              variant="h3"
              component="h2"
              gutterBottom
              fontWeight="medium"
              className="pb-2"
            >
              {t('emailDistribution.countryRegion', '国・地域')}
            </Typography>
            <div className="flex flex-wrap gap-3 border-b-2 border-dashed pb-3">
              {optionCountries.map((country) => (
                <div key={country.value}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={selectedCountries.includes(country.value)}
                        onChange={() => handleCountryChange(country.value)}
                        name={country.value}
                      />
                    }
                    label={country.key}
                    sx={{
                      '& .MuiFormControlLabel-label': {
                        fontSize: '14px',
                        fontWeight: 'bold',
                        color: 'text.onSurface',
                      },
                    }}
                  />
                </div>
              ))}
            </div>
          </Box>

          <Box mb={4}>
            <Typography
              variant="h3"
              component="h2"
              gutterBottom
              fontWeight="medium"
              className="pb-2"
            >
              {t('emailDistribution.language', '言語')}
            </Typography>
            <div className="flex flex-wrap gap-3 border-b-2 border-dashed pb-3">
              {languageOptions.map((language) => (
                <Grid item xs={6} sm={4} md={3} lg={2} key={language.code}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={selectedLanguages.includes(language.code)}
                        onChange={() => handleLanguageChange(language.code)}
                        name={language.code}
                      />
                    }
                    label={language.name}
                    sx={{
                      '& .MuiFormControlLabel-label': {
                        fontSize: '14px',
                        fontWeight: 'bold',
                        color: 'text.onSurface',
                      },
                    }}
                  />
                </Grid>
              ))}
            </div>
          </Box>

          <div className="flex justify-end w-full">
            <Typography variant="body2" sx={{ mb: { xs: 2, sm: 0 } }}>
              <span className="font-bold">
                {t('emailDistribution.itemCount', '件数:')}
              </span>{' '}
              <span>{resultCount.toLocaleString()}</span>
            </Typography>
          </div>

          <Box
            display="flex"
            flexDirection={{ xs: 'column', sm: 'row' }}
            justifyContent="space-between"
            alignItems="center"
            mt={3}
            pt={2}
            sx={{ borderTop: 1, borderColor: 'divider' }}
          >
            <Button
              variant="contained"
              size="large"
              onClick={handleApplyClick}
              sx={{
                minWidth: '100%',
                minHeight: '40px',
                py: 1,
                bgcolor: 'Background',
              }}
              className="!text-primary"
              disabled={isLoadingCountries || isLoading}
            >
              {t('common.button.apply', '適用')}
            </Button>
          </Box>
        </Paper>
      </Paper>
    </>
  );
};
