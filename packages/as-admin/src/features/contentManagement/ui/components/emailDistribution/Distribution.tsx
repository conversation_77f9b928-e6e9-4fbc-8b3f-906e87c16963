import { useNavigate } from 'react-router-dom';

import { But<PERSON> } from '@/components/forms/button';
import { Head } from '@/components/seo/head';
import { routes } from '@/routes/routes';

export const ContentManagementDistribution = () => {
  const navigate = useNavigate();

  return (
    <>
      <Head title="配信先リスト生成" />
      <div className="flex flex-col justify-center items-center h-screen bg-white">
        <h1 className="text-6xl font-bold mb-4">
          {'9-5-1/001 配信先リスト生成 - Distribution list generation'}
        </h1>
        <Button
          onClick={() =>
            navigate(
              `${routes.contentManagement.index}${routes.contentManagement.email.index}`,
            )
          }
        >
          {
            'Go back to the previous page メール配信管理 - Email distribution management'
          }
        </Button>
      </div>
    </>
  );
};
