import { useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError, AxiosRequestConfig } from 'axios';

import { GetNotificationsResponse, Notification } from '@/bundles/model';
import { AnnouncementFilters } from '@/features/contentManagement/domain/announcement/types';
import { announcementRepository } from '@/features/contentManagement/infrastructure/repositories/announcement-repository';
import { useAnnouncementArticleStore } from '@/features/contentManagement/ui/stores/announcement/announcement-article-store';

const repository = announcementRepository();

export const useGetAnnouncementArticles = () => {
  return useMutation<
    GetNotificationsResponse,
    AxiosError,
    {
      filters: AnnouncementFilters;
      options?: AxiosRequestConfig;
    }
  >({
    mutationFn: ({ filters, options }) =>
      repository.getAnnouncements(filters.page, filters.limit, options),
    onSuccess: (data) => {
      return data;
    },
    onError: (error) => {
      console.error('Failed to fetch announcements:', error);
      return [];
    },
  });
};

export const useGetAnnouncementArticlesById = () => {
  return useMutation<
    Notification,
    AxiosError,
    { id: string; options?: AxiosRequestConfig }
  >({
    mutationFn: async ({ id }) => {
      const announcement = await repository.getAnnouncementById(id);
      console.log('Fetched announcement by ID:', id, announcement);
      if (!announcement) {
        throw new Error('Announcement not found');
      }
      return announcement;
    },
    onSuccess: (data) => {
      console.log('Fetched announcement by ID successfully:', data);
    },
    onError: (error) => {
      console.error('Failed to fetch announcement by ID:', error);
    },
  });
};

export const useCreateAnnouncementArticle = () => {
  const queryClient = useQueryClient();
  const { setDialogState } = useAnnouncementArticleStore();

  return useMutation<Notification, AxiosError, Notification>({
    mutationFn: (data: Notification) => repository.createAnnouncement(data),
    onSuccess: (data) => {
      // Invalidate and refetch the announcements list to include the new one
      queryClient.invalidateQueries({ queryKey: ['announcements'] });
      // Optionally, you can set the query data for the new article to avoid a refetch on its detail page
      queryClient.setQueryData(['announcement', data.id], data);
      setDialogState({
        open: true,
        type: 'success',
        message: 'お知らせの作成に成功しました。',
      });
    },
    onError: (error) => {
      console.error('Failed to create announcement:', error);
      setDialogState({
        open: true,
        type: 'error',
        message: 'お知らせの作成に失敗しました。',
      });
    },
  });
};

export const useUpdateAnnouncementArticle = () => {
  const queryClient = useQueryClient();
  const { setDialogState } = useAnnouncementArticleStore();

  return useMutation<
    Notification,
    AxiosError,
    {
      id: string;
      data: Notification;
      existingAnnouncement?: Notification;
    }
  >({
    mutationFn: ({ id, data }) => repository.updateAnnouncement(id, data),
    onSuccess: (data) => {
      // Invalidate queries to refetch data
      queryClient.refetchQueries({ queryKey: ['announcement', data.id] });
      queryClient.refetchQueries({ queryKey: ['announcements'] });
      setDialogState({
        open: true,
        type: 'success',
        message: 'お知らせの更新に成功しました。',
      });
    },
    onError: (error) => {
      console.error('Failed to update announcement:', error);
      setDialogState({
        open: true,
        type: 'error',
        message: 'お知らせの更新に失敗しました。',
      });
    },
  });
};

// New: Hook for deleting an announcement
export const useDeleteAnnouncementArticle = () => {
  const queryClient = useQueryClient();
  const { setDialogState } = useAnnouncementArticleStore();

  return useMutation<void, AxiosError, string>({
    mutationFn: (id: string) => repository.deleteAnnouncement(id),
    onSuccess: () => {
      queryClient.refetchQueries({ queryKey: ['announcements'] });
      setDialogState({
        open: true,
        type: 'success',
        message: 'お知らせの削除に成功しました。',
      });
    },
    onError: (error) => {
      console.error('Failed to delete announcement:', error);
      setDialogState({
        open: true,
        type: 'error',
        message: 'お知らせの削除に失敗しました。',
      });
    },
  });
};
