import { DeleteOutline, Publish, SaveAs } from '@mui/icons-material';
import { Box, Card, CardContent, Divider, Typography } from '@mui/material';
import clsx from 'clsx';
import dayjs, { Dayjs } from 'dayjs';
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';

import { ContentStatus } from '@/bundles/model';
import { CommonDialog } from '@/components/dialog/CommonDialog';
import { Button } from '@/components/forms';
import { ApplicationDatePicker } from '@/components/forms/date-picker/ApplicationDatePicker';

interface AnnouncementRightPanelProps {
  type: 'create' | 'edit';
  initialPublicationDate?: string | Date | null;
  initialStatus?: ContentStatus;
  formIsValid: boolean;
  onSave: (publicationDate: Date, status: ContentStatus) => void;
  onDelete?: () => void;
  isSubmitting?: boolean;
}

export const AnnouncementRightPanel: React.FC<AnnouncementRightPanelProps> = ({
  type,
  initialPublicationDate,
  formIsValid,
  onSave,
  onDelete,
  isSubmitting,
}) => {
  const { t } = useTranslation();
  const [publicationDate, setPublicationDate] = useState<Dayjs | null>(
    initialPublicationDate ? dayjs(initialPublicationDate) : dayjs(),
  );
  const [dialogState, setDialogState] = useState<{
    open: boolean;
    type: 'alert' | 'success' | 'error' | 'confirm-delete';
    message: string;
  } | null>(null);

  useEffect(() => {
    setPublicationDate(
      initialPublicationDate ? dayjs(initialPublicationDate) : dayjs(),
    );
  }, [initialPublicationDate]);

  const handleSaveDraft = () => {
    onSave(
      publicationDate ? publicationDate.toDate() : new Date(),
      ContentStatus.Draft,
    );
  };

  const handlePublish = () => {
    if (!publicationDate) {
      toast.error(
        t(
          'announcement.error.publicationDateRequired',
          '公開するには更新適用日を指定してください。',
        ),
      );
      return;
    }
    onSave(publicationDate.toDate(), ContentStatus.Published);
  };

  const handleDialogOk = () => {
    onDelete?.();
    setDialogState(null);
  };

  const handleDialogCancel = () => {
    setDialogState(null);
  };

  const showDialog = () => {
    setDialogState({
      open: true,
      type: 'confirm-delete',
      message: 'この項目を削除しますか？',
    });
  };

  return (
    <Box className="flex justify-center items-start h-screen">
      <Card className="w-full border border-gray-200 rounded-lg shadow-sm overflow-hidden !py-3">
        <CardContent>
          <ApplicationDatePicker
            label={t('announcement.applyDateLabel', '更新適用日')}
            value={publicationDate}
            onChange={(date) => setPublicationDate(date ? dayjs(date) : null)}
          />

          <Divider className="border-t-1 border-outline" />

          <Box className="flex flex-col gap-4 pt-6">
            <Box className="flex gap-4 w-full">
              <Button
                variant="outlined"
                className="!w-1/2 !h-12"
                startIcon={
                  <SaveAs
                    className={clsx(
                      !formIsValid || isSubmitting
                        ? '!fill-[#A6A9A9]'
                        : '!fill-primary',
                    )}
                  />
                }
                onClick={handleSaveDraft}
                disabled={!formIsValid || isSubmitting}
              >
                <Typography
                  variant="h4"
                  className={clsx(
                    !formIsValid || isSubmitting
                      ? '!text-[#A6A9A9]'
                      : '!text-primary',
                  )}
                >
                  下書き
                </Typography>
              </Button>

              <Button
                variant="contained"
                className="!w-1/2 !h-12"
                startIcon={<Publish className="!fill-onPrimary" />}
                onClick={handlePublish}
                disabled={!formIsValid || isSubmitting || !publicationDate}
              >
                <Typography variant="h4" className="!text-onPrimary">
                  公開
                </Typography>
              </Button>
            </Box>

            {type === 'edit' && onDelete && (
              <Button
                variant="text"
                className="!w-full justify-center"
                startIcon={
                  <DeleteOutline
                    className={clsx(
                      isSubmitting ? '!fill-[#A6A9A9]' : '!fill-red-500',
                    )}
                  />
                }
                onClick={() => showDialog()}
                disabled={isSubmitting}
              >
                <Typography
                  variant="h4"
                  className={clsx(
                    isSubmitting ? '!text-[#A6A9A9]' : '!text-red-500',
                  )}
                >
                  削除
                </Typography>
              </Button>
            )}
          </Box>

          {dialogState && (
            <CommonDialog
              open={dialogState.open}
              type={dialogState.type}
              message={dialogState.message}
              onOk={handleDialogOk}
              onCancel={handleDialogCancel}
              okText="削除"
              cancelText="キャンセル"
            />
          )}
        </CardContent>
      </Card>
    </Box>
  );
};
