import { Warning } from '@mui/icons-material';
import {
  Box,
  Button as Mu<PERSON><PERSON><PERSON>on,
  CircularProgress,
  Paper,
  Typography,
  Grid,
} from '@mui/material';
import { useEffect, useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';

import {
  ContentStatus,
  Notification,
  NotificationArticleContent,
  NotificationType,
} from '@/bundles/model';
import { CommonDialog } from '@/components/dialog/CommonDialog';
import { Breadcrumb } from '@/components/elements';
import { Head } from '@/components/seo/head';
import { DateFormat } from '@/constants/date';
import { dateFormat } from '@/constants/utils';
import { useAnnouncementArticleStore } from '@/features/contentManagement/ui/stores/announcement/announcement-article-store';
import { useUploadFile } from '@/features/uploadFile/ui/hooks/use-upload-file';
import { routes } from '@/routes/routes';

import { ContentManagementAnnouncementForm } from './AnnouncementForm';
import { AnnouncementRightPanel } from './AnnouncementRightPanel';
import {
  useGetAnnouncementArticlesById,
  useUpdateAnnouncementArticle,
  useDeleteAnnouncementArticle,
} from './hook/use-create-announcement';

const setNestedValue = (obj: any, path: string, value: any) => {
  const keys = path.split('.');
  let current = obj;
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!current[key] || typeof current[key] !== 'object') {
      // If the next key is a number, create an array, otherwise an object
      if (!isNaN(parseInt(keys[i + 1], 10))) {
        current[key] = [];
      } else {
        current[key] = {};
      }
    }
    current = current[key];
  }
  current[keys[keys.length - 1]] = value;
  return obj;
};

export const ContentManagementAnnouncementDetails = () => {
  const { announcementId } = useParams<{ announcementId: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [isEditing, setIsEditing] = useState(false);
  const [showRightPanel, setShowRightPanel] = useState(false);
  const { dialogState, setDialogState } = useAnnouncementArticleStore();

  // Clear any existing dialog state on component mount
  useEffect(() => {
    setDialogState(null);
  }, [setDialogState]);

  const {
    data: notification,
    isPending,
    error: fetchError,
    mutate: fetchNotification,
  } = useGetAnnouncementArticlesById();

  const updateNotificationMutation = useUpdateAnnouncementArticle();
  const deleteNotificationMutation = useDeleteAnnouncementArticle();

  const [formValues, setFormValues] = useState<Notification>({
    type: NotificationType.CampaignPromotion,
    countryCodes: [],
    publishedAt: new Date().toISOString(),
    status: 'draft',
    content: {},
  });

  useEffect(() => {
    if (announcementId) {
      fetchNotification({ id: announcementId });
    } else {
      navigate(routes.contentManagement.announcement.index);
      setDialogState({
        open: true,
        type: 'error',
        message: t('announcement.notFound', 'お知らせが見つかりません。'),
      });
    }
  }, [announcementId, fetchNotification, navigate, t, setDialogState]);

  const { mutateAsync: uploadFile } = useUploadFile(() => {
    setDialogState({
      open: true,
      type: 'error',
      message: 'ファイルのアップロードに失敗しました。',
    });
  });

  useEffect(() => {
    if (notification) {
      setFormValues({
        id: notification.id,
        type: notification.type,
        countryCodes: notification.countryCodes || [],
        status: notification.status || 'draft',
        content: Object.entries(notification.content || {}).reduce(
          (acc, [lang, contentData]) => {
            acc[lang] = {
              active: contentData.active || false,
              title: contentData.title || '',
              message: contentData.message || '',
              keyImage: contentData.keyImage,
              articleImages: contentData.articleImages || [],
            };
            return acc;
          },
          {} as { [key: string]: NotificationArticleContent },
        ),
      });
      // If we fetch new data, and we are not explicitly in an interaction that shows the panel, hide it.
      if (!isEditing) {
        setShowRightPanel(false);
      }
    }
  }, [notification, isEditing]);

  const handleMainActionButtonClick = useCallback(() => {
    if (!isEditing) {
      setIsEditing(true);
      setShowRightPanel(false); // Ensure panel is hidden when entering edit mode
    } else {
      // If currently editing, clicking the "Done" button will show the right panel
      setShowRightPanel(true);
    }
  }, [isEditing]);

  const handleSetFormValue = useCallback((fieldName: string, value: any) => {
    setFormValues((prev) => {
      const newValues = JSON.parse(JSON.stringify(prev));
      setNestedValue(newValues, fieldName, value);
      return newValues;
    });
  }, []);

  const handleSaveFromPanel = useCallback(
    async (publicationDate: Date, status: ContentStatus) => {
      if (!announcementId || !notification) {
        setDialogState({
          open: true,
          type: 'error',
          message: '保存できません：お知らせのデータが不足しています。',
        });
        return;
      }

      try {
        const updatedContent: { [key: string]: NotificationArticleContent } =
          {};

        // Process each language content
        for (const [lang, contentData] of Object.entries(
          formValues.content || {},
        )) {
          const processedContent = { ...contentData };

          // Upload keyImage if it's a blob URL (newly uploaded)
          if (
            contentData.keyImage &&
            contentData.keyImage.startsWith('blob:')
          ) {
            const response = await fetch(contentData.keyImage);
            const blob = await response.blob();
            const file = new File([blob], 'keyImage.jpg', { type: blob.type });
            const uploadedKeyImage = await uploadFile(file);
            processedContent.keyImage = uploadedKeyImage.key;
          }

          // Upload articleImages if they are blob URLs (newly uploaded)
          if (
            contentData.articleImages &&
            contentData.articleImages.length > 0
          ) {
            const uploadedArticleImages = await Promise.all(
              contentData.articleImages.map(async (imageUrl) => {
                if (imageUrl && imageUrl.startsWith('blob:')) {
                  const response = await fetch(imageUrl);
                  const blob = await response.blob();
                  const file = new File([blob], 'articleImage.jpg', {
                    type: blob.type,
                  });
                  const uploadedImage = await uploadFile(file);
                  return uploadedImage.key;
                }
                return imageUrl; // Keep existing URL if not a blob
              }),
            );
            processedContent.articleImages =
              uploadedArticleImages.filter(Boolean);
          }

          updatedContent[lang] = processedContent;
        }

        const date = publicationDate?.toISOString?.() || formValues.publishedAt;
        const publishedAt: string | undefined = date
          ? dateFormat(date, DateFormat.fullDateWithHyphen)
          : undefined;

        const dataToSave = {
          ...formValues,
          publishedAt,
          status,
          content: updatedContent,
        };

        updateNotificationMutation.mutate(
          {
            id: announcementId,
            data: dataToSave,
          },
          {
            onSuccess: () => {
              setIsEditing(false);
              setShowRightPanel(false);
              fetchNotification({ id: announcementId });
            },
            onError: () => {
              setDialogState({
                open: true,
                type: 'error',
                message: 'お知らせの保存に失敗しました。',
              });
            },
          },
        );
      } catch (error) {
        setDialogState({
          open: true,
          type: 'error',
          message: 'ファイルのアップロードに失敗しました。',
        });
      }
    },
    [
      announcementId,
      notification,
      formValues,
      updateNotificationMutation,
      fetchNotification,
      uploadFile,
      setDialogState,
    ],
  );

  const handleDeleteFromPanel = useCallback(async () => {
    if (!announcementId) {
      setDialogState({
        open: true,
        type: 'error',
        message: '削除できません：お知らせIDが不足しています。',
      });
      return;
    }
    deleteNotificationMutation.mutate(announcementId, {
      onSuccess: () => {
        navigate(
          `${routes.contentManagement.index}${routes.contentManagement.announcement.index}`,
        );
      },
      onError: () => {
        setDialogState({
          open: true,
          type: 'error',
          message: 'お知らせの削除に失敗しました。',
        });
      },
    });
  }, [announcementId, deleteNotificationMutation, navigate, setDialogState]);

  const handleDialogClose = () => {
    setDialogState(null);
  };

  const isFormValid = useMemo(() => {
    if (!formValues.content) return false;
    const hasAtLeastOneTitle = Object.values(formValues.content).some(
      (langData) => langData?.title && langData.title.trim() !== '',
    );
    return hasAtLeastOneTitle;
  }, [formValues]);

  const currentTitle = useMemo(() => {
    const langToUse = 'en';
    if (isEditing && formValues.content?.[langToUse]?.title) {
      return (
        formValues.content[langToUse]?.title ||
        t('announcement.untitled', 'Untitled Announcement')
      );
    }
    return (
      notification?.content?.[langToUse]?.title ||
      notification?.content?.en?.title ||
      t('announcement.untitled', 'Untitled Announcement')
    );
  }, [notification?.content, formValues.content, isEditing, t]);

  const breadcrumbItems = useMemo(
    () => [
      { name: t('menu.home'), href: routes.home.index },
      {
        name: t('menu.contentManagement'),
        href: routes.contentManagement.index,
      },
      {
        name: t('announcement.title', 'お知らせ'),
        href: `${routes.contentManagement.index}${routes.contentManagement.announcement.index}`,
      },
      {
        name: t('announcement.detailsLoading', '記事'),
      },
    ],
    [t],
  );

  if (isPending) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="80vh"
      >
        <CircularProgress />{' '}
        <Typography ml={2}>{t('common.loading', 'Loading...')}</Typography>
      </Box>
    );
  }

  if (fetchError) {
    return (
      <Box p={3}>
        <Head
          title={t('announcement.detailsError', 'Error Loading Announcement')}
        />
        <Breadcrumb
          title={t('announcement.detailsError', 'Error')}
          items={breadcrumbItems.slice(0, -1)}
        />
        <Paper elevation={3} sx={{ p: 3, mt: 2, textAlign: 'center' }}>
          <Warning color="error" sx={{ fontSize: 48 }} />
          <Typography variant="h6" color="error" mt={1}>
            {t(
              'announcement.fetchErrorMsg',
              'Could not load announcement details.',
            )}
          </Typography>
          <Typography>{fetchError?.message || 'Unknown error'}</Typography>
          <MuiButton
            variant="contained"
            onClick={() =>
              navigate(routes.contentManagement.announcement.index)
            }
            sx={{ mt: 2 }}
          >
            {t('announcement.backToList', 'Back to Announcements List')}
          </MuiButton>
        </Paper>
      </Box>
    );
  }

  if (!notification) {
    return (
      <Box p={3}>
        <Head title={t('announcement.notFound', 'Announcement Not Found')} />
        <Breadcrumb
          title={t('announcement.notFound', 'Not Found')}
          items={breadcrumbItems.slice(0, -1)}
        />
        <Paper elevation={3} sx={{ p: 3, mt: 2, textAlign: 'center' }}>
          <Warning sx={{ fontSize: 48 }} />
          <Typography variant="h6" mt={1}>
            {t(
              'announcement.notFoundMsg',
              'The requested announcement could not be found.',
            )}
          </Typography>
          <MuiButton
            variant="contained"
            onClick={() =>
              navigate(routes.contentManagement.announcement.index)
            }
            sx={{ mt: 2 }}
          >
            {t('announcement.backToList', 'Back to Announcements List')}
          </MuiButton>
        </Paper>
      </Box>
    );
  }

  return (
    <>
      <Head title={currentTitle} />
      <Breadcrumb title="お知らせ" items={breadcrumbItems} />
      <Grid container spacing={2} sx={{ p: { xs: 1, md: 3 } }}>
        <Grid item xs={12} md={isEditing && showRightPanel ? 8.5 : 12}>
          <ContentManagementAnnouncementForm
            isEditing={isEditing}
            handleHeaderAction={handleMainActionButtonClick}
            formValues={formValues}
            setFormValue={handleSetFormValue}
            showRightPanel={showRightPanel}
            announcement={notification}
          />
        </Grid>
        {isEditing && showRightPanel && (
          <Grid item xs={12} md={3.5}>
            <AnnouncementRightPanel
              type="edit"
              initialPublicationDate={formValues.publishedAt}
              initialStatus={formValues.status}
              formIsValid={isFormValid}
              onSave={handleSaveFromPanel}
              onDelete={handleDeleteFromPanel}
              isSubmitting={
                updateNotificationMutation.isPending ||
                deleteNotificationMutation.isPending
              }
            />
          </Grid>
        )}
      </Grid>

      {dialogState && (
        <CommonDialog
          open={dialogState.open}
          type={dialogState.type}
          message={dialogState.message}
          onOk={handleDialogClose}
          onCancel={handleDialogClose}
          okText="OK"
          cancelText="戻る"
        />
      )}
    </>
  );
};
