import { useNavigate } from 'react-router-dom';

import { But<PERSON> } from '@/components/forms/button';
import { Head } from '@/components/seo/head';
import { routes } from '@/routes/routes';

export const ContentManagementAnnouncementImage = () => {
  const navigate = useNavigate();
  const router = `${routes.contentManagement.index}${routes.contentManagement.announcement.index}`;

  return (
    <>
      <Head title="記事画像アップローダ" />
      <div className="flex flex-col justify-center items-center h-screen bg-white">
        <h1 className="text-6xl font-bold mb-4">
          {'9-3-1-4/001 記事画像アップローダ - Article Image Uploader'}
        </h1>
        <Button onClick={() => navigate(router)}>
          {
            'Go back to the previous page お知らせ管理 - Announcement management'
          }
        </Button>
      </div>
    </>
  );
};
