import {
  AddPhotoAlternate,
  Check,
  DeleteOutline as Delete<PERSON><PERSON>,
  Edit as EditIcon,
} from '@mui/icons-material';
import {
  Box,
  Button as MuiButton,
  Checkbox,
  Chip,
  FormControlLabel,
  Grid,
  IconButton,
  InputAdornment,
  Paper,
  Radio,
  RadioGroup,
  Tab,
  Tabs,
  TextField,
  Typography,
  Divider,
} from '@mui/material';
import { format, isValid, parseISO } from 'date-fns';
import DOMPurify from 'dompurify';
import { ChangeEvent, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Notification } from '@/bundles/model';
import { AnimatedCopyButton } from '@/components/feedback';
import { MultilingualString } from '@/features/contentManagement/domain/announcement/types';
import { CloseIcon } from '@/features/userManagement/ui/components';
import { getImageUrl } from '@/utils/image-utils';

const availableLanguages: Array<{
  code: keyof MultilingualString;
  name: string;
}> = [
  { code: 'en', name: 'EN' },
  { code: 'es', name: 'ES' },
  { code: 'fr', name: 'FR' },
  { code: 'it', name: 'IT' },
  { code: 'ja', name: 'JA' },
  { code: 'ko', name: 'KO' },
  { code: 'zhCN', name: 'zhCN' },
];

// TODO: Change to master country from DB
const allDisplayCountries = [
  { code: 'AUS', name: 'Australia' },
  { code: 'FRA', name: 'France' },
  { code: 'HKG', name: 'Hong Kong' },
  { code: 'ITA', name: 'Italy' },
  { code: 'JPN', name: 'Japan' },
  { code: 'MCO', name: 'Monaco' },
  { code: 'SAU', name: 'Saudi Arabia' },
  { code: 'SGP', name: 'Singapore' },
  { code: 'KOR', name: 'South Korea' },
  { code: 'ESP', name: 'Spain' },
  { code: 'ARE', name: 'United Arab Emirates' },
  { code: 'GBR', name: 'United Kingdom' },
  { code: 'USA', name: 'United States' },
];

const categoryMapping: Record<string, string> = {
  CAMPAIGN_PROMOTION: 'キャンペーン・プロモーション情報',
  IMPORTANT_SYSTEM: '重要なお知らせ',
};

const formatDate = (dateString?: string | Date): string => {
  if (!dateString) return '-';
  const date =
    typeof dateString === 'string' ? parseISO(dateString) : dateString;
  return isValid(date) ? format(date, 'yyyy.MM.dd HH:mm') : '-';
};

interface ArticleFormProps {
  announcement: Notification;
  handleHeaderAction?: () => void;
  handleEdit?: () => void;
  isEditing?: boolean;
  isNew?: boolean;
  formValues: Notification;
  setFormValue: (fieldName: string, value: any) => void;
  showRightPanel?: boolean;
}

export const ContentManagementAnnouncementForm: React.FC<ArticleFormProps> = ({
  announcement,
  handleHeaderAction,
  isEditing,
  isNew,
  formValues,
  setFormValue,
}) => {
  const { t } = useTranslation();
  const [activeLangTab, setActiveLangTab] =
    useState<keyof MultilingualString>('en');
  const [errors, setErrors] = useState<Record<string, string | undefined>>({});

  // eslint-disable-next-line
  const validateField = (fieldPath: string, value: string) => {
    let message: string | undefined;

    if (fieldPath.includes('.title')) {
      if (value.length > 300) {
        message = t(
          'common.validation.maxLength',
          'タイトルは300文字を超えることはできません。',
          {
            count: 300,
          },
        );
      }
    } else if (fieldPath.includes('.message')) {
      if (value.length > 1000) {
        message = t(
          'common.validation.maxLength',
          '本文は1000文字を超えることはできません。',
          {
            count: 1000,
          },
        );
      }
    }

    setErrors((prev) => ({ ...prev, [fieldPath]: message }));
  };

  const hasErrors = useMemo(
    () => Object.values(errors).some((error) => !!error),
    [errors],
  );

  const handleTabChange = (
    _event: React.SyntheticEvent,
    newValue: keyof MultilingualString,
  ) => {
    setActiveLangTab(newValue);

    // Auto-activate EN content when switching to EN tab during editing/creating
    if ((isEditing || isNew) && newValue === 'en') {
      handleInputChange(`content.${newValue}.active`, true);
    }
  };

  const displayContent = useMemo(() => {
    if (isEditing || isNew) {
      return formValues?.content?.[activeLangTab]?.message || '';
    }
    const contentData =
      announcement.content?.[activeLangTab] || announcement.content?.en;
    return contentData?.message || '';
  }, [announcement?.content, activeLangTab, isEditing, isNew, formValues]);

  const sanitizedContent = DOMPurify.sanitize(displayContent);
  const announcementUrl = `http://sample.com/sample/${announcement.id}.html`;

  const handleInputChange = useCallback(
    (fieldPath: string, value: any) => {
      setFormValue(fieldPath, value);
      if (typeof value === 'string') {
        validateField(fieldPath, value);
      }
    },
    [setFormValue, validateField],
  );

  // Auto-activate EN content when component loads in editing/creating mode with EN tab
  useEffect(() => {
    if ((isEditing || isNew) && activeLangTab === 'en') {
      handleInputChange(`content.${activeLangTab}.active`, true);
    }
  }, [isEditing, isNew, activeLangTab, handleInputChange]);

  const handleArticleImageUpload = (
    event: ChangeEvent<HTMLInputElement>,
    index: number,
  ) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      const previewUrl = URL.createObjectURL(file);
      const currentImages =
        formValues.content?.[activeLangTab]?.articleImages || [];
      const newImages = [...currentImages];

      while (newImages.length <= index) {
        newImages.push('');
      }

      newImages[index] = previewUrl;
      handleInputChange(`content.${activeLangTab}.articleImages`, newImages);
    }
  };

  const removeArticleImage = (index: number) => {
    const currentImages =
      formValues.content?.[activeLangTab]?.articleImages || [];
    const newImages = [...currentImages];
    newImages[index] = '';
    handleInputChange(`content.${activeLangTab}.articleImages`, newImages);
  };

  const handleImageUpload = (
    event: ChangeEvent<HTMLInputElement>,
    fieldPath: string,
  ) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      const previewUrl = URL.createObjectURL(file);
      handleInputChange(fieldPath, previewUrl);
    }
  };

  const removeImage = (fieldPath: string) => {
    handleInputChange(fieldPath, '');
  };

  const headerButtonLabel = useMemo(() => {
    if (!isEditing) {
      return t('common.edit', '編集');
    }
    return t('common.done', '完了');
  }, [isEditing, t]);

  const mainTitle = isEditing
    ? formValues.content?.en?.title || ''
    : announcement.content?.en?.title ||
      t('announcement.untitled', 'Untitled Announcement');

  return (
    <>
      <Paper elevation={0} sx={{ m: { xs: 0 } }}>
        <Grid
          container
          justifyContent="space-between"
          alignItems="center"
          gap={1}
        >
          <Grid item xs={12} sm>
            {isEditing ? (
              <TextField
                fullWidth
                multiline
                label={t('announcement.articleTitleLabel', '記事タイトル')}
                variant="outlined"
                value={formValues.content?.en?.title || ''}
                onChange={(e) =>
                  handleInputChange('content.en.title', e.target.value)
                }
                error={!!errors['content.en.title']}
                helperText={
                  <Box
                    component="span"
                    sx={{ display: 'flex', justifyContent: 'space-between' }}
                  >
                    <span>{errors['content.en.title']}</span>
                    <span>
                      {formValues.content?.en?.title?.length || 0} / 300
                    </span>
                  </Box>
                }
                placeholder={t('common.pleaseEnter', '入力してください')}
                InputProps={{
                  endAdornment: formValues.content?.[activeLangTab]?.title && (
                    <InputAdornment position="start">
                      <CloseIcon
                        onClick={() =>
                          setFormValue(`content.${activeLangTab}.title`, '')
                        }
                        className="cursor-pointer mr-2 !fill-onSurface"
                      />
                    </InputAdornment>
                  ),
                }}
                sx={{ mb: { xs: 1, sm: 0 } }}
              />
            ) : (
              <Typography
                variant="h1"
                component="h1"
                sx={{
                  fontWeight: 'bold',
                  color: '#1A2027',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
              >
                {mainTitle}
              </Typography>
            )}
          </Grid>
          <Grid
            item
            xs={12}
            sm="auto"
            container
            justifyContent={{ xs: 'flex-start', sm: 'flex-end' }}
            alignItems="center"
            spacing={1}
          >
            {announcement.status === 'draft' && !isEditing && (
              <Grid item>
                <Chip
                  label={t('announcement.status.draft', '下書き')}
                  size="small"
                  sx={{
                    backgroundColor: '#283430',
                    color: 'white',
                    fontWeight: 'bold',
                    borderRadius: '16px',
                    height: '32px',
                    padding: '0 12px',
                  }}
                />
              </Grid>
            )}
            <Grid item>
              <MuiButton
                variant="contained"
                size="small"
                onClick={handleHeaderAction}
                startIcon={isEditing ? <Check /> : <EditIcon />}
                disabled={isEditing && hasErrors}
                className={
                  isEditing
                    ? '!bg-secondaryContainer !hover:bg-onSurfaceContainerLow !rounded-[4px] !text-black !min-w-20'
                    : '!bg-surfaceContainerLow !hover:bg-onSurfaceContainerLow !rounded-[4px] !text-black !min-w-20'
                }
              >
                {headerButtonLabel}
              </MuiButton>
            </Grid>
          </Grid>
        </Grid>

        <Box
          sx={{
            mt: 1.5,
            display: 'flex',
            alignItems: 'center',
            flexWrap: 'wrap',
            gap: 2,
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
              ID:
            </Typography>
            <Typography
              variant="subtitle2"
              sx={{ ml: 0.5, fontWeight: 'medium' }}
            >
              {announcement.id}
            </Typography>
            <AnimatedCopyButton
              value={announcement.id ?? ''}
              size="small"
              sx={{ color: 'primary.main' }}
              className="border border-primary text-primary rounded-[100%] p-1"
            />
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
              URL:
            </Typography>
            <Typography
              variant="subtitle2"
              component="a"
              href={announcementUrl}
              target="_blank"
              rel="noopener noreferrer"
              sx={{
                color: '#2D3748',
                ml: 0.5,
                fontWeight: 'medium',
                wordBreak: 'break-all',
                textDecoration: 'none',
                '&:hover': { textDecoration: 'underline' },
              }}
            >
              {announcementUrl}
            </Typography>
            <AnimatedCopyButton
              value={announcementUrl}
              size="small"
              sx={{ ml: 0.5, color: 'primary.main' }}
              className="border-primary text-primary rounded-[100%] p-1"
            />
          </Box>
        </Box>

        <Divider className="h-[2px]" sx={{ my: 1, background: '#000' }} />

        <Box className="flex gap-4 pt-1 pb-6">
          <Box className="flex items-center gap-1">
            <Typography variant="subtitle2" className="!font-bold">
              {t('common.createdAt', '作成日')}:&nbsp;
            </Typography>
            <Typography variant="subtitle2">
              {formatDate(announcement.createdAt)}
            </Typography>
          </Box>
          <Box className="flex items-center gap-1">
            <Typography variant="subtitle2" className="!font-bold">
              {t('common.updatedAt', '最終更新日')}:&nbsp;
            </Typography>
            <Typography variant="subtitle2">
              {isEditing ? '--' : formatDate(announcement.updatedAt)}
            </Typography>
          </Box>
          <Box className="flex items-center gap-1">
            <Typography variant="subtitle2" className="!font-bold">
              {t('common.publishedAt', '更新適用日')}:&nbsp;
            </Typography>
            <Typography variant="subtitle2">
              {announcement.status !== 'draft' && !isEditing
                ? formatDate(announcement.publishedAt)
                : '--'}
            </Typography>
          </Box>
        </Box>
        <Box className="bg-[#0000000D] px-4 py-6 rounded-lg">
          <Box
            mb={2.5}
            sx={{
              borderBottom: 1,
              borderColor: 'outline.variant',
              pb: 1.5,
              borderStyle: 'dashed',
            }}
            className="px-4"
          >
            <RadioGroup
              row
              value={isEditing ? formValues.type : announcement.type}
              name="announcement-category"
              onChange={(e) =>
                isEditing && handleInputChange('type', e.target.value)
              }
            >
              {Object.keys(categoryMapping).map((key) => (
                <FormControlLabel
                  key={key}
                  value={key}
                  control={
                    <Radio
                      sx={{
                        '& .MuiSvgIcon-root': {
                          color: isEditing
                            ? '#006A6A !important'
                            : 'text.onSurface !important',
                        },
                      }}
                    />
                  }
                  label={categoryMapping[key]}
                  disabled={!isEditing}
                  sx={{
                    '& .MuiFormControlLabel-label': {
                      fontSize: '14px',
                      fontWeight: 'bold',
                    },
                  }}
                />
              ))}
            </RadioGroup>
          </Box>
          <Box>
            <Grid container spacing={{ xs: 0, sm: 1 }} className="px-6">
              {allDisplayCountries.map((country) => (
                <Box key={country.code} className="flex gap-3">
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={(isEditing
                          ? formValues.countryCodes
                          : announcement.countryCodes
                        )?.includes(country.code)}
                        color="secondary"
                        disabled={!isEditing}
                        sx={{
                          '& .MuiFormControlLabel-label': {
                            fontSize: '14px !important',
                            fontWeight: 'bold !important',
                          },
                        }}
                        onChange={(e) => {
                          if (isEditing) {
                            const currentCountries = new Set(
                              formValues.countryCodes || [],
                            );
                            if (e.target.checked) {
                              currentCountries.add(country.code);
                            } else {
                              currentCountries.delete(country.code);
                            }
                            handleInputChange(
                              'countryCodes',
                              Array.from(currentCountries),
                            );
                          }
                        }}
                      />
                    }
                    label={country.name}
                    disabled={!isEditing}
                    sx={{
                      '& .MuiFormControlLabel-label': {
                        fontSize: '14px',
                        fontWeight: 'bold',
                        color: 'text.onSurface',
                      },
                    }}
                  />
                </Box>
              ))}
            </Grid>
          </Box>
        </Box>
        <Box
          sx={{
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderRadius: 2,
            marginTop: 3,
          }}
        >
          <Tabs
            value={activeLangTab}
            onChange={handleTabChange}
            aria-label="language tabs"
            variant="scrollable"
            scrollButtons="auto"
            className="bg-[#0000000D]"
            sx={{
              '& .MuiTabs-flexContainer': {
                justifyContent: 'center',
              },
              '& .MuiTab-root': {
                minWidth: 60,
                maxWidth: 30,
                padding: '8px 16px',
              },
            }}
          >
            {availableLanguages.map((lang) => {
              const isPublished = formValues.content?.[lang.code]?.active;
              return (
                <Tab
                  key={lang.code}
                  label={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {lang.name}
                      {isPublished && (
                        <Box
                          sx={{
                            width: 8,
                            height: 8,
                            borderRadius: '50%',
                            backgroundColor: 'error.main',
                          }}
                        />
                      )}
                    </Box>
                  }
                  value={lang.code}
                  disabled={
                    !isEditing &&
                    !(
                      announcement.content &&
                      announcement.content[lang.code]?.message
                    )
                  }
                />
              );
            })}
          </Tabs>
          <Box p={{ xs: 2, md: 3 }}>
            <Grid container spacing={3} sx={{ py: 1.5 }}>
              <Grid
                item
                xs={12}
                md={6}
                sx={{
                  borderRight: { md: '1px dashed' },
                  borderColor: { md: 'divider' },
                  pr: { md: 2 },
                }}
              >
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={
                        (isEditing || isNew) && activeLangTab === 'en'
                          ? true
                          : isEditing
                            ? formValues.content?.[activeLangTab]?.active ??
                              false
                            : announcement.content?.[activeLangTab]?.active
                      }
                      disabled={
                        !isEditing ||
                        ((isEditing || isNew) && activeLangTab === 'en')
                      }
                      onChange={(e) =>
                        isEditing &&
                        activeLangTab !== 'en' &&
                        handleInputChange(
                          `content.${activeLangTab}.active`,
                          e.target.checked,
                        )
                      }
                    />
                  }
                  label={t('announcement.isPublicLabel', '公開')}
                />
                <Box className="py-4">
                  {isEditing && activeLangTab !== 'en' ? (
                    <TextField
                      fullWidth
                      multiline
                      label={t(
                        'announcement.articleTitleLabel',
                        '記事タイトル',
                      )}
                      variant="outlined"
                      value={formValues.content?.[activeLangTab]?.title || ''}
                      onChange={(e) =>
                        handleInputChange(
                          `content.${activeLangTab}.title`,
                          e.target.value,
                        )
                      }
                      error={!!errors[`content.${activeLangTab}.title`]}
                      helperText={
                        <Box
                          component="span"
                          sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                          }}
                        >
                          <span>
                            {errors[`content.${activeLangTab}.title`]}
                          </span>
                          <span>
                            {formValues.content?.[activeLangTab]?.title
                              ?.length || 0}{' '}
                            / 300
                          </span>
                        </Box>
                      }
                      placeholder="入力してください"
                      sx={{
                        flexGrow: 1,
                        mr: { sm: 2 },
                        mb: { xs: 1, sm: 0 },
                        '& .MuiInputBase-root': {
                          alignItems: 'flex-start',
                          resize: 'vertical',
                          overflow: 'auto',
                        },
                      }}
                      InputProps={{
                        endAdornment: formValues.content?.[activeLangTab]
                          ?.title && (
                          <InputAdornment position="start">
                            <CloseIcon
                              onClick={() =>
                                setFormValue(
                                  `content.${activeLangTab}.title`,
                                  '',
                                )
                              }
                              className="cursor-pointer mr-2 !fill-onSurface"
                            />
                          </InputAdornment>
                        ),
                      }}
                    />
                  ) : (
                    <>
                      <Typography
                        variant="subtitle2"
                        className="!font-bold"
                        gutterBottom
                      >
                        記事タイトル
                      </Typography>
                      <Typography
                        variant="body2"
                        gutterBottom
                        className="pt-1"
                        sx={{
                          wordBreak: 'break-word',
                          lineHeight: 1.2,
                        }}
                      >
                        {(isEditing || isNew) && activeLangTab === 'en'
                          ? '--'
                          : announcement.content?.[activeLangTab]?.title ||
                            t('announcement.untitled', 'Untitled Announcement')}
                      </Typography>
                    </>
                  )}
                </Box>
                {!isEditing && (
                  <Typography
                    variant="subtitle2"
                    className="!font-bold"
                    gutterBottom
                  >
                    {t('announcement.contentLabel', '本文')}
                  </Typography>
                )}
                {isEditing ? (
                  <TextField
                    fullWidth
                    multiline
                    minRows={8}
                    label={t('announcement.contentLabel', '本文')}
                    value={formValues.content?.[activeLangTab]?.message || ''}
                    onChange={(e) =>
                      handleInputChange(
                        `content.${activeLangTab}.message`,
                        e.target.value,
                      )
                    }
                    error={!!errors[`content.${activeLangTab}.message`]}
                    helperText={
                      <Box
                        component="span"
                        sx={{
                          display: 'flex',
                          justifyContent: 'space-between',
                        }}
                      >
                        <span>
                          {errors[`content.${activeLangTab}.message`]}
                        </span>
                        <span>
                          {formValues.content?.[activeLangTab]?.message
                            ?.length || 0}{' '}
                          / 1000
                        </span>
                      </Box>
                    }
                    placeholder="入力してください"
                    variant="outlined"
                    InputProps={{
                      endAdornment: formValues.content?.[activeLangTab]
                        ?.message && (
                        <InputAdornment position="start">
                          <CloseIcon
                            onClick={() =>
                              setFormValue(
                                `content.${activeLangTab}.message`,
                                '',
                              )
                            }
                            className="cursor-pointer mr-2 !fill-onSurface"
                          />
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      '& .MuiInputBase-root': {
                        alignItems: 'flex-start',
                        resize: 'vertical',
                        overflow: 'auto',
                      },
                    }}
                  />
                ) : sanitizedContent ? (
                  <Box
                    dangerouslySetInnerHTML={{ __html: sanitizedContent }}
                    className="!text-sm !text-black"
                    sx={{
                      maxHeight: 300,
                      overflowY: 'auto',
                    }}
                  />
                ) : (
                  <Typography>
                    {t(
                      'announcement.noContentAvailable',
                      'No content available for this language.',
                    )}
                  </Typography>
                )}
              </Grid>
              <Grid item xs={12} md={6}>
                <Box
                  mb={3}
                  className="!border-outlineVariant !rounded-md"
                  sx={
                    isEditing
                      ? {
                          border: 1,
                          p: 2,
                        }
                      : {}
                  }
                >
                  {isEditing ? (
                    <Paper
                      variant="outlined"
                      className="!border-outlineVariant !rounded-none"
                      sx={{
                        textAlign: 'center',
                        borderStyle: 'dashed',
                        backgroundColor: 'action.hover',
                        minHeight: { xs: 150, md: 200 },
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        alignItems: 'center',
                        position: 'relative',
                        background: '#0000000D',
                      }}
                    >
                      <Typography
                        gutterBottom
                        variant="subtitle2"
                        fontWeight="bold"
                        position="absolute"
                        top={-24}
                        left={0}
                        zIndex={1000}
                        className="text-white bg-white"
                      >
                        {t('announcement.keyImageLabel', 'キー画像')}
                      </Typography>
                      <Typography
                        gutterBottom
                        variant="subtitle2"
                        fontWeight="bold"
                        position="absolute"
                        top={-24}
                        right={0}
                        zIndex={1000}
                        className="text-white bg-white"
                      >
                        任意項目
                      </Typography>
                      {formValues.content?.[activeLangTab]?.keyImage ? (
                        <img
                          src={
                            formValues.content[
                              activeLangTab
                            ]?.keyImage?.startsWith('blob:')
                              ? formValues.content[activeLangTab]?.keyImage
                              : getImageUrl(
                                  formValues.content[activeLangTab]?.keyImage,
                                )
                          }
                          alt="Key Preview"
                          style={{
                            maxHeight: 100,
                            maxWidth: '100%',
                            objectFit: 'contain',
                            marginBottom: '8px',
                          }}
                        />
                      ) : (
                        <div className="bg-white flex flex-col items-center justify-center h-[90px] w-[90px] rounded-full p-3">
                          <div>
                            <AddPhotoAlternate
                              sx={{ fontSize: 20, color: 'text.primary' }}
                            />
                          </div>
                          <MuiButton
                            component="label"
                            size="small"
                            variant="text"
                            sx={{ textTransform: 'none' }}
                          >
                            {t('common.button.upload', 'Upload')}
                            <input
                              type="file"
                              hidden
                              accept="image/*"
                              onChange={(e) =>
                                handleImageUpload(
                                  e,
                                  `content.${activeLangTab}.keyImage`,
                                )
                              }
                            />
                          </MuiButton>
                        </div>
                      )}
                      {formValues.content?.[activeLangTab]?.keyImage && (
                        <IconButton
                          size="small"
                          onClick={() =>
                            removeImage(`content.${activeLangTab}.keyImage`)
                          }
                          sx={{ position: 'absolute', top: 8, right: 8 }}
                        >
                          <DeleteIcon fontSize="small" color="error" />
                        </IconButton>
                      )}
                    </Paper>
                  ) : announcement.content?.[activeLangTab]?.keyImage ? (
                    <>
                      <Typography
                        gutterBottom
                        variant="subtitle2"
                        fontWeight="bold"
                      >
                        {t('announcement.keyImageLabel', 'キー画像')}
                      </Typography>
                      <Box
                        component="img"
                        src={getImageUrl(
                          announcement.content[activeLangTab]?.keyImage,
                        )}
                        sx={{
                          width: '100%',
                          maxHeight: 200,
                          objectFit: 'cover',
                          borderRadius: 1,
                          border: '1px solid',
                          borderColor: 'divider',
                        }}
                      />
                    </>
                  ) : (
                    <>
                      <Typography
                        gutterBottom
                        variant="subtitle2"
                        fontWeight="bold"
                      >
                        {t('announcement.keyImageLabel', 'キー画像')}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        {t('common.noImage', '画像がありません')}
                      </Typography>
                    </>
                  )}
                </Box>
                <Box
                  className="!border-outlineVariant !rounded-md"
                  sx={
                    isEditing
                      ? {
                          border: 1,
                          p: 2,
                        }
                      : {}
                  }
                >
                  {isEditing ? (
                    <Grid container spacing={1.5}>
                      {[...Array(2)].map((_, index) => (
                        <Grid item xs={6} key={index}>
                          <Paper
                            variant="outlined"
                            className="!border-outlineVariant !rounded-none"
                            sx={{
                              textAlign: 'center',
                              borderStyle: 'dashed',
                              backgroundColor: 'action.hover',
                              minHeight: { xs: 120, md: 150 },
                              display: 'flex',
                              flexDirection: 'column',
                              justifyContent: 'center',
                              alignItems: 'center',
                              position: 'relative',
                              background: '#0000000D',
                            }}
                          >
                            {index === 0 && (
                              <Typography
                                gutterBottom
                                variant="subtitle2"
                                fontWeight="bold"
                                position="absolute"
                                top={-24}
                                left={0}
                                zIndex={1000}
                                className="text-white bg-white"
                              >
                                {t(
                                  'announcement.articleImagesLabel',
                                  '記事画像',
                                )}
                              </Typography>
                            )}
                            {index === 1 && (
                              <Typography
                                gutterBottom
                                variant="subtitle2"
                                fontWeight="bold"
                                position="absolute"
                                top={-24}
                                right={0}
                                zIndex={1000}
                                className="text-white bg-white"
                              >
                                任意項目
                              </Typography>
                            )}
                            {formValues.content?.[activeLangTab]
                              ?.articleImages?.[index] ? (
                              <img
                                src={
                                  formValues.content[
                                    activeLangTab
                                  ]?.articleImages?.[index]?.startsWith('blob:')
                                    ? formValues.content[activeLangTab]
                                        ?.articleImages?.[index]
                                    : getImageUrl(
                                        formValues.content[activeLangTab]
                                          ?.articleImages?.[index],
                                      )
                                }
                                alt={`Article Preview ${index + 1}`}
                                style={{
                                  maxHeight: 60,
                                  maxWidth: '100%',
                                  objectFit: 'contain',
                                  marginBottom: '8px',
                                }}
                              />
                            ) : (
                              <div className="bg-white flex flex-col items-center justify-center h-[90px] w-[90px] rounded-full p-3">
                                <div>
                                  <AddPhotoAlternate
                                    sx={{ fontSize: 20, color: 'text.primary' }}
                                  />
                                </div>
                                <MuiButton
                                  component="label"
                                  size="small"
                                  variant="text"
                                  sx={{ textTransform: 'none' }}
                                >
                                  {t('common.button.upload', 'Upload')}
                                  <input
                                    type="file"
                                    hidden
                                    accept="image/*"
                                    onChange={(e) =>
                                      handleArticleImageUpload(e, index)
                                    }
                                  />
                                </MuiButton>
                              </div>
                            )}
                            {formValues.content?.[activeLangTab]
                              ?.articleImages?.[index] && (
                              <IconButton
                                size="small"
                                onClick={() => removeArticleImage(index)}
                                sx={{
                                  position: 'absolute',
                                  top: 4,
                                  right: 4,
                                }}
                              >
                                <DeleteIcon fontSize="small" color="error" />
                              </IconButton>
                            )}
                          </Paper>
                        </Grid>
                      ))}
                    </Grid>
                  ) : announcement.content?.[activeLangTab]?.articleImages &&
                    (announcement.content[activeLangTab]?.articleImages || [])
                      .length > 0 ? (
                    <>
                      <Typography
                        gutterBottom
                        variant="subtitle2"
                        fontWeight="bold"
                      >
                        {t('announcement.articleImagesLabel', '記事画像')}
                      </Typography>
                      <Grid container spacing={1}>
                        {announcement.content[
                          activeLangTab
                        ]?.articleImages?.map((img, index) => (
                          <Grid item xs={6} key={`img-${index}`}>
                            <Box
                              component="img"
                              src={getImageUrl(img)}
                              sx={{
                                width: '100%',
                                maxHeight: 150,
                                objectFit: 'cover',
                                borderRadius: 1,
                                border: 1,
                                borderColor: 'divider',
                              }}
                            />
                          </Grid>
                        ))}
                      </Grid>
                    </>
                  ) : (
                    <>
                      <Typography
                        gutterBottom
                        variant="subtitle2"
                        fontWeight="bold"
                      >
                        {t('announcement.articleImagesLabel', '記事画像')}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        {t('common.noImage', '画像がありません')}
                      </Typography>
                    </>
                  )}
                </Box>
              </Grid>
            </Grid>
          </Box>
        </Box>
      </Paper>
    </>
  );
};
