import { Box, Grid } from '@mui/material';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import {
  ContentStatus,
  Notification,
  NotificationArticleContent,
  NotificationType,
} from '@/bundles/model';
import { CommonDialog } from '@/components/dialog/CommonDialog';
import { Breadcrumb } from '@/components/elements';
import { Head } from '@/components/seo/head';
import { DateFormat } from '@/constants/date';
import { dateFormat } from '@/constants/utils';
import { useAnnouncementArticleStore } from '@/features/contentManagement/ui/stores/announcement/announcement-article-store';
import { useUploadFile } from '@/features/uploadFile/ui/hooks/use-upload-file';
import { routes } from '@/routes/routes';

import { ContentManagementAnnouncementForm } from './AnnouncementForm';
import { AnnouncementRightPanel } from './AnnouncementRightPanel';
import { useCreateAnnouncementArticle } from './hook/use-create-announcement';

const setNestedValue = (obj: any, path: string, value: any) => {
  const keys = path.split('.');
  let current = obj;
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!current[key] || typeof current[key] !== 'object') {
      if (!isNaN(parseInt(keys[i + 1], 10))) {
        current[key] = [];
      } else {
        current[key] = {};
      }
    }
    current = current[key];
  }
  current[keys[keys.length - 1]] = value;
  return obj;
};

export const ContentManagementAnnouncementCreate = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const createNotificationMutation = useCreateAnnouncementArticle();
  const { dialogState, setDialogState } = useAnnouncementArticleStore();

  // Clear any existing dialog state on component mount
  useEffect(() => {
    setDialogState(null);
  }, [setDialogState]);

  const { mutateAsync: uploadFile } = useUploadFile(() => {
    setDialogState({
      open: true,
      type: 'error',
      message: 'ファイルのアップロードに失敗しました。',
    });
  });

  const [showRightPanel, setShowRightPanel] = useState(false);
  const [formValues, setFormValues] = useState<Notification>({
    type: NotificationType.CampaignPromotion,
    countryCodes: [],
    status: 'draft',
    content: {
      en: {
        active: true,
        title: '',
        message: '',
        keyImage: '',
        articleImages: [],
      },
    },
  });

  const handleSetFormValue = useCallback((fieldName: string, value: any) => {
    setFormValues((prev) => {
      const newValues = JSON.parse(JSON.stringify(prev));
      setNestedValue(newValues, fieldName, value);
      return newValues;
    });
  }, []);

  const handleHeaderAction = useCallback(() => {
    setShowRightPanel(true);
  }, []);

  const handleSave = useCallback(
    async (publicationDate: Date, status: ContentStatus) => {
      try {
        const updatedContent: { [key: string]: NotificationArticleContent } =
          {};

        // Process each language content
        for (const [lang, contentData] of Object.entries(
          formValues.content || {},
        )) {
          const processedContent = { ...contentData };

          // Upload keyImage if it's a blob URL (newly uploaded)
          if (
            contentData.keyImage &&
            contentData.keyImage.startsWith('blob:')
          ) {
            const response = await fetch(contentData.keyImage);
            const blob = await response.blob();
            const file = new File([blob], 'keyImage.jpg', { type: blob.type });
            const uploadedKeyImage = await uploadFile(file);
            processedContent.keyImage = uploadedKeyImage.key;
          }

          // Upload articleImages if they are blob URLs (newly uploaded)
          if (
            contentData.articleImages &&
            contentData.articleImages.length > 0
          ) {
            const uploadedArticleImages = await Promise.all(
              contentData.articleImages.map(async (imageUrl) => {
                if (imageUrl && imageUrl.startsWith('blob:')) {
                  const response = await fetch(imageUrl);
                  const blob = await response.blob();
                  const file = new File([blob], 'articleImage.jpg', {
                    type: blob.type,
                  });
                  const uploadedImage = await uploadFile(file);
                  return uploadedImage.key;
                }
                return imageUrl; // Keep existing URL if not a blob
              }),
            );
            processedContent.articleImages =
              uploadedArticleImages.filter(Boolean);
          }

          updatedContent[lang] = processedContent;
        }

        const date = publicationDate?.toISOString?.() || undefined;
        const publishedAt: string | undefined = date
          ? dateFormat(date, DateFormat.fullDateWithHyphen)
          : undefined;

        const dataToSave = {
          ...formValues,
          publishedAt,
          status,
          content: updatedContent,
        };

        createNotificationMutation.mutate(dataToSave, {
          onSuccess: () => {
            navigate(
              `${routes.contentManagement.index}${routes.contentManagement.announcement.index}`,
            );
          },
          onError: () => {
            setDialogState({
              open: true,
              type: 'error',
              message: t(
                'announcement.createFailed',
                'お知らせの作成に失敗しました。',
              ),
            });
          },
        });
      } catch (error) {
        setDialogState({
          open: true,
          type: 'error',
          message: 'ファイルのアップロードに失敗しました。',
        });
      }
    },
    [
      formValues,
      createNotificationMutation,
      navigate,
      t,
      uploadFile,
      setDialogState,
    ],
  );

  const handleDialogClose = () => {
    setDialogState(null);
  };

  const isFormValid = useMemo(() => {
    if (!formValues.content) return false;
    return Object.values(formValues.content).some(
      (langData) => langData?.title && langData.title.trim() !== '',
    );
  }, [formValues]);

  const breadcrumbItems = useMemo(
    () => [
      { name: t('menu.home'), href: routes.home.index },
      {
        name: t('menu.contentManagement'),
        href: routes.contentManagement.index,
      },
      {
        name: t('announcement.title', 'お知らせ'),
        href: `${routes.contentManagement.index}${routes.contentManagement.announcement.index}`,
      },
      {
        name: t('announcement.create.breadcrumb', '新規記事作成'),
      },
    ],
    [t],
  );

  return (
    <>
      <Head
        title={t(
          'announcement.create.title',
          'お知らせ記事作成 - Create an announcement article',
        )}
      />
      <Breadcrumb
        title={t('announcement.create.breadcrumb', 'Create Announcement')}
        items={breadcrumbItems}
      />
      <Box p={{ xs: 1, md: 3 }}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={showRightPanel ? 8.5 : 12}>
            <ContentManagementAnnouncementForm
              isEditing={true}
              isNew={true}
              handleHeaderAction={handleHeaderAction}
              formValues={formValues}
              setFormValue={handleSetFormValue}
              showRightPanel={showRightPanel}
              announcement={formValues as Notification}
            />
          </Grid>
          {showRightPanel && (
            <Grid item xs={12} md={3.5}>
              <AnnouncementRightPanel
                type="create"
                initialPublicationDate={formValues.publishedAt}
                initialStatus={formValues.status as ContentStatus}
                formIsValid={isFormValid}
                onSave={handleSave}
                onDelete={undefined}
                isSubmitting={createNotificationMutation.isPending}
              />
            </Grid>
          )}
        </Grid>
      </Box>

      {dialogState && (
        <CommonDialog
          open={dialogState.open}
          type={dialogState.type}
          message={dialogState.message}
          onOk={handleDialogClose}
          onCancel={handleDialogClose}
          okText="OK"
          cancelText="戻る"
        />
      )}
    </>
  );
};
