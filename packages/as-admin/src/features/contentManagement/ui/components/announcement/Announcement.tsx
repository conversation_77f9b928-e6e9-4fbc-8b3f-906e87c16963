import { Article } from '@mui/icons-material';
import { Box, Button, Typography } from '@mui/material';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { Notification } from '@/bundles/model';
import { Breadcrumb } from '@/components/elements';
import { Head } from '@/components/seo/head';
import { routes } from '@/routes/routes';

import { useAnnouncementArticleStore } from '../../stores/announcement/announcement-article-store';
import { useGetAnnouncementArticles } from '../announcement/hook/use-create-announcement';

import { ArticleListItem } from './ArticleListItem';
import { ArticlePagination } from './ArticlePagination';

export const ContentManagementAnnouncement = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const {
    articles,
    isLoading,
    error,
    currentPage,
    totalPages,
    setCurrentPage,
    fetchArticles,
  } = useAnnouncementArticleStore();

  const {
    mutateAsync: useGetAnnouncementArticlesMutate,
    isPending: isFetchingList,
  } = useGetAnnouncementArticles();

  useEffect(() => {
    fetchArticles(useGetAnnouncementArticlesMutate);
  }, [currentPage, fetchArticles, useGetAnnouncementArticlesMutate]);

  const handleAddNewArticle = () => {
    navigate(
      `${routes.contentManagement.index}${routes.contentManagement.announcement.index}/create`,
    );
  };

  const handleViewArticleDetails = (announcementId: string) => {
    navigate(
      `${routes.contentManagement.index}${routes.contentManagement.announcement.index}/${announcementId}`,
    );
  };

  if (isLoading || isFetchingList) {
    return (
      <div className="flex justify-center items-center h-screen bg-gray-100">
        <p className="text-2xl text-gray-700">Loading articles...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-screen bg-gray-100">
        <p className="text-2xl text-red-600">Error: {error}</p>
      </div>
    );
  }

  const breadcrumbItems = [
    { name: t('menu.home'), href: routes.home.index },
    { name: t('menu.contentManagement'), href: routes.contentManagement.index },
    { name: t('announcement.title', 'お知らせ') },
  ];

  return (
    <>
      <Head title="お知らせ" />
      <Breadcrumb title="お知らせ" items={breadcrumbItems} />
      <Box className="mx-auto p-6 bg-white shadow-md">
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            borderBottom: 2,
            mb: 2,
            pt: { xs: 1, md: 1 },
            pb: 1,
          }}
        >
          <Typography variant="h2" component="h1">
            記事一覧
          </Typography>
          <Box
            sx={{
              ml: 'auto',
              display: 'flex',
              alignItems: 'center',
              gap: 1,
            }}
          >
            <Button
              variant="contained"
              color="inherit"
              startIcon={<Article className="!text-primary" />}
              className="!bg-surfaceContainerHigh !w-[120px] !h-[45px]"
              sx={{ height: '40px' }}
              onClick={handleAddNewArticle}
            >
              新規追加
            </Button>
          </Box>
        </Box>
        <div>
          {articles && articles.length > 0 ? (
            articles.map((article: Notification) => (
              <ArticleListItem
                key={article.id}
                article={article}
                onViewDetails={handleViewArticleDetails}
              />
            ))
          ) : (
            <p className="text-center text-gray-600 py-8">No articles found.</p>
          )}
        </div>

        {articles && articles.length > 0 && (
          <ArticlePagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        )}
      </Box>
    </>
  );
};
