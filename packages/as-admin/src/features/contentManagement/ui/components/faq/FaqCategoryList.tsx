import {
  DragDropContext,
  Droppable,
  Draggable,
  DropResult,
} from '@hello-pangea/dnd';
import AddIcon from '@mui/icons-material/Add';
import { Box, Paper, Typography } from '@mui/material';
import clsx from 'clsx';
import React, { useMemo, useCallback } from 'react';

import { FaqCategory } from '@/bundles/model';
import { Button } from '@/components/forms';

import { CategoryTreeNode } from './FaqCategoryThreeNode';

interface CategoryListProps {
  categories: FaqCategory[];
  expandedCategories: string[];
  loadingCategories: Record<string, boolean>;
  onToggleExpand: (categoryId: string) => void;
  onSelect?: (category: FaqCategory) => void;
  onActiveChange: (categoryId: string, active: boolean) => void;
  isEditing?: boolean;
  selectedCategoryId?: string;
  onAddNew?: () => void;
  onCategoriesReorder?: (newCategories: FaqCategory[]) => void;
}

interface FlatCategory {
  category: FaqCategory;
  level: number;
  parentId?: string;
  path: string[];
  isVisible: boolean;
}

const flattenCategories = (
  categories: FaqCategory[],
  expandedCategories: Record<string, boolean>,
  level = 0,
  parentId?: string,
  path: string[] = [],
): FlatCategory[] => {
  const result: FlatCategory[] = [];

  categories.forEach((category) => {
    const currentPath = [...path, category.id || ''];
    const isVisible =
      level === 0 || (parentId ? expandedCategories[parentId] === true : false);

    result.push({
      category,
      level,
      parentId,
      path: currentPath,
      isVisible,
    });

    if (
      category.subCategories &&
      category.subCategories.length > 0 &&
      expandedCategories[category.id || ''] === true
    ) {
      result.push(
        ...flattenCategories(
          category.subCategories,
          expandedCategories,
          level + 1,
          category.id,
          currentPath,
        ),
      );
    }
  });

  return result;
};

const isValidDrop = (
  sourceItem: FlatCategory,
  destinationItem: FlatCategory,
): boolean => {
  if (sourceItem.parentId === destinationItem.parentId) {
    return true;
  }

  if (sourceItem.level === destinationItem.level) {
    return true;
  }

  return false;
};

const CategoryItem = React.memo<{
  flatCategory: FlatCategory;
  index: number;
  isSelected: boolean;
  isEditing: boolean;
  onCategoryClick: (category: FaqCategory) => void;
  onActiveChange: (categoryId: string, active: boolean) => void;
  onExpandToggle: (categoryId: string) => void;
  expandedCategories: Record<string, boolean>;
  loadingCategories: Record<string, boolean>;
}>(
  ({
    flatCategory,
    index,
    isSelected,
    isEditing,
    onCategoryClick,
    onActiveChange,
    onExpandToggle,
    expandedCategories,
    loadingCategories,
  }) => {
    const { category, level } = flatCategory;

    if (!flatCategory.isVisible) {
      return null;
    }

    return (
      <Draggable
        draggableId={category.id || ''}
        index={index}
        isDragDisabled={!isEditing}
      >
        {(provided) => (
          <div
            ref={provided.innerRef}
            {...provided.draggableProps}
            {...provided.dragHandleProps}
            style={{ ...provided.draggableProps.style }}
          >
            <CategoryTreeNode
              key={category.id}
              category={
                {
                  ...category,
                  children: category.subCategories,
                } as any
              }
              level={level}
              isExpanded={expandedCategories[category.id || ''] || false}
              isLoading={loadingCategories[category.id || ''] || false}
              onToggle={onExpandToggle}
              onSelect={onCategoryClick as any}
              isEditing={isEditing}
              isSelected={isSelected}
              onActiveChange={onActiveChange}
            />
          </div>
        )}
      </Draggable>
    );
  },
);
CategoryItem.displayName = 'CategoryItem';

export const CategoryList: React.FC<CategoryListProps> = ({
  categories,
  expandedCategories,
  loadingCategories,
  onToggleExpand,
  onSelect,
  onActiveChange,
  isEditing = false,
  selectedCategoryId,
  onAddNew,
  onCategoriesReorder,
}) => {
  const flatCategories = useMemo(
    () =>
      flattenCategories(
        categories,
        expandedCategories.reduce((acc, id) => ({ ...acc, [id]: true }), {}),
      ),
    [categories, expandedCategories],
  );

  const visibleCategories = useMemo(
    () => flatCategories.filter((item) => item.isVisible),
    [flatCategories],
  );

  const handleCategoryClick = useCallback(
    (category: FaqCategory) => {
      if (onSelect) {
        onSelect(category);
      }
    },
    [onSelect],
  );

  const handleDragEnd = useCallback(
    (result: DropResult) => {
      if (!result.destination || !onCategoriesReorder) {
        return;
      }

      const sourceIndex = result.source.index;
      const destinationIndex = result.destination.index;

      if (sourceIndex === destinationIndex) {
        return;
      }

      const sourceItem = visibleCategories[sourceIndex];
      const destinationItem = visibleCategories[destinationIndex];

      if (!sourceItem || !destinationItem) {
        return;
      }

      if (!isValidDrop(sourceItem, destinationItem)) {
        return;
      }

      const clonedCategories = JSON.parse(JSON.stringify(categories));

      const reorderCategoriesRecursive = (
        cats: FaqCategory[],
        sourceId: string,
        destinationId: string,
        sourceParentId?: string,
        destinationParentId?: string,
      ): boolean => {
        if (sourceParentId === destinationParentId) {
          const sourceIdx = cats.findIndex((cat) => cat.id === sourceId);
          const destIdx = cats.findIndex((cat) => cat.id === destinationId);
          if (sourceIdx > -1 && destIdx > -1) {
            const [movedItem] = cats.splice(sourceIdx, 1);
            cats.splice(destIdx, 0, movedItem);
            return true;
          }
        }

        if (sourceParentId !== destinationParentId) {
          let sourceItemData: FaqCategory | undefined;
          let sourceArray: FaqCategory[] | undefined;
          let sIndex = -1;

          const findAndRemoveSource = (
            searchCats: FaqCategory[],
            parentId?: string,
          ): boolean => {
            if (parentId === sourceParentId) {
              sIndex = searchCats.findIndex((cat) => cat.id === sourceId);
              if (sIndex > -1) {
                sourceArray = searchCats;
                [sourceItemData] = searchCats.splice(sIndex, 1);
                return true;
              }
            }
            for (const cat of searchCats) {
              if (
                cat.subCategories &&
                findAndRemoveSource(cat.subCategories, cat.id)
              ) {
                return true;
              }
            }
            return false;
          };

          const findAndInsertDestination = (
            searchCats: FaqCategory[],
            parentId?: string,
          ): boolean => {
            if (parentId === destinationParentId) {
              const destIdx = searchCats.findIndex(
                (cat) => cat.id === destinationId,
              );
              if (destIdx > -1 && sourceItemData) {
                searchCats.splice(destIdx, 0, sourceItemData);
                return true;
              }
            }
            for (const cat of searchCats) {
              if (
                cat.subCategories &&
                findAndInsertDestination(cat.subCategories, cat.id)
              ) {
                return true;
              }
            }
            return false;
          };

          if (findAndRemoveSource(clonedCategories)) {
            if (findAndInsertDestination(clonedCategories)) {
              return true;
            } else if (sourceArray && sourceItemData && sIndex > -1) {
              sourceArray.splice(sIndex, 0, sourceItemData);
            }
          }
        }

        for (const cat of cats) {
          if (
            cat.subCategories &&
            reorderCategoriesRecursive(
              cat.subCategories,
              sourceId,
              destinationId,
              cat.id,
              cat.id,
            )
          ) {
            return true;
          }
        }

        return false;
      };

      const reordered = reorderCategoriesRecursive(
        clonedCategories,
        sourceItem.category.id || '',
        destinationItem.category.id || '',
        sourceItem.parentId,
        destinationItem.parentId,
      );

      if (reordered) {
        onCategoriesReorder(clonedCategories);
      }
    },
    [visibleCategories, categories, onCategoriesReorder],
  );

  return (
    <Paper className="bg-white border border-gray-200 rounded-lg pb-4">
      <Box className="flex items-center justify-between py-4 px-6">
        <Typography variant="h3">カテゴリ一覧</Typography>
        {isEditing && onAddNew && (
          <Button
            className="!rounded-lg !min-w-20 !shadow-sm"
            variant="outlined"
            color="primary"
            size="small"
            startIcon={
              <AddIcon
                className="!fill-primary"
                sx={{ fontSize: '18px', marginRight: '4px' }}
              />
            }
            onClick={onAddNew}
          >
            <Typography
              variant="subtitle2"
              className="!font-bold !text-primary"
            >
              新規追加
            </Typography>
          </Button>
        )}
      </Box>

      <Box className="px-6">
        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="categories">
            {(provided, snapshot) => (
              <div
                {...provided.droppableProps}
                ref={provided.innerRef}
                className={clsx(
                  'transition-colors',
                  snapshot.isDraggingOver &&
                    isEditing &&
                    'bg-[#f0f9ff] bg-opacity-30',
                )}
              >
                {visibleCategories.map((flatCategory, index) => (
                  <CategoryItem
                    key={flatCategory.category.id}
                    flatCategory={flatCategory}
                    index={index}
                    isSelected={selectedCategoryId === flatCategory.category.id}
                    isEditing={isEditing}
                    onCategoryClick={handleCategoryClick}
                    onActiveChange={onActiveChange}
                    onExpandToggle={onToggleExpand}
                    expandedCategories={expandedCategories.reduce(
                      (acc, id) => ({ ...acc, [id]: true }),
                      {},
                    )}
                    loadingCategories={loadingCategories}
                  />
                ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      </Box>
    </Paper>
  );
};
