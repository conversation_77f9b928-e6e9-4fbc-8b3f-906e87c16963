import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowDropDown } from '@mui/icons-material';
import CloseIcon from '@mui/icons-material/Close';
import {
  Box,
  Checkbox,
  Divider,
  FormControlLabel,
  Grid,
  IconButton,
  InputAdornment,
  Paper,
  Tab,
  Tabs,
  TextField,
  Typography,
} from '@mui/material';
import clsx from 'clsx';
import dayjs from 'dayjs';
import { useCallback, useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod';

import { Content } from '@/bundles/model';
import {
  CommonDialog,
  CommonDialogType,
  DialogState,
} from '@/components/dialog/CommonDialog';
import { AnimatedCopyButton } from '@/components/feedback';
import { Form } from '@/components/forms';
import { DateFormat } from '@/constants/date';
import { dateFormat, StatusApplicationDate } from '@/constants/utils';
import { Editor } from '@/features/news/ui/components/editor/Editor';

import { CategorySideMenu } from './CategorySideMenu';
import { FaqArticleRightPanel } from './FaqArticleRightPanel';
import { useFaqCategoriesBranches } from './hook/use-faq-categories-branches';

const availableLanguages = [
  { code: 'en', name: 'EN' },
  { code: 'es', name: 'ES' },
  { code: 'fr', name: 'FR' },
  { code: 'it', name: 'IT' },
  { code: 'ja', name: 'JA' },
  { code: 'ko', name: 'KO' },
  { code: 'zhCN', name: 'zhCN' },
];

const faqArticleSchema = z.object({
  id: z.string().optional(),
  title: z
    .record(
      z
        .string()
        .max(300, { message: 'タイトルは300文字以内で入力してください。' })
        .optional()
        .or(z.literal('')),
    )
    .refine((val) => val.en && val.en.trim().length > 0, {
      message: '英語のタイトルは必須です。',
      path: ['en'],
    }),
  parentContentId: z.string().min(1, 'カテゴリは必須です。'),
  contentCategoryName: z.string().optional(),
  contentCategoryCode: z.string().optional(),
  status: z.enum(['draft', 'published', 'waitingToPublish']).optional(),
  publishedAt: z.string().optional(),
  content: z
    .record(
      z.object({
        active: z.boolean().optional(),
        body: z.string().optional(),
        attachments: z.array(z.string()).optional(),
      }),
    )
    .refine(
      (val) =>
        Object.values(val).some(
          (langContent) =>
            langContent?.body &&
            langContent.body.replace(/<p>(\s*<br\s*\/?>\s*)*<\/p>/gi, '').trim()
              .length > 0,
        ),
      {
        message: '少なくとも1つの言語で本文を入力してください。',
      },
    ),
});

export type FaqArticleFormValues = z.infer<typeof faqArticleSchema>;

type FaqArticleFormProps = {
  type: 'create' | 'edit';
  initialData?: Content;
  onSubmit: (formValues: FaqArticleFormValues) => void;
  onDelete?: (articleId: string) => void;
};

export const FaqArticleForm: React.FC<FaqArticleFormProps> = ({
  type,
  initialData,
  onSubmit,
  onDelete,
}) => {
  const [isCategoryMenuOpen, setIsCategoryMenuOpen] = useState(false);
  const [activeTabLang, setActiveTabLang] = useState<string>('en');
  const recordId = initialData?.id || 'N/A';
  const inAppUrl = initialData?.id ? `/app/articles/${initialData.id}` : 'N/A';

  const [applicationDate, setApplicationDate] = useState(() =>
    initialData?.publishedAt ? dayjs(initialData.publishedAt) : dayjs(),
  );

  const [dialogState, setDialogState] = useState<DialogState | undefined>(
    undefined,
  );
  const [action, setAction] = useState<StatusApplicationDate | undefined>(
    undefined,
  );

  const [bodyCharCounts, setBodyCharCounts] = useState<Record<string, number>>(
    {},
  );

  // Fetch FAQ categories for mapping category ID to name (always get fresh data)
  const { data: faqBranches } = useFaqCategoriesBranches(undefined, {
    refetchOnWindowFocus: true, // Refetch when window gains focus
    staleTime: 0, // Always consider data stale to get fresh data
  });

  // Helper function to find category name by ID (returns English name for edit mode)
  const findCategoryNameById = useCallback(
    (categoryId: string): string => {
      if (!faqBranches || !categoryId) return '';

      // Use only the first branch (branches[0]) - same as CategorySideMenu
      const firstBranch = faqBranches[0];

      const allFaqCategories: any[] = [];
      if (
        firstBranch?.faqCategories &&
        Array.isArray(firstBranch.faqCategories)
      ) {
        allFaqCategories.push(...firstBranch.faqCategories);
      }

      // Filter faqCategories by status='published' and sort by latest createdAt
      const publishedCategories = allFaqCategories
        .filter((category: any) => {
          // Filter by published status and ensure has English title
          const isPublished =
            category.status === 'published' || category.active === true;
          const hasEngTitle =
            category.displayName?.['en'] &&
            category.displayName['en'].trim().length > 0;
          return isPublished && hasEngTitle;
        })
        .sort((a: any, b: any) => {
          // Sort by createdAt descending (latest first)
          const dateA = new Date(a.createdAt || a.updatedAt || 0).getTime();
          const dateB = new Date(b.createdAt || b.updatedAt || 0).getTime();
          return dateB - dateA;
        });

      const findInCategories = (categories: any[]): string => {
        for (const category of categories) {
          if (category.id === categoryId) {
            // Return English name for edit mode, only if English title exists
            const hasEngTitle =
              category.displayName?.['en'] &&
              category.displayName['en'].trim().length > 0;
            if (!hasEngTitle) return '';

            return category.displayName?.['en'] || '';
          }

          // Search in subCategories
          if (category.subCategories && Array.isArray(category.subCategories)) {
            const found = findInCategories(category.subCategories);
            if (found) return found;
          }

          // Search in children
          if (category.children && Array.isArray(category.children)) {
            const found = findInCategories(category.children);
            if (found) return found;
          }
        }
        return '';
      };

      return findInCategories(publishedCategories);
    },
    [faqBranches],
  );

  const methods = useForm<FaqArticleFormValues>({
    resolver: zodResolver(faqArticleSchema),
    mode: 'onChange',
    defaultValues: {
      title: { en: '' },
      content: {},
      parentContentId: '',
      contentCategoryName: '',
      contentCategoryCode: 'faq',
      status: initialData?.status || 'draft',
      publishedAt: initialData?.publishedAt,
    },
  });

  const { formState, watch, setValue, getValues, control } = methods;
  const isFormInvalid = !formState.isValid;
  const contentValues = watch('content');

  useEffect(() => {
    if (!initialData) return;

    const initialCounts: Record<string, number> = {};
    for (const langCode of availableLanguages.map((l) => l.code)) {
      const html = initialData.content?.[langCode]?.body;
      if (html) {
        const div = document.createElement('div');
        div.innerHTML = html;
        const text = div.textContent || div.innerText || '';
        initialCounts[langCode] = text.trim().length;
      } else {
        initialCounts[langCode] = 0;
      }
    }
    setBodyCharCounts(initialCounts);

    let categoryName = '';
    if (faqBranches && initialData.parentContentId) {
      categoryName = findCategoryNameById(initialData.parentContentId);
    }

    const resetData = {
      id: initialData.id,
      title: availableLanguages.reduce(
        (acc, lang) => ({
          ...acc,
          [lang.code]: initialData.title?.[lang.code] || '',
        }),
        {},
      ),
      parentContentId: initialData.parentContentId || '',
      contentCategoryName: categoryName,
      contentCategoryCode: 'faq',
      content: availableLanguages.reduce(
        (acc, lang) => ({
          ...acc,
          [lang.code]: {
            active: initialData.content?.[lang.code]?.active ?? false,
            body: initialData.content?.[lang.code]?.body || '<p><br></p>',
            attachments: initialData.content?.[lang.code]?.attachments || [],
          },
        }),
        {},
      ),
      status: initialData.status || 'draft',
      publishedAt: initialData.publishedAt,
    };
    methods.reset(resetData);

    if (initialData.publishedAt) {
      setApplicationDate(dayjs(initialData.publishedAt));
    }
  }, [initialData, faqBranches, findCategoryNameById, methods]);

  const handleFormSubmit = (
    data: FaqArticleFormValues,
    action: StatusApplicationDate,
  ) => {
    const submissionData: any = { ...data };

    switch (action) {
      case StatusApplicationDate.Publish:
        submissionData.status = 'published';
        submissionData.publishedAt = applicationDate.toISOString();
        break;
      case StatusApplicationDate.Unpublish:
        submissionData.status = 'draft';
        submissionData.publishedAt = null;
        break;
      case StatusApplicationDate.Delete:
        submissionData.status = 'delete';
        submissionData.publishedAt = null;
        break;
      default:
        submissionData.status = 'draft';
        submissionData.publishedAt = applicationDate.toISOString();
    }

    onSubmit(submissionData);
  };

  const openConfirmationDialog = (action: StatusApplicationDate) => {
    let message = '';
    let type: CommonDialogType = 'alert';

    switch (action) {
      case StatusApplicationDate.Draft:
        message = '下書きとして保存します。よろしいですか？';
        break;
      case StatusApplicationDate.Publish:
        message = '公開します。よろしいですか？';
        break;
      case StatusApplicationDate.Unpublish:
        message = '公開予約をキャンセルします。よろしいですか？';
        break;
      case StatusApplicationDate.Delete:
        message = 'この記事を削除します。よろしいですか？';
        type = 'confirm-delete';
        break;
    }

    setAction(action);
    setDialogState({
      open: true,
      type,
      message,
    });
  };

  const handleDialogConfirm = () => {
    const newAction = (dialogState?.action as StatusApplicationDate) || action;
    if (!newAction) {
      return;
    }
    if (newAction === StatusApplicationDate.Delete) {
      if (onDelete && initialData?.id) {
        onDelete(initialData.id);
      }
    } else {
      handleFormSubmit(getValues(), newAction);
    }
    setDialogState(undefined);
    setAction(undefined);
  };

  const handleDialogCancel = () => {
    setDialogState(undefined);
    setAction(undefined);
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: string) => {
    setActiveTabLang(newValue);
  };

  const handleCategorySelect = (
    categoryCodeValue: string,
    categoryName: string,
  ) => {
    setValue('parentContentId', categoryCodeValue, {
      shouldValidate: true,
      shouldDirty: true,
    });
    setValue('contentCategoryName', categoryName);
    setIsCategoryMenuOpen(false);
  };

  const handleClearCategory = (event: React.MouseEvent) => {
    event.stopPropagation();
    setValue('parentContentId', '', { shouldValidate: true });
    setValue('contentCategoryName', '');
  };

  const contentCategoryCode = watch('parentContentId');
  const selectedCategoryDisplayName = watch('contentCategoryName');

  return (
    <>
      <Paper elevation={0} className="bg-transparent">
        <Grid container spacing={2}>
          <Grid item xs={12} md={8.5}>
            <Paper elevation={0} className="px-6 py-3 rounded-lg">
              <Box
                mb={3}
                pb={2}
                sx={{ borderBottom: 1, borderColor: 'divider' }}
              >
                <Controller
                  name="title.en"
                  control={control}
                  render={({ field, fieldState }) => (
                    <TextField
                      {...field}
                      label="記事タイトル"
                      multiline
                      fullWidth
                      placeholder="Enter English Title"
                      onChange={(e) => field.onChange(e.target.value)}
                      error={!!fieldState.error}
                      helperText={fieldState.error?.message}
                      variant="outlined"
                      InputProps={{
                        endAdornment: field.value && (
                          <InputAdornment position="start">
                            <CloseIcon
                              onClick={() =>
                                setValue('title.en', '', {
                                  shouldValidate: true,
                                  shouldDirty: true,
                                })
                              }
                              className="cursor-pointer mr-2 !fill-onSurface"
                            />
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        '& .MuiInputBase-root': {
                          alignItems: 'flex-start',
                          resize: 'vertical',
                          overflow: 'auto',
                        },
                      }}
                    />
                  )}
                />
                {type === 'edit' && initialData?.id && (
                  <Box
                    sx={{
                      mt: 1.5,
                      display: 'flex',
                      alignItems: 'center',
                      flexWrap: 'wrap',
                      gap: 2,
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Typography
                        variant="subtitle2"
                        sx={{ fontWeight: 'bold' }}
                      >
                        ID:
                      </Typography>
                      <Typography
                        variant="subtitle2"
                        sx={{ ml: 0.5, fontWeight: 'medium' }}
                      >
                        {initialData.id}
                      </Typography>
                      <AnimatedCopyButton
                        value={recordId}
                        size="small"
                        sx={{ ml: 0.5, color: 'primary.main' }}
                        className="border border-primary text-primary rounded-[100%] p-1"
                      />
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Typography
                        variant="subtitle2"
                        sx={{ fontWeight: 'bold' }}
                      >
                        アプリ内URL:
                      </Typography>
                      <Typography
                        variant="subtitle2"
                        sx={{
                          ml: 0.5,
                          fontWeight: 'medium',
                          wordBreak: 'break-all',
                        }}
                      >
                        {inAppUrl}
                      </Typography>
                      <AnimatedCopyButton
                        value={inAppUrl}
                        size="small"
                        sx={{ ml: 0.5, color: 'primary.main' }}
                        className="border border-primary text-primary rounded-[100%] p-1"
                      />
                    </Box>
                  </Box>
                )}

                <Divider
                  className="h-[2px]"
                  sx={{ my: 1, background: '#000' }}
                />

                <Grid
                  container
                  className="pt-1"
                  spacing={{ xs: 1, sm: 3 }}
                  sx={{ color: '#4A5568' }}
                >
                  <Grid item>
                    <Typography variant="subtitle2">
                      <span className="font-bold">作成日: </span>
                      <Typography variant="subtitle2" component="span">
                        {dateFormat(
                          initialData?.createdAt,
                          DateFormat.fullDateYYYYMMDDWithDotHHmm,
                        ) ?? '--'}{' '}
                      </Typography>
                    </Typography>
                  </Grid>
                  <Grid item>
                    <Typography variant="subtitle2">
                      <span className="font-bold">最終更新日: </span>
                      <Typography variant="subtitle2" component="span">
                        {dateFormat(
                          initialData?.updatedAt,
                          DateFormat.fullDateYYYYMMDDWithDotHHmm,
                        ) ?? '--'}{' '}
                      </Typography>
                    </Typography>
                  </Grid>
                  {type === 'edit' && (
                    <Grid item>
                      <Typography variant="subtitle2">
                        <span className="font-bold">更新適用日: </span>
                        <Typography variant="subtitle2" component="span">
                          {dateFormat(
                            initialData?.publishedAt,
                            DateFormat.fullDateYYYYMMDDWithDot,
                          ) ?? '--'}{' '}
                        </Typography>
                      </Typography>
                    </Grid>
                  )}
                </Grid>
              </Box>

              <Form onSubmit={() => {}} className="space-y-6 pt-2">
                {() => (
                  <>
                    <Grid
                      container
                      alignItems="flex-end"
                      className="mb-4 bg-[#0000000D] p-3 rounded-lg"
                    >
                      <Grid item xs={12} sm={7}>
                        <Controller
                          name="parentContentId"
                          control={control}
                          render={({ field, fieldState }) => (
                            <TextField
                              id="category-select"
                              label="カテゴリ"
                              fullWidth
                              variant="outlined"
                              placeholder="カテゴリを選択してください"
                              value={selectedCategoryDisplayName || ''}
                              onClick={() => setIsCategoryMenuOpen(true)}
                              error={!!fieldState.error}
                              helperText={fieldState.error?.message || ''}
                              InputProps={{
                                readOnly: true,
                                className: 'h-12',
                                sx: {
                                  backgroundColor: '#F2F2F2',
                                  cursor: 'pointer',
                                },
                                endAdornment: (
                                  <InputAdornment position="end">
                                    {field.value && (
                                      <IconButton
                                        onClick={handleClearCategory}
                                        size="small"
                                      >
                                        <CloseIcon />
                                      </IconButton>
                                    )}
                                    <ArrowDropDown className="mr-1" />
                                  </InputAdornment>
                                ),
                              }}
                            />
                          )}
                        />
                      </Grid>
                    </Grid>

                    <Box
                      className="mb-0 rounded-t-lg"
                      sx={{ border: '1px solid #D2DBDA', borderBottom: 0 }}
                    >
                      <Tabs
                        value={activeTabLang}
                        onChange={handleTabChange}
                        variant="scrollable"
                        scrollButtons="auto"
                        className="bg-[#0000000D]"
                        sx={{
                          '& .MuiTabs-flexContainer': {
                            justifyContent: 'center',
                          },
                          '& .MuiTab-root': {
                            minWidth: 60,
                            maxWidth: 30,
                            padding: '8px 16px',
                          },
                        }}
                      >
                        {availableLanguages.map((langItem) => {
                          const isPublished =
                            contentValues?.[langItem.code]?.active;
                          return (
                            <Tab
                              key={langItem.code}
                              value={langItem.code}
                              label={
                                <Box
                                  sx={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: 0.5,
                                  }}
                                >
                                  {langItem.name}
                                  {isPublished && (
                                    <Box
                                      sx={{
                                        width: 8,
                                        height: 8,
                                        borderRadius: '50%',
                                        backgroundColor: 'error.main',
                                      }}
                                    />
                                  )}
                                </Box>
                              }
                            />
                          );
                        })}
                      </Tabs>
                      {availableLanguages.map((lang) => (
                        <Box
                          key={lang.code}
                          sx={{
                            display:
                              activeTabLang === lang.code ? 'block' : 'none',
                          }}
                        >
                          <Box
                            className={clsx('p-4 rounded-b-lg', {
                              'border-red-500':
                                !!formState.errors.content ||
                                !!formState.errors.title,
                            })}
                          >
                            <Controller
                              name={`content.${lang.code}.active`}
                              control={control}
                              render={({ field }) => (
                                <FormControlLabel
                                  control={
                                    <Checkbox
                                      checked={!!field.value}
                                      onChange={(e) =>
                                        field.onChange(e.target.checked)
                                      }
                                    />
                                  }
                                  label="公開"
                                />
                              )}
                            />

                            <Box className="mb-4 mt-3 border-b border-dashed pb-6">
                              <Controller
                                name={`title.${lang.code}`}
                                control={control}
                                render={({ field, fieldState }) => (
                                  <>
                                    <TextField
                                      {...field}
                                      label="記事タイトル"
                                      multiline
                                      maxRows={3}
                                      value={field.value || ''}
                                      onChange={(e) =>
                                        field.onChange(e.target.value)
                                      }
                                      fullWidth
                                      placeholder="記事タイトルを入力してください"
                                      error={!!fieldState.error}
                                      helperText={fieldState.error?.message}
                                      InputProps={{
                                        endAdornment: field.value && (
                                          <InputAdornment position="start">
                                            <CloseIcon
                                              onClick={() =>
                                                setValue(
                                                  `title.${lang.code}`,
                                                  '',
                                                  {
                                                    shouldValidate: true,
                                                    shouldDirty: true,
                                                  },
                                                )
                                              }
                                              className="cursor-pointer mr-2 !fill-onSurface"
                                            />
                                          </InputAdornment>
                                        ),
                                      }}
                                      sx={{
                                        '& .MuiInputBase-root': {
                                          alignItems: 'flex-start',
                                          resize: 'vertical',
                                          overflow: 'auto',
                                        },
                                      }}
                                    />
                                    <div className="text-right text-sm">
                                      {field.value?.length || 0}
                                      <span className="text-black">/300</span>
                                    </div>
                                  </>
                                )}
                              />
                            </Box>

                            <Box className="border-b border-dashed pb-6">
                              <Controller
                                control={control}
                                name={`content.${lang.code}.body`}
                                render={({ field, fieldState }) => (
                                  <>
                                    <Editor
                                      value={field.value ?? ''}
                                      onChange={(content) => {
                                        field.onChange(content.html);
                                        setBodyCharCounts((prev) => ({
                                          ...prev,
                                          [lang.code]: (
                                            content.html ?? ''
                                          ).trim().length,
                                        }));
                                      }}
                                    />
                                    <Box
                                      sx={{
                                        textAlign: 'right',
                                        fontSize: '0.875rem',
                                        color: 'text.secondary',
                                        mt: 1,
                                      }}
                                    >
                                      {bodyCharCounts[lang.code] ?? 0} 文字
                                    </Box>
                                    {fieldState.error && (
                                      <Typography
                                        color="error"
                                        variant="caption"
                                        mt={1}
                                      >
                                        {fieldState.error.message}
                                      </Typography>
                                    )}
                                  </>
                                )}
                              />
                            </Box>
                          </Box>
                        </Box>
                      ))}
                    </Box>
                  </>
                )}
              </Form>
            </Paper>
          </Grid>
          <Grid item xs={12} md={3.5}>
            <FaqArticleRightPanel
              applicationDate={applicationDate.format(
                DateFormat.fullDateWithHyphen,
              )}
              status={initialData?.status}
              hasChanges={formState.isDirty}
              isFormInvalid={isFormInvalid}
              onComplete={openConfirmationDialog}
              setDialogState={setDialogState}
              updateApplicationDate={(date) => setApplicationDate(dayjs(date))}
            />
          </Grid>
        </Grid>
        <CategorySideMenu
          open={isCategoryMenuOpen}
          onClose={() => setIsCategoryMenuOpen(false)}
          onSelectCategory={handleCategorySelect}
          selectedCategoryCode={contentCategoryCode}
        />
      </Paper>

      {dialogState && (
        <CommonDialog
          open={dialogState.open}
          type={dialogState.type}
          message={dialogState.message}
          onOk={handleDialogConfirm}
          onCancel={handleDialogCancel}
          okText="OK"
          cancelText="戻る"
        />
      )}
    </>
  );
};
