import Typography from '@mui/material/Typography';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { Breadcrumb } from '@/components/elements';
import { Button } from '@/components/forms/button';
import { Head } from '@/components/seo/head';
import { routes } from '@/routes/routes';

export const ContentManagementFAQ = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const router = routes.contentManagement.index;

  const breadcrumbItems = [
    { name: t('menu.home'), href: routes.home.index },
    { name: t('menu.contentManagement'), href: routes.contentManagement.index },
    { name: t('faq.title', 'FAQ') },
  ];

  const buttonItems = [
    {
      title: '記事一覧',
      onClick: () =>
        navigate(
          `${router}${routes.contentManagement.faq.index}${routes.contentManagement.faq.article.index}`,
        ),
    },
    {
      title: 'カテゴリー覧',
      onClick: () =>
        navigate(
          `${router}${routes.contentManagement.faq.index}${routes.contentManagement.faq.category.index}`,
        ),
    },
  ];

  return (
    <>
      <Head title="FAQ" />
      <Breadcrumb title="FAQ" items={breadcrumbItems} />
      <div className="p-4">
        <div className="grid grid-cols-3 gap-4">
          {buttonItems.map((item, index) => (
            <Button
              key={`item-${index}-title`}
              onClick={item.onClick}
              className="p-6 bg-primary text-center hover:opacity-90 transition-opacity !w-full !h-12"
            >
              <Typography variant="h3" className="!text-onPrimary">
                {item.title}
              </Typography>
            </Button>
          ))}
        </div>
      </div>
    </>
  );
};
