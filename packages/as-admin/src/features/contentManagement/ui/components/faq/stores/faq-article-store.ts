import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import { Content, FaqCategory } from '@/bundles/model'; //

// Utility function to build category hierarchy path from FAQ categories
const buildCategoryHierarchy = (
  categoryId: string,
  faqCategories: FaqCategory[],
): {
  categoryPath: string;
  categoryDisplayName: string;
  hasSubCategories: boolean;
} => {
  if (!categoryId || !faqCategories?.length) {
    return {
      categoryPath: 'Unknown Category',
      categoryDisplayName: 'Unknown Category',
      hasSubCategories: false,
    };
  }

  // Find the category by ID in the hierarchy
  const findCategory = (
    categories: FaqCategory[],
    targetId: string,
  ): FaqCategory | null => {
    for (const category of categories) {
      if (category.id === targetId) {
        return category;
      }
      // Search in subCategories recursively
      if (category.subCategories && Array.isArray(category.subCategories)) {
        const found = findCategory(category.subCategories, targetId);
        if (found) return found;
      }
    }
    return null;
  };

  // Build path by traversing up the hierarchy
  const buildPath = (
    categories: FaqCategory[],
    targetId: string,
    currentPath: string[] = [],
  ): string[] => {
    for (const category of categories) {
      if (category.id === targetId) {
        const displayName =
          category.displayName?.['en'] ||
          category.displayName?.['ja'] ||
          category.id ||
          'Unknown';
        return [displayName, ...currentPath];
      }

      // Check if target is in subCategories
      if (category.subCategories && Array.isArray(category.subCategories)) {
        const found = findCategory(category.subCategories, targetId);
        if (found) {
          const displayName =
            category.displayName?.['en'] ||
            category.displayName?.['ja'] ||
            category.id ||
            'Unknown';
          const childPath = buildPath(
            category.subCategories,
            targetId,
            currentPath,
          );
          return [displayName, ...childPath];
        }
      }
    }
    return currentPath;
  };

  const foundCategory = findCategory(faqCategories, categoryId);
  if (!foundCategory) {
    return {
      categoryPath: 'Unknown Category',
      categoryDisplayName: 'Unknown Category',
      hasSubCategories: false,
    };
  }

  const pathArray = buildPath(faqCategories, categoryId);
  const categoryDisplayName =
    foundCategory.displayName?.['en'] ||
    foundCategory.displayName?.['ja'] ||
    foundCategory.id ||
    'Unknown';
  const hasSubCategories = !!(
    foundCategory.subCategories && foundCategory.subCategories.length > 0
  );

  return {
    categoryPath: pathArray.join(' / '),
    categoryDisplayName,
    hasSubCategories,
  };
};

export interface FaqArticle {
  id: string;
  title: string;
  status: 'draft' | 'published' | 'archived'; // Assuming status can be one of these
  parentContentId?: string; // Parent content ID for hierarchy
  categoryPath: string; // Display path with hierarchy structure
  categoryDisplayName: string; // EN title of the category
  hasSubCategories: boolean; // Whether this category has children
  lastUpdated: string;
  isChecked: boolean;
}

interface FaqArticleState {
  articles: FaqArticle[];
  isLoading: boolean;
  error: string | null;
  currentPage: number;
  totalPages: number;
  itemsPerPage: number;

  // Actions
  setArticles: (articles: FaqArticle[]) => void;
  setLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  setCurrentPage: (page: number) => void;
  setTotalPages: (totalPages: number) => void;
  toggleArticleCheck: (id: string) => void;

  // Async actions will now accept the mutate functions as arguments
  fetchArticles: (
    getContentsMutate: (params: {
      searchQuery: any;
      options?: any;
    }) => Promise<Content[]>,
    faqCategories?: FaqCategory[],
  ) => Promise<void>;
  deleteArticle: (
    id: string,
    deleteContentMutate: (id: string) => Promise<void>,
  ) => Promise<void>;
}

export const useFaqArticleStore = create<FaqArticleState>()(
  persist(
    (set, get) => ({
      articles: [],
      isLoading: false,
      error: null,
      currentPage: 1,
      totalPages: 1,
      itemsPerPage: 10,

      setArticles: (articles) => set({ articles }),
      setLoading: (isLoading) => set({ isLoading }),
      setError: (error) => set({ error }),
      setCurrentPage: (page) => set({ currentPage: page }),
      setTotalPages: (totalPages) => set({ totalPages }),
      toggleArticleCheck: (id) =>
        set((state) => ({
          articles: state.articles.map((article) =>
            article.id === id
              ? { ...article, isChecked: !article.isChecked }
              : article,
          ),
        })),

      fetchArticles: async (getContentsMutate, faqCategories = []) => {
        // Now accepts getContentsMutate and faqCategories
        set({ isLoading: true, error: null });
        try {
          const { currentPage, itemsPerPage } = get();

          const contents = await getContentsMutate({
            searchQuery: {
              contentCategoryCode: 'faq',
              page: currentPage,
              limit: itemsPerPage,
            },
          });

          // Build category hierarchy for each article
          const mappedArticles: FaqArticle[] = [];
          if (contents) {
            for (const content of contents) {
              const categoryInfo = buildCategoryHierarchy(
                content.parentContentId || '',
                faqCategories,
              );

              mappedArticles.push({
                id: content.id || `temp-${Math.random()}`,
                title:
                  content.title?.['en'] || content.title?.['ja'] || 'No Title',
                parentContentId: content.parentContentId,
                categoryPath: categoryInfo.categoryPath,
                categoryDisplayName: categoryInfo.categoryDisplayName,
                hasSubCategories: categoryInfo.hasSubCategories,
                status: content.status || 'draft', // Assuming status is part of Content
                lastUpdated: content.updatedAt || content.createdAt || '',
                isChecked: false,
              });
            }
          }

          set({
            articles: mappedArticles,
            totalPages: Math.ceil(mappedArticles.length / itemsPerPage),
            isLoading: false,
          });
        } catch (err: any) {
          console.error('Failed to fetch articles:', err);
          set({
            error: err.message || 'Failed to load articles.',
            isLoading: false,
          });
        }
      },

      deleteArticle: async (
        id: string,
        deleteContentMutate: (id: string) => Promise<void>,
      ) => {
        // Now accepts deleteContentMutate
        set({ isLoading: true, error: null });
        try {
          await deleteContentMutate(id);
          // After deletion, refetch the list to update the UI
          await get().fetchArticles; // Pass getContentsMutate again
        } catch (err: any) {
          console.error('Failed to delete article:', err);
          set({
            error: err.message || 'Failed to delete article.',
            isLoading: false,
          });
        }
      },
    }),
    {
      name: 'faq-article-storage',
    },
  ),
);
