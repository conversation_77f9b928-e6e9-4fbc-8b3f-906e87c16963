import CalendarMonthOutlinedIcon from '@mui/icons-material/CalendarMonthOutlined';
import CloseIcon from '@mui/icons-material/Close';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import PublishIcon from '@mui/icons-material/Publish';
import SaveAsIcon from '@mui/icons-material/SaveAs';
import { Box, Card, CardContent, Divider, Typography } from '@mui/material';
import clsx from 'clsx';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useState } from 'react';

import { ContentStatus } from '@/bundles/model';
import { DialogState } from '@/components/dialog/CommonDialog';
import { Button } from '@/components/forms';
import { ApplicationDatePicker } from '@/components/forms/date-picker/ApplicationDatePicker';
import { DateFormat } from '@/constants/date';
import { StatusApplicationDate } from '@/constants/utils';

/**
 * Component props
 */
interface FaqArticleRightPanelProps {
  applicationDate?: string;
  status?: ContentStatus;
  hasChanges?: boolean;
  isFormInvalid: boolean;
  onComplete?: (action: StatusApplicationDate) => void;
  setDialogState: (dialogState?: DialogState) => void;
  updateApplicationDate: (applicationDate: string) => void;
}

/**
 * ApplicationDate component show form select date apply for UI Text
 */
export const FaqArticleRightPanel = ({
  applicationDate: initialDate,
  status,
  hasChanges = false,
  isFormInvalid,
  setDialogState,
  onComplete,
  updateApplicationDate,
}: FaqArticleRightPanelProps) => {
  const [applicationDate, setApplicationDate] = useState<Dayjs | null>(dayjs());
  const [isInitial, setIsInitial] = useState<boolean>(true);

  useEffect(() => {
    const newDate = initialDate ? dayjs(initialDate) : dayjs();
    setApplicationDate(newDate);
    updateApplicationDate(newDate.format(DateFormat.fullDateWithHyphen));

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialDate]);

  // Constants
  const isDraft = status === ContentStatus.Draft;
  const isWaitingToPublish =
    status === ContentStatus.Published &&
    applicationDate?.isAfter(dayjs(), 'day') &&
    isInitial;
  const isDraftButtonDisabled = isDraft && !hasChanges;

  const handleAction = (
    actionStatus: StatusApplicationDate,
    showConfirm = false,
  ) => {
    // Check if the action is publish and the date is today
    if (showConfirm && actionStatus === StatusApplicationDate.Publish) {
      const today = dayjs().format(DateFormat.fullDateWithHyphen);
      const selectedDate =
        applicationDate?.format(DateFormat.fullDateWithHyphen) || '';

      if (dayjs(selectedDate).isSame(dayjs(today), 'day')) {
        setDialogState({
          open: true,
          type: 'alert',
          message: '更新内容をすぐに反映しますか？',
          action: actionStatus,
        });
        return;
      }
    }

    if (onComplete) {
      onComplete(actionStatus);
    }
  };

  // Các handler cụ thể
  const handleSaveDraft = () => handleAction(StatusApplicationDate.Draft);
  const handlePublish = () => handleAction(StatusApplicationDate.Publish, true);
  const handleDelete = () => handleAction(StatusApplicationDate.Delete);
  const handleCancel = () => handleAction(StatusApplicationDate.Unpublish);

  // Render for Waiting To Publish status
  if (isWaitingToPublish) {
    return (
      <Box className="flex justify-center items-start h-screen">
        <Card className="w-full border border-gray-200 rounded-lg shadow-sm overflow-hidden">
          <CardContent>
            <Typography variant="h4" className="pb-2 font-semibold">
              更新適用日
            </Typography>

            <Box className="flex items-center mb-6">
              <Box component="span" className="pr-3">
                <CalendarMonthOutlinedIcon className="text-gray-500" />
              </Box>
              <Typography variant="body2">
                {applicationDate?.format(DateFormat.fullDateYYYYMMDDWithDot) ||
                  ''}
              </Typography>
            </Box>

            <Divider className="border-t-1 border-outline" />

            <Box className="border-t border-gray-200 pt-6">
              <Button
                variant="text"
                color="primary"
                className="!w-full justify-center"
                startIcon={<CloseIcon className="!fill-primary" />}
                onClick={() => {
                  setIsInitial(true);
                  handleCancel();
                }}
              >
                <Typography variant="h4" className="!text-primary">
                  キャンセル
                </Typography>
              </Button>
            </Box>
          </CardContent>
        </Card>
      </Box>
    );
  }

  // Render for other statuses
  return (
    <Box className="flex justify-center items-start h-screen">
      <Card className="w-full border border-gray-200 rounded-lg shadow-sm overflow-hidden">
        <CardContent className="p-6">
          <ApplicationDatePicker
            value={applicationDate}
            onChange={(date) => {
              updateApplicationDate(date);
              setIsInitial(false);
            }}
            minDate={dayjs()}
          />

          <Divider className="border-t-1 border-outline" />

          <Box className="flex flex-col gap-4 pt-6">
            <Box className="flex gap-4 w-full">
              <Button
                variant="outlined"
                className="!w-1/2 !h-12"
                startIcon={
                  <SaveAsIcon
                    className={`${
                      isDraftButtonDisabled
                        ? '!fill-[#A6A9A9]'
                        : '!fill-primary'
                    }`}
                  />
                }
                onClick={handleSaveDraft}
                disabled={isDraftButtonDisabled || isFormInvalid}
              >
                <Typography
                  variant="h4"
                  className={clsx(
                    isDraftButtonDisabled ? '!text-[#A6A9A9]' : '!text-primary',
                  )}
                >
                  下書き
                </Typography>
              </Button>

              <Button
                variant="contained"
                className="!w-1/2 !h-12"
                startIcon={<PublishIcon className="!fill-onPrimary" />}
                onClick={handlePublish}
                disabled={isFormInvalid}
              >
                <Typography variant="h4" className="!text-onPrimary">
                  公開
                </Typography>
              </Button>
            </Box>

            <Button
              variant="text"
              className="!w-full justify-center"
              startIcon={
                <DeleteOutlineIcon
                  className={!isDraft ? '!fill-[#A6A9A9]' : '!fill-red-500'}
                />
              }
              onClick={handleDelete}
              disabled={!isDraft}
            >
              <Typography
                variant="h4"
                className={!isDraft ? '!text-[#A6A9A9]' : '!text-red-500'}
              >
                削除
              </Typography>
            </Button>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};
