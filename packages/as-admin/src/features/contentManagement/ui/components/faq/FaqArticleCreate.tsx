import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import { Content } from '@/bundles/model';
import { Breadcrumb } from '@/components/elements';
import { LoadingIndicator } from '@/components/feedback/loading-indicator';
import { Head } from '@/components/seo/head';
import { routes } from '@/routes/routes';

import { useCreateContent } from '../faq/hook/use-content-management-api';

import { FaqArticleForm, FaqArticleFormValues } from './FaqArticleForm';

export const FaqArticleCreate = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const createContentMutation = useCreateContent();

  const onSubmitForm = async (formValues: FaqArticleFormValues) => {
    // Transform FaqArticleFormValues to Content payload
    const payload: Content = {
      ...formValues,
      title: {},
      content: {},
      status: formValues.status as Content['status'],
    };

    if (!payload.title) {
      payload.title = {};
    }

    for (const langCode in formValues.title) {
      if (formValues.title[langCode]) {
        payload.title[langCode] = formValues.title[langCode]!;
      }
    }

    for (const langCode in formValues.content) {
      const formContent = formValues.content[langCode];
      if (formContent?.body) {
        if (!payload.content) {
          payload.content = {};
        }
        payload.content[langCode] = {
          active: formContent.active ?? false,
          body: formContent.body,
          title: formValues.title[langCode] || '',
          url: '',
          attachments: [],
        };
      }
    }

    payload.publishedAt = undefined;

    try {
      await createContentMutation.mutateAsync(payload);
      toast.success(t('common.toast.registerSuccess', { name: 'FAQ記事' }));
      navigate(
        `${routes.contentManagement.index}${routes.contentManagement.faq.index}${routes.contentManagement.faq.article.index}`,
      );
    } catch (error) {
      toast.error(t('common.toast.registerFailed', { name: 'FAQ記事' }));
    }
  };

  const breadcrumbItems = [
    { name: 'ホーム', href: routes.home.index },
    { name: 'コンテンツ管理', href: routes.contentManagement.index },
    {
      name: 'FAQ',
      href: `${routes.contentManagement.index}${routes.contentManagement.faq.index}`,
    },
    {
      name: '記事一覧',
      href: `${routes.contentManagement.index}${routes.contentManagement.faq.index}${routes.contentManagement.faq.article.index}`,
    },
    { name: '新規記事作成' },
  ];

  if (createContentMutation.isPending) {
    return <LoadingIndicator isLoading={true} fullscreen />;
  }

  return (
    <>
      <Head title="FAQ記事追加" />
      <Box>
        <Breadcrumb title="FAQ" items={breadcrumbItems} />
      </Box>
      <FaqArticleForm type="create" onSubmit={onSubmitForm} />
    </>
  );
};
