import CloseIcon from '@mui/icons-material/Close';
import SearchIcon from '@mui/icons-material/Search';
import {
  Box,
  Drawer,
  Typography,
  IconButton,
  TextField,
  InputAdornment,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  CircularProgress,
} from '@mui/material';
import React, { useState, useMemo } from 'react';

import { FaqCategory } from '@/bundles/model';

import { useFaqCategoriesBranches } from './hook/use-faq-categories-branches';

interface CategorySideMenuProps {
  open: boolean;
  onClose: () => void;
  onSelectCategory: (categoryCode: string, categoryName: string) => void;
  selectedCategoryCode?: string;
}

const flattenFaqCategoriesForDropdown = (
  categories: FaqCategory[],
): FaqCategory[] => {
  const result: FaqCategory[] = [];

  const flatten = (cats: FaqCategory[], level = 0) => {
    cats.forEach((cat) => {
      const hasEnglishTitle =
        cat.displayName?.['en'] && cat.displayName['en'].trim().length > 0;
      if (hasEnglishTitle) {
        result.push({ ...cat, level } as any);
      }

      if ((cat as any).children && Array.isArray((cat as any).children)) {
        flatten((cat as any).children, level + 1);
      } else if (cat.subCategories && Array.isArray(cat.subCategories)) {
        flatten(cat.subCategories as FaqCategory[], level + 1);
      }
    });
  };

  flatten(categories);
  return result;
};

export const CategorySideMenu: React.FC<CategorySideMenuProps> = ({
  open,
  onClose,
  onSelectCategory,
  selectedCategoryCode,
}) => {
  const { data: branches, isLoading } = useFaqCategoriesBranches(undefined, {
    refetchOnWindowFocus: true,
    staleTime: 0,
  });
  const [searchTerm, setSearchTerm] = useState('');
  const allCategories = useMemo(() => {
    if (!branches || branches.length === 0) return [];
    const firstBranch = branches[0];

    const allFaqCategories: any[] = [];
    if (
      firstBranch?.faqCategories &&
      Array.isArray(firstBranch.faqCategories)
    ) {
      allFaqCategories.push(...firstBranch.faqCategories);
    }

    const publishedCategories = allFaqCategories
      .filter((category: any) => {
        const isPublished =
          category.status === 'published' || category.active === true;
        const hasEngTitle =
          category.displayName?.['en'] &&
          category.displayName['en'].trim().length > 0;
        return isPublished && hasEngTitle;
      })
      .sort((a: any, b: any) => {
        const dateA = new Date(a.createdAt || a.updatedAt || 0).getTime();
        const dateB = new Date(b.createdAt || b.updatedAt || 0).getTime();
        return dateB - dateA;
      });
    return flattenFaqCategoriesForDropdown(
      publishedCategories as FaqCategory[],
    );
  }, [branches]);

  const handleCategorySelect = (category: FaqCategory) => {
    const englishName = category.displayName?.['en'] || '';
    onSelectCategory(category.id || '', englishName);
    onClose();
  };

  const filteredCategories = useMemo(() => {
    if (!allCategories) return [];
    return allCategories.filter((cat: FaqCategory) => {
      // Only show categories that have Japanese title
      const hasEnglishTitle =
        cat.displayName?.['en'] && cat.displayName['en'].trim().length > 0;
      if (!hasEnglishTitle) return false;

      // Apply search filter
      if (!searchTerm) return true;

      return (
        cat.displayName?.['en']
          ?.toLowerCase()
          .includes(searchTerm.toLowerCase()) ||
        cat.displayName?.['ja']
          ?.toLowerCase()
          .includes(searchTerm.toLowerCase())
      );
    });
  }, [allCategories, searchTerm]);

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          width: '100%',
          maxWidth: '320px',
          height: '100%',
          borderRadius: 0,
          boxShadow: 3,
        },
      }}
    >
      <Box
        sx={{
          p: 2,
          borderBottom: 1,
          borderColor: 'divider',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <Typography variant="h6">カテゴリ</Typography>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </Box>

      <Box sx={{ p: 2, flexGrow: 1, overflowY: 'auto' }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="検索..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          sx={{ mb: 2 }}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />

        {isLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <List>
            {filteredCategories.map((cat: FaqCategory & { level?: number }) => (
              <ListItem key={cat.id} disablePadding>
                <ListItemButton
                  selected={selectedCategoryCode === cat.id}
                  onClick={() => handleCategorySelect(cat)}
                  sx={{ paddingLeft: ((cat.level || 0) + 1) * 2 }}
                >
                  <ListItemText
                    primary={cat.displayName?.['en'] || ''}
                    secondary={cat.displayName?.['ja'] || ''}
                  />
                </ListItemButton>
              </ListItem>
            ))}
          </List>
        )}
      </Box>
    </Drawer>
  );
};
