import { Article } from '@mui/icons-material';
import { Box, Typography } from '@mui/material';
import React from 'react';

import { Button } from '@/components/forms';

interface ArticleHeaderProps {
  onAddNewArticle: () => void;
}

export const ArticleHeader: React.FC<ArticleHeaderProps> = ({
  onAddNewArticle,
}) => {
  return (
    <Box className="flex justify-between items-center mb-6 border-b pb-4">
      <Typography variant="h2" className="text-2xl font-bold text-gray-800">
        記事一覧
      </Typography>
      <Button
        className="!bg-surfaceContainerHigh !w-[120px] !h-[45px]"
        variant="contained"
        color="inherit"
        size="large"
        onClick={onAddNewArticle}
        startIcon={<Article className="mr-2 !text-primary" />}
      >
        新規作成
      </Button>
    </Box>
  );
};
