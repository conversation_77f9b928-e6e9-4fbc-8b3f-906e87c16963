import {
  Box,
  But<PERSON>,
  Chip,
  Container,
  Divider,
  Paper,
  Typography,
} from '@mui/material';
import { useLocation, useNavigate } from 'react-router-dom';

import { Content } from '@/bundles/model';
import { Head } from '@/components/seo/head';

export const ContentManagementFaqPreview = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Get the form data passed from the previous page
  const { formData } = (location.state as {
    formData: Content;
  }) || { formData: null };

  const handleBack = () => {
    navigate(-1); // Go back to the form page
  };

  // If no form data is found, show a message
  if (!formData) {
    return (
      <Container>
        <Typography sx={{ mt: 4 }}>
          プレビューするコンテンツがありません。
        </Typography>
        <Button variant="outlined" onClick={handleBack} sx={{ mt: 2 }}>
          編集に戻る
        </Button>
      </Container>
    );
  }

  return (
    <>
      <Head title={`プレビュー: ${formData.title?.en || '無題の記事'}`} />
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 2,
          }}
        >
          <Typography variant="h4" component="h1" fontWeight="bold">
            プレビュー
          </Typography>
          <Button variant="outlined" onClick={handleBack}>
            編集に戻る
          </Button>
        </Box>

        {/* This Paper component mimics the style of the FaqArticleDetails page */}
        <Paper elevation={2} sx={{ p: 4, mt: 2 }}>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mb: 2,
            }}
          >
            <Typography variant="h4" gutterBottom>
              {formData.title?.en || 'No Title'}
            </Typography>
            <Chip
              label={formData.status || 'Draft'}
              color={formData.status === 'published' ? 'success' : 'default'}
            />
          </Box>

          <Box sx={{ mb: 3 }}>
            <Chip label={formData.contentCategoryCode || 'No Category'} />
          </Box>

          <Divider sx={{ mb: 3 }} />

          {/* Map through the languages and display their content */}
          {Object.entries(formData.content || {}).map(
            ([langCode, langContent]) => {
              // Only render the section if the body has actual content
              const hasContent =
                langContent?.body &&
                langContent.body
                  .replace(/<p>(\s*<br\s*\/?>\s*)*<\/p>/gi, '')
                  .trim().length > 0;

              if (!hasContent) {
                return null;
              }

              return (
                <Box key={langCode} sx={{ mb: 4, '&:last-child': { mb: 0 } }}>
                  <Typography
                    variant="h5"
                    sx={{
                      mb: 2,
                      textTransform: 'uppercase',
                      fontWeight: 'bold',
                    }}
                  >
                    {langCode}
                  </Typography>

                  {/* Check if the content for this language is marked as "公開" */}
                  {langContent?.active ? (
                    <Box>
                      <Typography variant="h6" gutterBottom>
                        {formData.title?.[langCode]}
                      </Typography>
                      {/* Safely render the HTML content from the editor */}
                      <Box
                        dangerouslySetInnerHTML={{
                          __html: langContent?.body || '',
                        }}
                        sx={{
                          mt: 2,
                          p: 2,
                          border: '1px solid #ddd',
                          borderRadius: 1,
                          wordBreak: 'break-word',
                        }}
                      />
                    </Box>
                  ) : (
                    <Typography color="text.secondary">
                      (この言語のコンテンツは非公開です)
                    </Typography>
                  )}
                </Box>
              );
            },
          )}
        </Paper>
      </Container>
    </>
  );
};
