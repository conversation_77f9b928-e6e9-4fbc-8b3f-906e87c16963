import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { Box, Checkbox, IconButton, Typography } from '@mui/material';
import React from 'react';

import { FaqCategory } from '@/features/contentManagement/domain/faq/category/types';
import { getCategoryDisplayName } from '@/features/contentManagement/domain/faq/category/utils';

interface CategoryTreeNodeProps {
  category: FaqCategory & { subCategories?: { active: boolean }[] };
  level: number;
  isExpanded: boolean;
  isLoading: boolean;
  isSelected?: boolean;
  isEditing?: boolean;
  onToggle: (categoryId: string) => void;
  onSelect?: (category: FaqCategory) => void;
  onActiveChange: (categoryId: string, active: boolean) => void;
}

export const CategoryTreeNode: React.FC<CategoryTreeNodeProps> = ({
  category,
  level,
  isExpanded,
  isLoading,
  isSelected = false,
  isEditing,
  onToggle,
  onSelect,
  onActiveChange,
}) => {
  const hasChildren = category.children && category.children.length > 0;

  const handleToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (hasChildren) {
      onToggle(category.id);
    }
  };

  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    event.stopPropagation();
    onActiveChange(category.id, event.target.checked);
  };

  const handleRowClick = () => {
    if (isEditing && onSelect) {
      onSelect(category);
    }
  };

  const getBackgroundColor = () => {
    if (isSelected) return 'bg-[#E0E0E0]';
    return '';
  };

  const subCategoryCount = React.useMemo(() => {
    if (!category.subCategories) {
      return 0;
    }
    if (isEditing) {
      return category.subCategories.length;
    }
    return category.subCategories.filter((subCat) => subCat.active).length;
  }, [category.subCategories, isEditing]);

  return (
    <Box
      className={`flex items-center py-2 px-2 border-b border-gray-200 hover:bg-gray-100 ${getBackgroundColor()}`}
      style={{
        paddingLeft: `${level * 24}px`,
        cursor: isEditing ? 'pointer' : 'default',
      }}
      onClick={handleRowClick}
    >
      {isEditing && (
        <Checkbox
          size="medium"
          checked={category.active}
          onChange={handleCheckboxChange}
          className="mr-2"
          onClick={(e) => e.stopPropagation()}
        />
      )}
      <Box className="flex-1 items-center">
        <Typography variant="body2" className="!text-onSurface">
          {getCategoryDisplayName(category, 'en')}
        </Typography>
        <Typography variant="subtitle2" className="!text-onSurfaceVariant">
          {getCategoryDisplayName(category, 'ja')}
        </Typography>
      </Box>

      <Typography
        variant="body2"
        className={`!text-surfaceTint font-medium ${
          hasChildren && subCategoryCount != 0 ? 'pr-2' : 'pr-0'
        }`}
      >
        {subCategoryCount === 0
          ? ''
          : subCategoryCount >= 100
            ? '100+'
            : subCategoryCount}
      </Typography>

      <Box className="flex items-center justify-center">
        {hasChildren && subCategoryCount != 0 && (
          <IconButton size="small" onClick={handleToggle} disabled={isLoading}>
            {isExpanded ? (
              <ExpandLessIcon fontSize="small" />
            ) : (
              <ExpandMoreIcon fontSize="small" />
            )}
          </IconButton>
        )}
      </Box>
    </Box>
  );
};
