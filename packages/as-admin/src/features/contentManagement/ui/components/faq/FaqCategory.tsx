import CheckIcon from '@mui/icons-material/Check';
import { Box, Typography } from '@mui/material';
import clsx from 'clsx';

import { CommonDialog } from '@/components/dialog/CommonDialog';
import { Breadcrumb } from '@/components/elements';
import { Button } from '@/components/forms';
import { Select } from '@/components/select';
import { Head } from '@/components/seo/head';
import { DateFormat } from '@/constants/date';
import { dateFormat } from '@/constants/utils';
import { useCategoryContainer } from '@/features/contentManagement/ui/containers/faq/CategoryContainer';
import { routes } from '@/routes/routes';

import { CategoryEditor } from './FaqCategoryEditor';
import { CategoryList } from './FaqCategoryList';

export const ContentManagementFaqCategory = () => {
  const {
    // Global states
    categories,
    selectedCategory,
    selectedCategoryParentId,
    selectedBranch,
    isEditing,

    // Local states
    expandedCategories,
    isLoading,
    showEditor,
    isCreateMode,
    dialogState,

    // Actions
    setDialogState,
    handleSelectBranch,
    toggleExpand,
    handleToggleEdit,
    handleSelectCategory,
    handleNewCategory,
    handleSaveCategory,
    handleActiveChange,
    handleDeleteCategory,
    handleCategoriesReorder,
    reloadBranches,

    // For UI
    branchOptions,
  } = useCategoryContainer();

  const breadcrumbItems = [
    { name: 'ホーム', href: routes.home.index },
    { name: 'コンテンツ管理', href: routes.contentManagement.index },
    {
      name: 'FAQ',
      href: `${routes.contentManagement.index}${routes.contentManagement.faq.index}`,
    },
    { name: 'カテゴリ管理' },
  ];

  const handleDialogCancel = () => {
    if (dialogState?.onCancel) {
      dialogState.onCancel();
    } else {
      setDialogState(undefined);
    }
  };

  const handleDialogOk = () => {
    if (dialogState?.onOk) {
      dialogState.onOk();
    } else {
      setDialogState(undefined);
    }
  };

  return (
    <>
      <Head title="FAQカテゴリ管理" />
      <Box>
        <Breadcrumb title="FAQ" items={breadcrumbItems} />
        <Box className="flex px-6 py-4 gap-4">
          <Box
            className={
              isEditing && showEditor
                ? 'w-full transition-all'
                : 'w-full transition-all'
            }
          >
            <Box className="bg-surfaceContainer flex items-center justify-start py-2 px-4 rounded-lg gap-[10px]">
              <Box>
                <Select
                  className="!h-12 !w-[300px]"
                  fullWidth
                  value={selectedBranch?.id || ''}
                  options={branchOptions.map((b) => ({
                    key: b.key,
                    value: b.value,
                  }))}
                  onChange={(e) => handleSelectBranch(e.target.value as string)}
                  disabled={isEditing || isLoading}
                  customLabel="Branch"
                />
              </Box>
              <Button
                variant="contained"
                color="inherit"
                className="!h-12 !w-[130px] !rounded-lg"
                onClick={() => reloadBranches()}
                disabled={
                  isLoading ||
                  !selectedBranch ||
                  (branchOptions.length > 0 &&
                    selectedBranch?.id === branchOptions[0]?.value)
                }
              >
                読込
              </Button>
            </Box>

            <Box>
              <Box className="flex items-center border-b-2 py-2 mt-4 pb-4 border-onSurface">
                <Typography variant="h2">
                  {selectedBranch
                    ? `${dateFormat(selectedBranch.createdAt, DateFormat.fullDateYYYYMMDDWithDotHHmmss)}`
                    : '--'}
                </Typography>
                <Box className="ml-auto">
                  <Button
                    className={clsx(
                      '!rounded-[4px]',
                      isEditing
                        ? '!bg-secondaryContainer !text-onSecondaryContainer !hover:bg-secondaryContainer'
                        : '!bg-surfaceContainerLow !hover:bg-onSurfaceContainerLow',
                    )}
                    variant="contained"
                    color="inherit"
                    onClick={handleToggleEdit}
                    startIcon={
                      isEditing ? (
                        <CheckIcon
                          sx={{
                            fontSize: '18px',
                            marginRight: '4px',
                            color: 'inherit',
                          }}
                        />
                      ) : (
                        <></>
                      )
                    }
                    disabled={isLoading && !selectedBranch}
                  >
                    編集
                  </Button>
                </Box>
              </Box>

              <Box className="flex gap-4 pt-3 pb-6">
                <Box className="flex items-center gap-1">
                  <Typography variant="subtitle2" className="!font-bold">
                    作成日:&nbsp;
                  </Typography>
                  <Typography variant="subtitle2">
                    {selectedBranch?.createdAt
                      ? dateFormat(
                          selectedBranch.createdAt,
                          DateFormat.fullDateYYYYMMDDWithDotHHmm,
                        )
                      : '--'}
                  </Typography>
                </Box>
                <Box className="flex items-center gap-1">
                  <Typography variant="subtitle2" className="!font-bold">
                    最終更新日:&nbsp;
                  </Typography>
                  <Typography variant="subtitle2">
                    {selectedBranch?.updatedAt && !isEditing
                      ? dateFormat(
                          selectedBranch.updatedAt,
                          DateFormat.fullDateYYYYMMDDWithDotHHmm,
                        )
                      : '--'}
                  </Typography>
                </Box>
                <Box className="flex items-center gap-1">
                  <Typography variant="subtitle2" className="!font-bold">
                    更新適用日:&nbsp;
                  </Typography>
                  <Typography variant="subtitle2">
                    {selectedBranch?.publishedAt && !isEditing
                      ? dateFormat(
                          selectedBranch.publishedAt,
                          DateFormat.fullDateYYYYMMDDWithDot,
                        )
                      : '--'}
                  </Typography>
                </Box>
              </Box>

              {isLoading ? (
                <Box className="bg-white p-4 text-center border border-gray-200 rounded-md">
                  <Typography>ロード中...</Typography>
                </Box>
              ) : categories.length === 0 && !isEditing ? (
                <Box className="bg-white p-8 text-center border border-gray-200 rounded-md">
                  <Typography variant="h6" className="text-lg text-gray-600">
                    データがありません
                  </Typography>
                  <Typography
                    variant="body2"
                    className="text-sm text-gray-500 mt-2"
                  >
                    カテゴリーデータが見つかりません。別のブランチを選択するか、管理者に連絡してください。
                  </Typography>
                </Box>
              ) : (
                <CategoryList
                  categories={categories}
                  expandedCategories={expandedCategories}
                  loadingCategories={{}}
                  onToggleExpand={toggleExpand}
                  onSelect={handleSelectCategory}
                  onActiveChange={handleActiveChange}
                  isEditing={isEditing}
                  selectedCategoryId={selectedCategory?.id}
                  onAddNew={handleNewCategory}
                  onCategoriesReorder={handleCategoriesReorder}
                />
              )}
            </Box>
          </Box>

          {isEditing && showEditor && (
            <Box className="transition-all w-[350px]">
              <CategoryEditor
                category={selectedCategory}
                selectedCategoryParentId={selectedCategoryParentId}
                isCreateMode={isCreateMode}
                categories={categories}
                onSave={handleSaveCategory as any}
                onDelete={handleDeleteCategory}
              />
            </Box>
          )}
        </Box>
      </Box>
      {dialogState && (
        <CommonDialog
          open={dialogState.open}
          type={dialogState.type}
          message={dialogState.message}
          onOk={handleDialogOk}
          onCancel={handleDialogCancel}
          okText={dialogState.okText}
          cancelText={dialogState.cancelText}
        />
      )}
    </>
  );
};
