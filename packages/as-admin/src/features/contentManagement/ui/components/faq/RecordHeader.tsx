import { Box, Divider, Grid, Typo<PERSON>, <PERSON>, Button } from '@mui/material';
import { useNavigate } from 'react-router-dom';

import { Content } from '@/bundles/model';
import { AnimatedCopyButton } from '@/components/feedback';
import { DateFormat } from '@/constants';
import { dateFormat } from '@/constants/utils';
import { routes } from '@/routes/routes';

interface RecordHeaderProps {
  type: 'create' | 'edit';
  initialData?: Content;
}

export const RecordHeader: React.FC<RecordHeaderProps> = ({
  type,
  initialData,
}) => {
  const navigate = useNavigate();

  const recordId = initialData?.id || 'N/A';
  const inAppUrl = initialData?.id ? `/app/articles/${initialData.id}` : 'N/A';

  const articleTitle =
    type === 'edit'
      ? initialData?.title?.en || '記事タイトル読込中...'
      : '新規FAQ記事';

  const handleEdit = () => {
    if (initialData?.id) {
      navigate(
        `${routes.contentManagement.index}${routes.contentManagement.faq.index}${routes.contentManagement.faq.article.index}/${initialData?.id}/edit`,
      );
    }
  };

  return (
    <Box>
      <Grid container justifyContent="space-between" alignItems="center">
        <Grid item xs={12} sm>
          <Typography
            variant="h1"
            component="h1"
            sx={{
              fontWeight: 'bold',
              color: '#1A2027',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {articleTitle}
          </Typography>
        </Grid>
        <Grid
          item
          xs={12}
          sm="auto"
          container
          justifyContent={{ xs: 'flex-start', sm: 'flex-end' }}
          alignItems="center"
          spacing={1}
          sx={{ mt: { xs: 1, sm: 0 } }}
        >
          {type === 'edit' && initialData?.status === 'draft' && (
            <Grid item>
              <Chip
                label="下書き"
                size="small"
                sx={{
                  backgroundColor: '#283430',
                  color: 'white',
                  fontWeight: 'bold',
                  borderRadius: '16px',
                  height: '32px',
                  padding: '0 12px',
                }}
              />
            </Grid>
          )}
          <Grid item>
            <Button
              variant="contained"
              size="small"
              onClick={handleEdit}
              className="!bg-surfaceContainerLow !hover:bg-onSurfaceContainerLow !rounded-[4px] !text-black !min-w-20"
            >
              編集
            </Button>
          </Grid>
        </Grid>
      </Grid>

      {type === 'edit' && initialData?.id && (
        <Box
          sx={{
            mt: 1.5,
            display: 'flex',
            alignItems: 'center',
            flexWrap: 'wrap',
            gap: 2,
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
              ID:
            </Typography>
            <Typography
              variant="subtitle2"
              sx={{ ml: 0.5, fontWeight: 'medium' }}
            >
              {initialData.id}
            </Typography>
            <AnimatedCopyButton
              value={recordId}
              size="small"
              sx={{ color: 'primary.main' }}
              className="border border-primary text-primary rounded-[100%] p-1"
            />
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
              アプリ内URL:
            </Typography>
            <Typography
              variant="subtitle2"
              sx={{
                color: '#2D3748',
                ml: 0.5,
                fontWeight: 'medium',
                wordBreak: 'break-all',
              }}
            >
              {inAppUrl}
            </Typography>
            <AnimatedCopyButton
              value={inAppUrl}
              size="small"
              sx={{ ml: 0.5, color: 'primary.main' }}
              className="border-primary text-primary rounded-[100%] p-1"
            />
          </Box>
        </Box>
      )}

      <Divider className="h-[2px]" sx={{ my: 1, background: '#000' }} />

      <Box className="flex gap-4 pt-1 pb-6">
        <Box className="flex items-center gap-1">
          <Typography variant="subtitle2" className="!font-bold">
            作成日:&nbsp;
          </Typography>
          <Typography variant="subtitle2">
            {dateFormat(
              initialData?.createdAt,
              DateFormat.fullDateYYYYMMDDWithDotHHmm,
            ) ?? '--'}{' '}
          </Typography>
        </Box>
        <Box className="flex items-center gap-1">
          <Typography variant="subtitle2" className="!font-bold">
            最終更新日:&nbsp;
          </Typography>
          <Typography variant="subtitle2">
            {dateFormat(
              initialData?.updatedAt,
              DateFormat.fullDateYYYYMMDDWithDotHHmm,
            ) ?? '--'}{' '}
          </Typography>
        </Box>
        <Box className="flex items-center gap-1">
          <Typography variant="subtitle2" className="!font-bold">
            更新適用日:&nbsp;
          </Typography>
          <Typography variant="subtitle2">
            {dateFormat(
              initialData?.publishedAt,
              DateFormat.fullDateYYYYMMDDWithDot,
            ) ?? '--'}{' '}
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};
