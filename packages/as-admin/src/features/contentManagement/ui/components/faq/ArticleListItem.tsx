import { ArrowRightSharp, ArticleOutlined } from '@mui/icons-material';
import FolderIcon from '@mui/icons-material/Folder';
import { format, isValid } from 'date-fns';
import React from 'react';

import { FaqArticle } from './stores/faq-article-store';

interface ArticleListItemProps {
  article: FaqArticle;
  onViewDetails: (articleId: string) => void;
}

export const ArticleListItem: React.FC<ArticleListItemProps> = ({
  article,
  onViewDetails,
}) => {
  const parsedDate = Date.parse(article.lastUpdated);
  let displayDate = article.lastUpdated;
  if (isValid(parsedDate)) {
    displayDate = format(parsedDate, 'yyyy.MM.dd HH:mm');
  } else {
    console.error(
      `Invalid date string for article ID: ${article.id}. Value: "${article.lastUpdated}"`,
    );
    displayDate = 'Invalid Date';
  }

  return (
    <div
      className=" bg-gray-50 p-4 rounded-md border border-gray-200 cursor-pointer hover:bg-gray-100 transition-colors"
      onClick={() => onViewDetails(article.id)}
    >
      <div className="flex items-start border-b border-dashed pb-3">
        <div className="flex-shrink-0 mr-4 text-gray-500">
          <ArticleOutlined fontSize="medium" />
        </div>
        <div className="flex-grow">
          <h3 className="text-lg font-semibold text-gray-900">
            {article.title}
          </h3>
          <p className="text-xs text-gray-500">{displayDate}</p>
        </div>
        <button className="text-gray-400 hover:text-gray-600">
          {article.status === 'draft' && (
            <span className="text-primary">Draft</span>
          )}
          <ArrowRightSharp fontSize="medium" />
        </button>
      </div>
      <p className="text-sm text-gray-600 mt-3 flex items-center px-1">
        <FolderIcon
          fontSize="small"
          className={`mr-1 ${article.hasSubCategories ? 'text-blue-500' : ''}`}
        />
        / {article.categoryPath}
        {article.hasSubCategories && (
          <span className="ml-2 text-xs text-blue-500 font-medium">
            (contains sub-folders)
          </span>
        )}
      </p>
    </div>
  );
};
