import { AccountTree, UploadFile } from '@mui/icons-material';
import {
  Box,
  Typography,
  Button,
  Paper,
  Grid,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Checkbox,
  FormControlLabel,
} from '@mui/material';
import DOMPurify from 'dompurify';
import { useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import { Breadcrumb } from '@/components/elements';
import { LoadingIndicator } from '@/components/feedback/loading-indicator';
import { Head } from '@/components/seo/head';
import { routes } from '@/routes/routes';

import { useGetContentById } from '../faq/hook/use-content-management-api';

import { useFaqCategoriesBranches } from './hook/use-faq-categories-branches';
import { RecordHeader } from './RecordHeader';

// Static language list as requested
const availableLanguages = [
  { code: 'en', name: '<PERSON><PERSON>' },
  { code: 'es', name: 'ES' },
  { code: 'fr', name: 'FR' },
  { code: 'it', name: 'IT' },
  { code: 'ja', name: 'J<PERSON>' },
  { code: 'ko', name: 'KO' },
  { code: 'zhCN', name: 'zhCN' },
];

export const FaqArticleDetails = () => {
  const { articleId } = useParams<{ articleId: string }>();
  const navigate = useNavigate();

  const {
    data: article,
    isPending: isFetchingArticle,
    error: fetchError,
    mutate: fetchArticle,
  } = useGetContentById();
  const { data: faqBranches } = useFaqCategoriesBranches(undefined, {
    refetchOnWindowFocus: true,
    staleTime: 0,
  });

  useEffect(() => {
    if (articleId) {
      fetchArticle(articleId);
    }
  }, [articleId, fetchArticle]);

  const defaultLangCode = 'en';
  const [activeTabLang, setActiveTabLang] = useState<string>(defaultLangCode);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: string) => {
    setActiveTabLang(newValue);
  };

  const categoryName = useMemo(() => {
    if (!article?.parentContentId || !faqBranches?.length) {
      return '';
    }

    const findCategoryById = (categories: any[], targetId: string): any => {
      for (const category of categories) {
        if (category.id === targetId) {
          return category;
        }
        if (category.subCategories && Array.isArray(category.subCategories)) {
          const found = findCategoryById(category.subCategories, targetId);
          if (found) return found;
        }
      }
      return null;
    };

    const faqCategories = faqBranches[0]?.faqCategories || [];
    const foundCategory = findCategoryById(
      faqCategories,
      article.parentContentId,
    );

    return (
      foundCategory?.displayName?.['en'] ||
      foundCategory?.displayName?.['ja'] ||
      ''
    );
  }, [article, faqBranches]);

  const handleBackToList = () => {
    navigate(
      `${routes.contentManagement.index}${routes.contentManagement.faq.index}${routes.contentManagement.faq.article.index}`,
    );
  };

  const breadcrumbItems = [
    { name: 'ホーム', href: routes.home.index },
    { name: 'コンテンツ管理', href: routes.contentManagement.index },
    {
      name: 'FAQ',
      href: `${routes.contentManagement.index}${routes.contentManagement.faq.index}`,
    },
    {
      name: '記事一覧',
      href: `${routes.contentManagement.index}${routes.contentManagement.faq.index}${routes.contentManagement.faq.article.index}`,
    },
    { name: '記事' },
  ];

  if (isFetchingArticle) {
    return <LoadingIndicator isLoading={true} fullscreen />;
  }

  if (fetchError || !article) {
    return (
      <Box className="p-6 text-center text-red-600">
        <Typography>記事の読み込み中にエラーが発生しました。</Typography>
        <Button onClick={handleBackToList} variant="contained" className="mt-4">
          一覧に戻る
        </Button>
      </Box>
    );
  }

  const title = article.title?.['en'] || 'No Title';
  const contentHtml = article.content?.[activeTabLang]?.body || '';
  const cleanHtml = DOMPurify.sanitize(contentHtml, {
    USE_PROFILES: { html: true },
  });
  const attachments = article.content?.[activeTabLang]?.attachments || [];
  const isPublic = article.content?.[activeTabLang]?.active ?? false;

  return (
    <>
      <Head title={`FAQ詳細: ${article.title?.en || ''}`} />
      <Box>
        <Breadcrumb title="FAQ" items={breadcrumbItems} />

        <Box className="flex px-6 py-4 flex-col">
          <RecordHeader type="edit" initialData={article} />

          <Grid
            container
            alignItems="flex-end"
            className="mb-4 bg-[#0000000D] p-3 rounded-lg"
          >
            <Grid item xs={12} sm={7} className="pl-3">
              <Typography
                variant="subtitle2"
                sx={{ color: '#000000DE', fontWeight: 'bold', mb: 1 }}
              >
                カテゴリ
              </Typography>
              <Box className="flex items-center">
                <AccountTree sx={{ fontSize: 16, mr: 1 }} />
                <Typography variant="body2">{categoryName}</Typography>
              </Box>
            </Grid>
          </Grid>

          <Box
            className="mb-0 rounded-t-lg overflow-hidden"
            sx={{ border: '1px solid #D2DBDA', borderBottom: 0 }}
          >
            <Tabs
              value={activeTabLang}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
              className="bg-[#0000000D]"
              sx={{
                '& .MuiTabs-flexContainer': {
                  justifyContent: 'center',
                },
                '& .MuiTab-root': {
                  minWidth: 60,
                  maxWidth: 30,
                  padding: '8px 16px',
                },
              }}
            >
              {availableLanguages.map((langItem) => {
                const isPublished = article.content?.[langItem.code]?.active;
                return (
                  <Tab
                    key={langItem.code}
                    value={langItem.code}
                    label={
                      <Box
                        sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
                      >
                        {langItem.name}
                        {isPublished && (
                          <Box
                            sx={{
                              width: 8,
                              height: 8,
                              borderRadius: '50%',
                              backgroundColor: 'error.main',
                            }}
                          />
                        )}
                      </Box>
                    }
                  />
                );
              })}
            </Tabs>
          </Box>

          <Paper
            elevation={0}
            sx={{ p: 2, border: '1px solid #e0e0e0', borderTop: 0 }}
            className="flex w-full gap-3"
          >
            <Box className="w-1/4 border-r border-dotted">
              <FormControlLabel
                control={<Checkbox checked={isPublic} disabled />}
                className="!text-black"
                sx={{
                  '& .MuiFormControlLabel-label': {
                    fontSize: '14px',
                  },
                }}
                label="公開"
              />
              <Typography
                variant="subtitle2"
                sx={{ mt: 2, fontWeight: 'bold' }}
              >
                記事タイトル
              </Typography>
              <Typography variant="body2" sx={{ mb: 3 }}>
                {title}
              </Typography>

              <Typography
                variant="subtitle2"
                sx={{ fontWeight: 'bold', mb: 1, mt: 3 }}
              >
                添付ファイル
              </Typography>
              {attachments.length > 0 ? (
                <List dense>
                  {attachments.map((file, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        <UploadFile />
                      </ListItemIcon>
                      <ListItemText primary={file} />
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  添付ファイルはありません。
                </Typography>
              )}
            </Box>

            <Box className="w-3/4">
              <Box
                className="prose max-w-none mb-4"
                dangerouslySetInnerHTML={{ __html: cleanHtml }}
                sx={{
                  p: 2,
                  minHeight: '150px',
                  color: '#000000DE',
                }}
              />
            </Box>
          </Paper>
        </Box>
      </Box>
    </>
  );
};
