import CheckCircleOutlineOutlinedIcon from '@mui/icons-material/CheckCircleOutlineOutlined';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import RadioButtonUncheckedOutlinedIcon from '@mui/icons-material/RadioButtonUncheckedOutlined';
import {
  Box,
  Checkbox,
  FormControlLabel,
  InputAdornment,
  TextField,
  Typography,
} from '@mui/material';
import clsx from 'clsx';
import { useEffect, useState, useMemo } from 'react';

import { FaqCategory } from '@/bundles/model';
import { Button } from '@/components/forms';
import { Select } from '@/components/select';
import { DisplayName } from '@/features/contentManagement/domain/faq/category/types';
import { getCategoryDisplayName } from '@/features/contentManagement/domain/faq/category/utils';
import { CloseIcon } from '@/features/userManagement/ui/components';

interface Language {
  id: string;
  name: string;
  code: string;
  checked: boolean;
  expanded?: boolean;
  value: string;
  originalValue?: string;
}

interface CategoryEditorProps {
  category: FaqCategory | null;
  selectedCategoryParentId: string | null;
  isCreateMode: boolean;
  categories: FaqCategory[];
  onSave: (
    updatedCategory: Partial<FaqCategory>,
    newParentId: string | null,
  ) => void;
  onDelete?: () => void;
}

export const CategoryEditor = ({
  category,
  selectedCategoryParentId,
  isCreateMode,
  categories,
  onSave,
  onDelete,
}: CategoryEditorProps) => {
  const [languages, setLanguages] = useState<Language[]>([]);
  const [selectedParentCategory, setSelectedParentCategory] = useState('');
  const [showChildCategory, setShowChildCategory] = useState(false);

  const isEnglishRequired = useMemo(() => {
    const englishLang = languages.find((lang) => lang.code === 'en');
    return !englishLang?.value?.trim();
  }, [languages]);

  const hasChanges = useMemo(() => {
    // Check if any language values have changed
    const languageChanges = languages.some(
      (lang) => lang.value !== (lang.originalValue || ''),
    );

    // Check if parent category selection has changed
    const parentCategoryChanged =
      selectedParentCategory !== (selectedCategoryParentId || '');
    const checkboxChanged = showChildCategory !== !!selectedCategoryParentId;

    return languageChanges || parentCategoryChanged || checkboxChanged;
  }, [
    languages,
    selectedParentCategory,
    selectedCategoryParentId,
    showChildCategory,
  ]);

  useEffect(() => {
    const defaultLanguages: Omit<Language, 'value' | 'originalValue'>[] = [
      { id: '1', name: 'English', code: 'en', checked: true, expanded: false },
      { id: '2', name: 'Español', code: 'es', checked: true, expanded: false },
      { id: '3', name: 'Français', code: 'fr', checked: true, expanded: false },
      { id: '4', name: 'Italiano', code: 'it', checked: true, expanded: false },
      { id: '5', name: '日本語', code: 'ja', checked: true, expanded: false },
      { id: '6', name: '한국어', code: 'ko', checked: true, expanded: false },
      { id: '7', name: '简体中文', code: 'ch', checked: true, expanded: false },
    ];

    if (category) {
      const updatedLanguages = defaultLanguages.map((lang) => {
        const value = category.displayName?.[lang.code] || '';
        return {
          ...lang,
          value,
          originalValue: value,
          checked: lang.code === 'en' ? true : !!value, // English is always considered checked for editing
        };
      });
      setLanguages(updatedLanguages);
      setSelectedParentCategory(selectedCategoryParentId || '');
    } else {
      setLanguages(
        defaultLanguages.map((l) => ({
          ...l,
          value: '',
          originalValue: '',
          checked: l.code === 'en', // Only English is checked by default for creation
        })),
      );
      setSelectedParentCategory('');
    }

    setShowChildCategory(!!selectedCategoryParentId);
  }, [category, selectedCategoryParentId]);

  const handleLanguageChange = (id: string, value: string) => {
    setLanguages(
      languages.map((lang) => {
        if (lang.id === id) {
          const shouldBeChecked = lang.code === 'en' || value.trim() !== '';
          return { ...lang, value, checked: shouldBeChecked };
        }
        return lang;
      }),
    );
  };

  // const handleLanguageSelect = (id: string) => {
  //   setLanguages(
  //     languages.map((lang) =>
  //       lang.id === id && lang.code !== 'en' // Prevent unchecking English
  //         ? { ...lang, checked: !lang.checked }
  //         : lang,
  //     ),
  //   );
  // };

  const handleSubmit = () => {
    if (isEnglishRequired) return;

    const displayName: DisplayName = {};
    for (const lang of languages) {
      if (lang.checked && lang.value) {
        displayName[lang.code] = lang.value;
      }
    }
    const updatedCategoryData: Partial<FaqCategory> = {
      id: category?.id,
      displayName,
    };
    const newParentId = showChildCategory ? selectedParentCategory : null;
    onSave(updatedCategoryData, newParentId);
  };

  const allCategoriesFlat = (cats: FaqCategory[]): FaqCategory[] => {
    let flat: FaqCategory[] = [];
    for (const cat of cats) {
      flat.push(cat);
      if (cat.subCategories) {
        flat = [...flat, ...allCategoriesFlat(cat.subCategories)];
      }
    }
    return flat;
  };

  return (
    <Box className="bg-white border sticky top-16 right-1 border-gray-200 rounded-lg px-4 py-6 flex flex-col max-h-[80vh] w-[288px] overflow-auto">
      <Box className="flex-grow">
        <Box className="mb-6">
          {languages.map((lang) => (
            <Box key={lang.id}>
              <Box
                className="flex items-center p-2 rounded hover:bg-[#E0E0E0] border-b border-gray-200 cursor-pointer"
                onClick={() =>
                  setLanguages(
                    languages.map((l) =>
                      l.id === lang.id ? { ...l, expanded: !l.expanded } : l,
                    ),
                  )
                }
              >
                <Box className="flex items-center mr-2">
                  {lang.checked ? (
                    <CheckCircleOutlineOutlinedIcon fontSize="small" />
                  ) : (
                    <RadioButtonUncheckedOutlinedIcon fontSize="small" />
                  )}
                </Box>
                <Typography className="mr-2 !text-sm">
                  {lang.name}
                  {lang.code === 'en' && (
                    <span className="text-red-500 ml-1">*</span>
                  )}
                </Typography>
                <Box className="flex-grow" />
                <Box className="flex items-center cursor-pointer">
                  {lang.expanded ? (
                    <ExpandLessIcon fontSize="small" />
                  ) : (
                    <ExpandMoreIcon fontSize="small" />
                  )}
                </Box>
              </Box>
              {lang.expanded && (
                <Box className="my-2">
                  <TextField
                    fullWidth
                    multiline
                    minRows={2}
                    value={lang.value}
                    onChange={(e) =>
                      handleLanguageChange(lang.id, e.target.value)
                    }
                    placeholder={`入力してください`}
                    InputProps={{
                      endAdornment: lang.value && (
                        <InputAdornment position="start">
                          <CloseIcon
                            onClick={() => handleLanguageChange(lang.id, '')}
                            className="cursor-pointer mr-2 !fill-onSurface"
                          />
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      '& .MuiInputBase-root': {
                        alignItems: 'flex-start',
                        resize: 'vertical',
                        overflow: 'auto',
                        fontSize: '14px',
                      },
                    }}
                  />
                </Box>
              )}
            </Box>
          ))}
        </Box>

        <FormControlLabel
          control={
            <Checkbox
              checked={showChildCategory}
              onChange={(e) => {
                const isChecked = e.target.checked;
                setShowChildCategory(isChecked);
                if (!isChecked) {
                  setSelectedParentCategory('');
                }
              }}
              size="small"
            />
          }
          label="次のカテゴリの下位にネスト"
          className="pl-3"
          sx={{
            '& .MuiFormControlLabel-label': {
              fontSize: '14px',
              fontWeight: 'bold',
            },
          }}
        />

        <Box className="mt-3 mb-6">
          <Select
            fullWidth
            value={selectedParentCategory}
            onChange={(e) => {
              const newValue = e.target.value as string;
              setSelectedParentCategory(newValue);
              // Uncheck if no value is selected
              if (!newValue) {
                setShowChildCategory(false);
              }
            }}
            onFocus={() => {
              if (!showChildCategory) {
                setShowChildCategory(true);
              }
            }}
            disabled={!showChildCategory}
            options={allCategoriesFlat(categories)
              .filter((c) => c.id !== category?.id)
              .map((cat) => ({
                key: getCategoryDisplayName(cat as any),
                value: cat.id ?? '',
              }))}
          />
        </Box>
      </Box>

      <Box className="mt-auto pt-4 border-t">
        <Button
          variant="contained"
          size="large"
          color="primary"
          fullWidth
          onClick={handleSubmit}
          disabled={isEnglishRequired || (!isCreateMode && !hasChanges)}
          className="!h-12 !w-full"
        >
          {isCreateMode ? '作成' : '更新'}
        </Button>
        <div className="mt-3"></div>
        {!isCreateMode && (
          <Button
            variant="text"
            fullWidth
            startIcon={
              <DeleteOutlineIcon
                className={clsx(
                  '!text-lg',
                  !!(
                    category?.subCategories && category.subCategories.length > 0
                  ) || !!(category && category.count && category.count > 0)
                    ? '!text-onSurface'
                    : '!text-red-500',
                )}
              />
            }
            onClick={onDelete}
            className={clsx(
              '!text-sm',
              !!(
                category?.subCategories && category.subCategories.length > 0
              ) || !!(category && category.count && category.count > 0)
                ? '!text-onSurface !opacity-30'
                : '!text-red-500',
            )}
            disabled={
              !!(
                category?.subCategories && category.subCategories.length > 0
              ) || !!(category && category.count && category.count > 0)
            }
          >
            削除
          </Button>
        )}
      </Box>
    </Box>
  );
};
