import { Typo<PERSON>, Box, Button } from '@mui/material';
import { format } from 'date-fns';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';

import { Content } from '@/bundles/model';
import { Breadcrumb } from '@/components/elements';
import { LoadingIndicator } from '@/components/feedback/loading-indicator';
import { Head } from '@/components/seo/head';
import { routes } from '@/routes/routes';

import {
  useGetContentById,
  useUpdateContent,
  useDeleteContent,
} from '../faq/hook/use-content-management-api';

import { FaqArticleForm, FaqArticleFormValues } from './FaqArticleForm';

export const FaqArticleEdit = () => {
  const { articleId } = useParams<{ articleId: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();

  const {
    mutateAsync: fetchArticle,
    data: article,
    isPending: isFetching,
    error,
  } = useGetContentById();
  const updateContentMutation = useUpdateContent();
  const deleteContentMutation = useDeleteContent();

  useEffect(() => {
    if (articleId) {
      fetchArticle(articleId);
    }
  }, [articleId, fetchArticle]);

  const onSubmitForm = async (formValues: FaqArticleFormValues) => {
    if (!articleId) {
      toast.error('Article ID is missing for update.');
      return;
    }

    const validStatus: Content['status'] =
      formValues.status === 'draft' || formValues.status === 'published'
        ? formValues.status
        : undefined;

    const payload: Content = {
      ...formValues,
      id: articleId,
      title: {},
      content: {},
      status: validStatus,
    };

    if (!payload.title) {
      payload.title = {};
    }

    for (const langCode in formValues.title) {
      if (formValues.title[langCode]) {
        payload.title[langCode] = formValues.title[langCode]!;
      }
    }

    if (!payload.content) {
      payload.content = {};
    }

    for (const langCode in formValues.content) {
      const formContent = formValues.content[langCode];
      if (formContent?.body) {
        payload.content[langCode] = {
          active: formContent.active ?? false,
          body: formContent.body,
          title: formValues.title[langCode] || '',
          url: '',
          attachments: [],
        };
      }
    }

    try {
      if (payload.publishedAt) {
        payload.publishedAt = format(payload.publishedAt, 'yyyy-MM-dd');
      }
      await updateContentMutation.mutateAsync({
        id: articleId,
        content: payload,
      });
      navigate(
        `${routes.contentManagement.index}${routes.contentManagement.faq.index}${routes.contentManagement.faq.article.index}/${articleId}`,
      );
    } catch (err) {
      toast.error(t('common.toast.updateFailed', { name: 'FAQ記事' }));
    }
  };

  const handleDelete = async () => {
    if (!articleId) return;
    try {
      await deleteContentMutation.mutateAsync(articleId);
      navigate(
        `${routes.contentManagement.index}${routes.contentManagement.faq.index}${routes.contentManagement.faq.article.index}`,
      );
    } catch (err) {
      console.error(err);
    }
  };

  const handleCancel = () => {
    navigate(-1); // Go back to the previous page
  };

  if (isFetching || updateContentMutation.isPending) {
    return <LoadingIndicator isLoading={true} fullscreen />;
  }

  if (error || !article) {
    return (
      <Box className="p-6 text-center text-red-600">
        <Typography>記事の読み込みに失敗しました。</Typography>
        <Button onClick={handleCancel} variant="contained" className="mt-4">
          戻る
        </Button>
      </Box>
    );
  }

  const breadcrumbItems = [
    { name: 'ホーム', href: routes.home.index },
    { name: 'コンテンツ管理', href: routes.contentManagement.index },
    { name: 'FAQ', href: routes.contentManagement.faq.index },
    {
      name: '記事一覧',
      href: `${routes.contentManagement.index}${routes.contentManagement.faq.index}${routes.contentManagement.faq.article.index}`,
    },
    { name: '記事編集' },
  ];

  return (
    <>
      <Head title={`FAQ記事編集: ${article.title?.en || ''}`} />
      <Box>
        <Breadcrumb title="FAQ" items={breadcrumbItems} />
      </Box>
      <FaqArticleForm
        type="edit"
        initialData={article}
        onSubmit={onSubmitForm}
        onDelete={handleDelete}
      />
    </>
  );
};
