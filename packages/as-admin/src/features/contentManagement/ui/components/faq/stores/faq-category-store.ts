import { create } from 'zustand';

import { FaqCategoriesBranch, FaqCategory } from '@/bundles/model';

type FaqCategoryStore = {
  // Data
  branches: FaqCategoriesBranch[];
  selectedBranch: FaqCategoriesBranch | null;
  categories: FaqCategory[];
  selectedCategory: FaqCategory | null;
  selectedCategoryParentId: string | null;

  // UI States
  isLoading: boolean;
  isEditing: boolean;
  showEditor: boolean;
  isCreateMode: boolean;
  showApplicationDate: boolean;
  dialogState?: any;
  applicationDate: Date | null;
  expandedCategories: string[];

  // Actions
  setBranches: (branches: FaqCategoriesBranch[]) => void;
  setSelectedBranch: (branchId: string | null) => void;
  setCategories: (categories: FaqCategory[]) => void;
  setSelectedCategory: (
    category: FaqCategory | null,
    parentId?: string | null,
  ) => void;
  setLoading: (isLoading: boolean) => void;
  toggleIsEditing: () => void;
  setShowEditor: (show: boolean) => void;
  setCreateMode: (isCreate: boolean) => void;
  setShowApplicationDate: (show: boolean) => void;
  setDialogState: (dialogState?: any) => void;
  setApplicationDate: (date: Date | null) => void;
  toggleExpandCategory: (categoryId: string) => void;
  updateSelectedBranchCategories: (newFaqCategories: FaqCategory[]) => void;
  clearState: () => void;
};

const initialState = {
  branches: [],
  selectedBranch: null,
  categories: [],
  selectedCategory: null,
  selectedCategoryParentId: null,
  isLoading: false,
  isEditing: false,
  showEditor: false,
  isCreateMode: false,
  showApplicationDate: false,
  dialogState: undefined,
  applicationDate: null,
  expandedCategories: [],
};

export const useFaqCategoryStore = create<FaqCategoryStore>((set, get) => ({
  ...initialState,

  setBranches: (branches) => set({ branches }),
  setSelectedBranch: (branchId) => {
    const branch = get().branches.find((b) => b.id === branchId) || null;
    set({
      selectedBranch: branch,
      categories: (branch?.faqCategories as FaqCategory[]) || [],
      selectedCategory: null,
      selectedCategoryParentId: null,
    });
  },
  setCategories: (categories) =>
    set({ categories: categories as FaqCategory[] }),
  setSelectedCategory: (category, parentId = null) =>
    set({
      selectedCategory: category as FaqCategory | null,
      selectedCategoryParentId: parentId,
    }),
  setLoading: (isLoading) => set({ isLoading }),
  toggleIsEditing: () => set((state) => ({ isEditing: !state.isEditing })),
  setShowEditor: (show) => set({ showEditor: show }),
  setCreateMode: (isCreate) => set({ isCreateMode: isCreate }),
  setShowApplicationDate: (show) => set({ showApplicationDate: show }),
  setDialogState: (dialogState) => set({ dialogState }),
  setApplicationDate: (date) => set({ applicationDate: date }),
  toggleExpandCategory: (categoryId: string) =>
    set((state) => {
      const expandedCategories = state.expandedCategories.includes(categoryId)
        ? state.expandedCategories.filter((id) => id !== categoryId)
        : [...state.expandedCategories, categoryId];
      return { expandedCategories };
    }),

  updateSelectedBranchCategories: (newFaqCategories: FaqCategory[]) => {
    set((state) => {
      let updatedBranch: FaqCategoriesBranch;
      let updatedBranches: FaqCategoriesBranch[];

      if (state.selectedBranch) {
        // Logic to update an existing branch
        updatedBranch = {
          ...state.selectedBranch,
          faqCategories: newFaqCategories,
        };
        updatedBranches = state.branches.map((branch) =>
          branch.id === updatedBranch.id ? updatedBranch : branch,
        );
      } else {
        // **FIX**: Logic to create a new temporary branch if none exists
        updatedBranch = {
          id: `new-branch-${Date.now()}`,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          status: 'draft',
          faqCategories: newFaqCategories,
        };
        updatedBranches = [...state.branches, updatedBranch];
      }

      return {
        selectedBranch: updatedBranch,
        branches: updatedBranches,
        categories: newFaqCategories,
      };
    });
  },
  clearState: () => set(initialState),
}));
