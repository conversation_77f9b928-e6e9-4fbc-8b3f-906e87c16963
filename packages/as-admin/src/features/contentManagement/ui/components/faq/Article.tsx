import { Box } from '@mui/material';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

import { Breadcrumb } from '@/components/elements';
import { Head } from '@/components/seo/head';
import { routes } from '@/routes/routes';

import { useGetContents } from '../faq/hook/use-content-management-api';

import { ArticleHeader } from './ArticleHeader';
import { ArticleListItem } from './ArticleListItem';
import { ArticlePagination } from './ArticlePagination';
import { useFaqCategoriesBranches } from './hook/use-faq-categories-branches';
import { useFaqArticleStore } from './stores/faq-article-store';

export const ContentManagementFaqArticle = () => {
  const navigate = useNavigate();

  const {
    articles,
    isLoading,
    error,
    currentPage,
    totalPages,
    fetchArticles,
    setCurrentPage,
  } = useFaqArticleStore();

  const { mutateAsync: getContentsMutate, isPending: isFetchingList } =
    useGetContents();

  const { data: faqBranches } = useFaqCategoriesBranches(undefined, {
    refetchOnWindowFocus: true,
    staleTime: 0,
  });

  useEffect(() => {
    const faqCategories = faqBranches?.[0]?.faqCategories || [];
    fetchArticles(getContentsMutate, faqCategories);
  }, [currentPage, fetchArticles, getContentsMutate, faqBranches]);

  const handleAddNewArticle = () => {
    navigate(
      `${routes.contentManagement.index}${routes.contentManagement.faq.index}${routes.contentManagement.faq.article.index}/create`,
    );
  };

  const handleViewArticleDetails = (articleId: string) => {
    navigate(
      `${routes.contentManagement.index}${routes.contentManagement.faq.index}${routes.contentManagement.faq.article.index}/${articleId}`,
    );
  };

  if (isLoading || isFetchingList) {
    return (
      <div className="flex justify-center items-center h-screen bg-gray-100">
        <p className="text-2xl text-gray-700">Loading articles...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-screen bg-gray-100">
        <p className="text-2xl text-red-600">Error: {error}</p>
      </div>
    );
  }

  const breadcrumbItems = [
    { name: 'ホーム', href: routes.home.index },
    { name: 'コンテンツ管理', href: routes.contentManagement.index },
    {
      name: 'FAQ',
      href: `${routes.contentManagement.index}${routes.contentManagement.faq.index}`,
    },
    { name: '記事一覧' },
  ];

  return (
    <>
      <Head title="FAQ記事一覧" />
      <Box>
        <Breadcrumb title="FAQ" items={breadcrumbItems} />
      </Box>
      <Box className="mx-auto p-6 bg-white shadow-md">
        <ArticleHeader onAddNewArticle={handleAddNewArticle} />

        <div className="space-y-4">
          {articles.length > 0 ? (
            articles.map((article) => (
              <ArticleListItem
                key={article.id}
                article={article}
                onViewDetails={handleViewArticleDetails}
              />
            ))
          ) : (
            <p className="text-center text-gray-600 py-8">No articles found.</p>
          )}
        </div>

        <ArticlePagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setCurrentPage}
        />
      </Box>
    </>
  );
};
