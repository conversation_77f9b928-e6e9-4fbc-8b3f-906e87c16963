import { zodResolver } from '@hookform/resolvers/zod';
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  InputLabel,
  TextareaAutosize,
  Theme,
  Typography,
} from '@mui/material';
import { createStyles, makeStyles } from '@mui/styles';
import clsx from 'clsx';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import { Content } from '@/bundles/model';
import { Button } from '@/components/forms';
import { InputText } from '@/components/input-text';
import { Item, Select } from '@/components/select';
import { useMenuContent } from '@/features/cms/ui/stores/menuContentStore';
import i18n from '@/lib/i18n/config';
import { TitleTypeEnum } from '@/types';

const useStyles = makeStyles((theme: Theme) =>
  createStyles({
    header: {
      background: theme.palette.primary.main,
      position: 'relative',
      '&__title': {
        color: '#FFFFFF',
        fontSize: '18px',
        fontWeight: 'bold',
        textAlign: 'center',
      },
      '&__close': {
        position: 'absolute',
        top: '50%',
        right: '12px',
        transform: 'translateY(-50%)',
        color: '#fff',
      },
    },
    faqForm: {
      width: '100%',
      margin: 0,
      marginTop: '16px',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'flex-start',
      justifyContent: 'center',
      padding: 0,
      boxSizing: 'border-box',
      '& label': {
        color: '#000',
        fontWeight: 'bold',
      },
      '& > .MuiGrid-item': {
        width: '100%',
      },
      '&__textarea': {
        width: '100%',
        padding: '8px',
        border: '1px solid #ddd',
        resize: 'vertical',
        fontSize: '16px',
        '&--error': {
          border: '1px solid red',
        },
      },
      '&__textarea:focus': {
        border: '1px solid transparent',
        outline: `2px solid ${theme.palette.primary.main}`,
      },
    },
    asterisk: {
      color: 'red',
      '& .MuiFormLabel-asterisk': {
        color: 'red',
      },
    },
    btnCancel: {
      minWidth: '90px',
      borderColor: theme.palette.primary.main,
      paddingLeft: 0,
      paddingRight: 0,
      marginRight: '8px',
    },
    btnSubmit: {
      minWidth: '90px',
      background: theme.palette.primary.main,
    },
    borderTop: {
      borderTop: '1px solid #ddd',
    },
  }),
);

const titleByType = {
  create: 'FAQ記事追加',
  update: 'FAQ記事編集',
};

const faqSchema = z.object({
  id: z.optional(z.string().or(z.number())),
  title: z.string().min(1, 'Title is required'),
  content: z.optional(z.any()),
  lang: z.string().min(1, 'Lang is required'),
});

type Props = {
  open: boolean;
  onClose: () => void;
  onSubmit: (formValues: Content) => void;
  data?: Content;
  type: TitleTypeEnum;
  optionLanguages: Item[];
};

export const FaqDialog: React.FC<Props> = ({
  open,
  onClose,
  onSubmit,
  data,
  type,
  optionLanguages,
}: Props) => {
  const classes = useStyles();
  const { t } = useTranslation();

  const { handleSubmit, register, formState, reset } = useForm<Content>({
    defaultValues: {
      ...data,
    },
    resolver: zodResolver(faqSchema),
    mode: 'all',
  });

  const handleSubmitForm: SubmitHandler<Content> = (dataContent) => {
    dataContent.contentCategoryCode = 'faq';
    onSubmit(dataContent);
    onClose();
  };

  const handleClose = () => {
    reset({
      // title: '',
    });
    onClose();
  };
  const formTitle = titleByType[type];

  const menuContent = useMenuContent();

  const contentType = menuContent.find(
    (item) => item.code === 'faq',
  )?.contentType;

  return (
    <Dialog
      open={open}
      fullWidth
      PaperProps={{
        component: 'form',
        sx: { borderRadius: 0 },
        onSubmit: handleSubmit(handleSubmitForm),
        noValidate: true,
      }}
    >
      <DialogTitle className={`${classes.header}`}>
        <Typography className={`${classes.header}__title`}>
          {formTitle}
        </Typography>
      </DialogTitle>
      <DialogContent>
        <Grid container spacing={2} className={classes.faqForm}>
          <Grid item>
            <InputText
              id="title"
              label={'タイトル'}
              placeholder={'タイトル'}
              fullWidth
              required
              error={!!formState.errors.title}
              // TODO: fix type
              // helperText={t(formState.errors.title?.message ?? '')}
              registrationForm={register('title')}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                }
              }}
            />
          </Grid>
          <Grid item>
            <Select
              id="lang"
              label={'言語'}
              placeholder={'言語'}
              fullWidth
              required
              // TODO: fix type
              // value={watch('lang')}
              // error={!!formState.errors.lang}
              // helperText={t(formState.errors.lang?.message ?? '')}
              // onChange={(e) => {
              //   setValue('lang', e.target.value as string);
              // }}
              options={optionLanguages}
            />
          </Grid>
          {contentType &&
            contentType.fields &&
            contentType.fields.map((field) => {
              if (field.type === 'text') {
                return (
                  <Grid item key={field.code}>
                    <InputText
                      id={field.code}
                      label={field.displayName?.[i18n.language]}
                      placeholder={field.displayName?.[i18n.language]}
                      fullWidth
                      required={field.required}
                      error={!!formState.errors.content?.[field.code ?? '']}
                      // TODO: fix type
                      // helperText={t(
                      //   formState.errors.content?.[field.code ?? '']?.message ??
                      //     '',
                      // )}
                      registrationForm={register(`content.${field.code ?? ''}`)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                        }
                      }}
                    />
                  </Grid>
                );
              }

              if (field.type === 'richtext') {
                return (
                  <Grid item key={field.code}>
                    <InputLabel
                      required={field.required}
                      htmlFor={field.code}
                      className={classes.asterisk}
                    >
                      {field.displayName?.[i18n.language] + ':'}
                    </InputLabel>
                    <TextareaAutosize
                      id={field.code}
                      aria-label="minimum height"
                      minRows={5}
                      placeholder={field.displayName?.[i18n.language]}
                      {...register(`content.${field.code ?? ''}`)}
                      className={clsx(`${classes.faqForm}__textarea`, {
                        [`${classes.faqForm}__textarea--error`]:
                          !!formState.errors.content?.[field.code ?? ''],
                      })}
                    />
                    {/* TODO: fix type */}
                    {/* {formState.errors.content?.[field.code ?? ''] && (
                      <Typography color="error" variant="caption">
                        {t(
                          formState.errors.content?.[field.code ?? '']
                            ?.message ?? '',
                        )}
                      </Typography>
                    )} */}
                  </Grid>
                );
              }

              return null;
            })}
        </Grid>
      </DialogContent>
      <DialogActions className={classes.borderTop}>
        <Button
          variant="outlined"
          size="large"
          className={classes.btnCancel}
          onClick={handleClose}
        >
          {t('common.button.cancel')}
        </Button>
        <Button
          variant="contained"
          size="large"
          className={classes.btnSubmit}
          type="submit"
          disabled={Object.keys(formState.errors).length > 0}
        >
          {t('common.button.submit')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
