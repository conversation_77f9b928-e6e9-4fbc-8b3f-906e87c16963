import { useTranslation } from 'react-i18next';

import { TermsType } from '@/bundles/model';
import { routes } from '@/routes/routes';

import { GenericTermPage } from './common/GenericTermPage';

export const ContentManagementTermsPayment = () => {
  const { t } = useTranslation();
  const type = TermsType.PaymentTerms;

  const breadcrumbItems = [
    { name: t('menu.home'), href: routes.home.index },
    { name: t('menu.contentManagement'), href: routes.contentManagement.index },
    {
      name: t('terms.title', '利用規約'),
      href: `${routes.contentManagement.index}${routes.contentManagement.terms.index}`,
    },
    { name: t('terms.payment.title', '決済規約') },
  ];

  return (
    <GenericTermPage
      pageTitleKey="terms.payment.title"
      pageTitleDefault="決済規約"
      breadcrumbItemsConfig={breadcrumbItems}
      termsType={type}
    />
  );
};
