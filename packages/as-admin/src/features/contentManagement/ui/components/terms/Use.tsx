import { useTranslation } from 'react-i18next';

import { TermsType } from '@/bundles/model';
import { routes } from '@/routes/routes';

import { GenericTermPage } from './common/GenericTermPage';

export const ContentManagementTermsUse = () => {
  const { t } = useTranslation();
  const type = TermsType.TermsOfUseForGeneral;

  const breadcrumbItems = [
    { name: t('menu.home'), href: routes.home.index },
    { name: t('menu.contentManagement'), href: routes.contentManagement.index },
    {
      name: t('terms.title', '利用規約'),
      href: `${routes.contentManagement.index}${routes.contentManagement.terms.index}`,
    },
    { name: t('terms.use.title', 'ご利用規約') },
  ];

  return (
    <GenericTermPage
      pageTitleKey="terms.use.title"
      pageTitleDefault="ご利用規約"
      breadcrumbItemsConfig={breadcrumbItems}
      termsType={type}
    />
  );
};
