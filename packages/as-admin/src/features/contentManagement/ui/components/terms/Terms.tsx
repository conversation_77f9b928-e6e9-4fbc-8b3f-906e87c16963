import Typography from '@mui/material/Typography';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { Breadcrumb } from '@/components/elements';
import { Button } from '@/components/forms/button';
import { Head } from '@/components/seo/head';
import { routes } from '@/routes/routes';

export const ContentManagementTerms = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const router = routes.contentManagement.index;

  const breadcrumbItems = [
    { name: t('menu.home'), href: routes.home.index },
    { name: t('menu.contentManagement'), href: routes.contentManagement.index },
    { name: t('terms.title', 'ご利用規約') },
  ];

  const buttonItems = [
    {
      title: 'ご利用規約',
      onClick: () =>
        navigate(
          `${router}${routes.contentManagement.terms.index}${routes.contentManagement.terms.guidelines.index}`,
        ),
    },
    {
      title: 'プライバシーポリシー',
      onClick: () =>
        navigate(
          `${router}${routes.contentManagement.terms.index}${routes.contentManagement.terms.policy.index}`,
        ),
    },
    {
      title: 'ご利用規約',
      onClick: () =>
        navigate(
          `${router}${routes.contentManagement.terms.index}${routes.contentManagement.terms.use.index}`,
        ),
    },
    {
      title: '決済規約',
      onClick: () =>
        navigate(
          `${router}${routes.contentManagement.terms.index}${routes.contentManagement.terms.payment.index}`,
        ),
    },
    {
      title: '購入者保護ポリシー',
      onClick: () =>
        navigate(
          `${router}${routes.contentManagement.terms.index}${routes.contentManagement.terms.buyer.index}`,
        ),
    },
    {
      title: '特定商取引法に関する表記',
      onClick: () =>
        navigate(
          `${router}${routes.contentManagement.terms.index}${routes.contentManagement.terms.description.index}`,
        ),
    },
  ];

  return (
    <>
      <Head title="ご利用規約" />
      <Breadcrumb title="ご利用規約" items={breadcrumbItems} />
      <div className="p-4">
        <div className="grid grid-cols-3 gap-4">
          {buttonItems.map((item, index) => (
            <Button
              key={`item-${index}-title`}
              onClick={item.onClick}
              className="p-6 bg-primary text-center hover:opacity-90 transition-opacity !w-full !h-12"
            >
              <Typography variant="h3" className="!text-onPrimary">
                {item.title}
              </Typography>
            </Button>
          ))}
        </div>
        {/* <div className="grid grid-cols-1 pt-4">
          <Button
            onClick={() =>
              navigate(
                `${router}${routes.contentManagement.terms.index}${routes.contentManagement.terms.add.index}`,
              )
            }
            className="p-6 border border-primary text-center hover:opacity-90 transition-opacity !w-full !h-12"
            style={{ backgroundColor: 'white' }}
          >
            <Typography variant="h3">新規追加</Typography>
          </Button>
        </div> */}
      </div>
    </>
  );
};
