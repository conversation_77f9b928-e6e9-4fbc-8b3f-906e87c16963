import { useTranslation } from 'react-i18next';

import { TermsType } from '@/bundles/model';
import { routes } from '@/routes/routes';

import { GenericTermPage } from './common/GenericTermPage';

export const ContentManagementGuidelines = () => {
  const { t } = useTranslation();

  const type = TermsType.CommunityGuidelines;

  const breadcrumbItems = [
    { name: t('menu.home'), href: routes.home.index },
    { name: t('menu.contentManagement'), href: routes.contentManagement.index },
    {
      name: t('terms.title', '利用規約'),
      href: `${routes.contentManagement.index}${routes.contentManagement.terms.index}`,
    },
    { name: t('terms.guidelines.title', 'コミュニティガイドライン') },
  ];

  return (
    <GenericTermPage
      pageTitleKey="terms.guidelines.title"
      pageTitleDefault="コミュニティガイドライン"
      breadcrumbItemsConfig={breadcrumbItems}
      termsType={type}
    />
  );
};
