import { useTranslation } from 'react-i18next';

import { TermsType } from '@/bundles/model';
import { routes } from '@/routes/routes';

import { GenericTermPage } from './common/GenericTermPage';

export const ContentManagementTermsPolicy = () => {
  const { t } = useTranslation();
  const type = TermsType.PrivacyPolicy;

  const breadcrumbItems = [
    { name: t('menu.home'), href: routes.home.index },
    { name: t('menu.contentManagement'), href: routes.contentManagement.index },
    {
      name: t('terms.title', '利用規約'),
      href: `${routes.contentManagement.index}${routes.contentManagement.terms.index}`,
    },
    { name: t('terms.policy.title', 'プライバシーポリシー') },
  ];

  return (
    <GenericTermPage
      pageTitleKey="terms.policy.title"
      pageTitleDefault="プライバシーポリシー"
      breadcrumbItemsConfig={breadcrumbItems}
      termsType={type}
    />
  );
};
