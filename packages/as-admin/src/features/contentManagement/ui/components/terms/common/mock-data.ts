import { Dayjs } from 'dayjs';

// Shared Interfaces (consider renaming LegalGuidelineBranch for broader use, e.g., TermsBranchData)
export interface LanguageSetting {
  langCode: string; // e.g., 'en', 'es', 'ja'
  langName: string; // e.g., 'English', 'Español', '日本語'
  isActive: boolean;
  url: string | null; // URL to the legal document for this language in this region
}

export interface RegionLegalSetting {
  active: boolean | undefined;
  id: number;
  regionCode: string;
  regionName: string;
  languages: LanguageSetting[];
  lastUpdated: string;
}

// Renamed for more generic use
export interface CommonTermBranchData {
  branchId: string; // e.g., 'main-branch', 'YYYYMMDDmmss'
  branchName: string; // e.g., '#BranchName from image'
  regions: RegionLegalSetting[];
  createdAt: string; // ISO Date string
  updatedAt: string; // ISO Date string
  publishedAt?: string | Dayjs | null; // ISO Date string or Dayjs object
  status: 'draft' | 'published' | 'waitingToPublish' | null;
}

// --- Language Definitions ---
export const SUPPORTED_LANGUAGES: Array<{ code: string; name: string }> = [
  { code: 'en', name: 'English' },
  { code: 'es', name: 'Español' },
  { code: 'fr', name: 'Français' },
  { code: 'it', name: 'Italiano' },
  { code: 'ja', name: '日本語' },
  { code: 'ko', name: '한국어' },
  { code: 'zhCN', name: '简体中文' },
];

// --- Region Definitions ---
export const REGIONS_LIST: Array<{
  code: string;
  name: string;
  idPrefix: string;
}> = [
  { code: 'ARE', name: 'アラブ首長国連邦', idPrefix: 'are' },
  { code: 'AUS', name: 'オーストラリア', idPrefix: 'aus' },
  { code: 'ESP', name: 'スペイン', idPrefix: 'esp' },
  { code: 'FRA', name: 'フランス', idPrefix: 'fra' },
  { code: 'GBR', name: 'イギリス', idPrefix: 'gbr' },
  { code: 'HKG', name: '香港', idPrefix: 'hkg' },
  { code: 'ITA', name: 'イタリア', idPrefix: 'ita' },
  { code: 'JPN', name: '日本', idPrefix: 'jpn' },
  { code: 'KOR', name: '韓国', idPrefix: 'kor' },
  { code: 'MCO', name: 'モナコ', idPrefix: 'mco' },
  { code: 'SAU', name: 'サウジアラビア', idPrefix: 'sau' },
  { code: 'SGP', name: 'シンガポール', idPrefix: 'sgp' },
  { code: 'USA', name: 'アメリカ合衆国', idPrefix: 'usa' },
];

// --- Generic Mock Data ---
const initialRegions: RegionLegalSetting[] = REGIONS_LIST.map(
  (region, index) => ({
    id: 1, // Unique ID for each region
    regionCode: region.code,
    regionName: region.name,
    active: true, // or set logic as needed
    lastUpdated: new Date().toISOString(),
    languages: SUPPORTED_LANGUAGES.map((lang) => {
      let isActive = false;
      let url = null;
      if (region.code === 'JPN' && (lang.code === 'ja' || lang.code === 'en')) {
        isActive = true;
        url = `https://example.com/legal/${region.code.toLowerCase()}/${lang.code}.html`;
      } else if (region.code === 'USA' && lang.code === 'en') {
        isActive = true;
        url = `https://example.com/legal/${region.code.toLowerCase()}/${lang.code}.html`;
      } else if (
        index % 3 === 0 &&
        (lang.code === 'en' ||
          lang.code ===
            SUPPORTED_LANGUAGES[index % SUPPORTED_LANGUAGES.length].code)
      ) {
        isActive = true;
        url = `https://example.com/legal/${region.code.toLowerCase()}/${lang.code}.html`;
      }
      return {
        langCode: lang.code,
        langName: lang.name,
        isActive: isActive,
        url: url,
      };
    }),
  }),
);

export const mockCommonTermBranch: CommonTermBranchData = {
  branchId: '20250529100000',
  branchName: '#BranchName',
  status: 'published',
  createdAt: '2025-05-01T10:00:00Z',
  updatedAt: '2025-05-28T15:30:00Z',
  publishedAt: '2025-05-29T00:00:00Z',
  regions: initialRegions,
};
