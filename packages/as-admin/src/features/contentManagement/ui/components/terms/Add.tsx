import { useNavigate } from 'react-router-dom';

import { But<PERSON> } from '@/components/forms/button';
import { Head } from '@/components/seo/head';
import { routes } from '@/routes/routes';

export const ContentManagementTermsAdd = () => {
  const navigate = useNavigate();
  const router = `${routes.contentManagement.index}${routes.contentManagement.terms.index}`;

  return (
    <>
      <Head title="ドキュメントタイプの追加" />
      <div className="flex flex-col justify-center items-center h-screen bg-white">
        <h1 className="text-6xl font-bold mb-4">
          {'9-2-7/001 ドキュメントタイプの追加 - Adding a document type'}
        </h1>
        <Button onClick={() => navigate(router)}>
          {
            'Go back to the previous page 規約管理 - Terms and Conditions Management'
          }
        </Button>
      </div>
    </>
  );
};
