import {
  CalendarMonthOutlined,
  Close,
  DeleteOutline,
  Publish,
  SaveAs,
} from '@mui/icons-material';
import { Box, Divider, Typography } from '@mui/material';
import clsx from 'clsx';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useState } from 'react';

import { MasterStatus } from '@/bundles/model';
import { DialogState } from '@/components/dialog/CommonDialog';
import { Button } from '@/components/forms';
import { ApplicationDatePicker } from '@/components/forms/date-picker/ApplicationDatePicker';
import { DateFormat } from '@/constants/date';
import { StatusApplicationDate } from '@/constants/utils';

interface GenericTermApplicationDateProps {
  applicationDate?: string | null;
  status?: MasterStatus;
  hasChanges?: boolean;
  onComplete?: (status: StatusApplicationDate) => void;
  setDialogState: (dialogState?: DialogState) => void;
  updateApplicationDate: (applicationDate: string) => void;
  isPublished: boolean;
}

export const GenericTermApplicationDate: React.FC<
  GenericTermApplicationDateProps
> = ({
  applicationDate: initialDate,
  status,
  hasChanges = false,
  setDialogState,
  onComplete,
  updateApplicationDate,
  isPublished,
}) => {
  const [applicationDate, setApplicationDate] = useState<Dayjs | null>(dayjs());

  useEffect(() => {
    // Ensure initialDate is valid before creating a dayjs object
    const newDate =
      initialDate && dayjs(initialDate).isValid()
        ? dayjs(initialDate)
        : dayjs();
    setApplicationDate(newDate);
  }, [initialDate]);

  const isDraft = status === MasterStatus.Draft;
  const isWaitingToPublish = status === MasterStatus.WaitingToPublish;
  const isDraftButtonDisabled = isDraft && !hasChanges;

  const handleBack = (actionStatus: StatusApplicationDate) => {
    if (onComplete) {
      onComplete(actionStatus);
    }
  };

  const handleAction = (
    actionStatus: StatusApplicationDate,
    showConfirm = false,
  ) => {
    if (showConfirm && actionStatus === StatusApplicationDate.Publish) {
      const today = dayjs().format(DateFormat.fullDateWithHyphen);
      const selectedDate =
        applicationDate?.format(DateFormat.fullDateWithHyphen) || '';

      if (dayjs(selectedDate).isSame(dayjs(today), 'day')) {
        setDialogState({
          open: true,
          type: 'alert',
          message: '更新内容をすぐに反映しますか？',
        });
        return;
      }
    } else if (
      showConfirm &&
      actionStatus === StatusApplicationDate.Unpublish
    ) {
      const today = dayjs().format(DateFormat.fullDateWithHyphen);
      const selectedDate =
        applicationDate?.format(DateFormat.fullDateWithHyphen) || '';

      if (dayjs(selectedDate).isSame(dayjs(today), 'day')) {
        setDialogState({
          open: true,
          type: 'alert',
          message: `更新内容をすぐに非公開にしますか？`,
        });
        return;
      }
    }
    handleBack(actionStatus);
  };

  const handleSaveDraft = () => handleAction(StatusApplicationDate.Draft);
  const handlePublish = () => handleAction(StatusApplicationDate.Publish);
  const handleDelete = () => handleAction(StatusApplicationDate.Delete);
  const handleCancel = () => handleAction(StatusApplicationDate.Unpublish);

  const handleDateChange = (value: string) => {
    const newDate = value && dayjs(value).isValid() ? dayjs(value) : dayjs();
    setApplicationDate(newDate);
    updateApplicationDate(newDate.format(DateFormat.fullDateWithHyphen));
  };

  if (isWaitingToPublish) {
    return (
      <Box className="bg-white border sticky top-16 right-1 border-gray-200 rounded-lg px-4 py-6 flex flex-col max-h-[80vh] w-[288px] overflow-auto">
        <Box className="flex-grow">
          <Typography variant="h4" className="pb-2 font-semibold">
            更新適用日
          </Typography>
          <Box className="flex items-center mb-6">
            <Box component="span" className="pr-3">
              <CalendarMonthOutlined className="text-gray-500" />
            </Box>
            <Typography variant="body2">
              {applicationDate?.format(DateFormat.fullDateYYYYMMDDWithDot) ||
                ''}
            </Typography>
          </Box>

          <Divider className="border-t-1 border-outline" />
        </Box>

        <Box className="pt-6">
          <Button
            variant="text"
            color="primary"
            className="!w-full justify-center"
            startIcon={<Close className="!fill-primary" />}
            onClick={handleCancel}
          >
            <Typography variant="h4" className="!text-primary">
              キャンセル
            </Typography>
          </Button>
        </Box>
      </Box>
    );
  }

  return (
    <Box className="bg-white border sticky top-16 right-1 border-gray-200 rounded-lg px-4 py-6 flex flex-col max-h-[80vh] w-[288px] overflow-auto">
      <Box className="flex-grow">
        <ApplicationDatePicker
          value={applicationDate}
          onChange={handleDateChange}
          minDate={dayjs()}
        />

        <Divider className="border-t-1 border-outline" sx={{ mt: 3 }} />
      </Box>

      <Box className="flex flex-col gap-4 pt-6">
        <Box className="flex gap-4 w-full">
          <Button
            variant="outlined"
            className="!w-1/2 !h-12"
            startIcon={
              <SaveAs
                className={`${
                  isDraftButtonDisabled ? '!fill-[#A6A9A9]' : '!fill-primary'
                }`}
              />
            }
            onClick={handleSaveDraft}
            disabled={isDraftButtonDisabled}
          >
            <Typography
              variant="h4"
              className={clsx(
                isDraftButtonDisabled ? '!text-[#A6A9A9]' : '!text-primary',
              )}
            >
              下書き
            </Typography>
          </Button>

          <Button
            variant="contained"
            className="!w-1/2 !h-12"
            startIcon={<Publish className="!fill-onPrimary" />}
            onClick={handlePublish}
            color={isPublished ? 'warning' : 'primary'}
          >
            <Typography variant="h4" className="!text-onPrimary">
              公開
            </Typography>
          </Button>
        </Box>

        <Button
          variant="text"
          className="!w-full justify-center"
          startIcon={
            <DeleteOutline
              className={!isDraft ? '!fill-[#A6A9A9]' : '!fill-red-500'}
            />
          }
          onClick={handleDelete}
          disabled={!isDraft}
        >
          <Typography
            variant="h4"
            className={!isDraft ? '!text-[#A6A9A9]' : '!text-red-500'}
          >
            削除
          </Typography>
        </Button>
      </Box>
    </Box>
  );
};
