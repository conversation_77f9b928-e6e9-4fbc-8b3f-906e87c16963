import CheckCircleOutlineOutlinedIcon from '@mui/icons-material/CheckCircleOutlineOutlined';
import CloseIcon from '@mui/icons-material/Close';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import RadioButtonUncheckedOutlinedIcon from '@mui/icons-material/RadioButtonUncheckedOutlined';
import {
  Box,
  Typography,
  TextField,
  InputAdornment,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import { useState, useEffect } from 'react';

import { Button } from '@/components/forms';

import {
  LanguageSetting,
  RegionLegalSetting,
  SUPPORTED_LANGUAGES,
} from './mock-data';

interface EditableLanguageSetting extends LanguageSetting {
  expanded?: boolean;
}

interface GenericTermEditPanelProps {
  region: RegionLegalSetting;
  tempSettings: LanguageSetting[];
  onLanguageSettingChange: (
    langCode: string,
    field: keyof LanguageSetting,
    value: any,
  ) => void;
  id: string; // FIX: Receive ID as a prop
  onIdChange: (id: string) => void;
  onSaveChanges: () => void;
  showConsentCheckbox?: boolean;
  consentCheckboxLabel?: string;
  consentChecked?: boolean;
  onConsentChange?: (checked: boolean) => void;
}

export const GenericTermEditPanel: React.FC<GenericTermEditPanelProps> = ({
  region,
  tempSettings,
  onLanguageSettingChange,
  id,
  onSaveChanges,
  showConsentCheckbox = true,
  consentCheckboxLabel = '同意必須',
}) => {
  const [editableLanguages, setEditableLanguages] = useState<
    EditableLanguageSetting[]
  >([]);

  // FIX: Remove internal state for the ID
  // const [editableId, setEditableId] = useState<string>(String(region.id));
  // useEffect(() => {
  //   setEditableId(String(region.id));
  // }, [region.id]);

  useEffect(() => {
    const initialLanguages = SUPPORTED_LANGUAGES.map(
      (supportedLang: { code: string; name: any }) => {
        const storeLang = tempSettings.find(
          (s) => s.langCode === supportedLang.code,
        );
        const baseSettings = storeLang || {
          langCode: supportedLang.code,
          langName: supportedLang.name,
          isActive: false,
          url: null,
        };

        return {
          ...baseSettings,
          expanded: baseSettings.isActive && !!baseSettings.url,
        };
      },
    );
    setEditableLanguages(initialLanguages);
  }, [region.id, tempSettings]);

  const handleLanguageToggleActive = (langCode: string) => {
    const currentLang = editableLanguages.find((l) => l.langCode === langCode);
    if (!currentLang) return;

    const newIsActive = !currentLang.isActive;

    setEditableLanguages((prev) =>
      prev.map((l) =>
        l.langCode === langCode
          ? {
              ...l,
              isActive: newIsActive,
              expanded: newIsActive ? l.expanded : false,
            }
          : l,
      ),
    );

    onLanguageSettingChange(langCode, 'isActive', newIsActive);
    if (!newIsActive) {
      onLanguageSettingChange(langCode, 'url', null);
    }
  };

  const handleLanguageExpand = (langCode: string) => {
    setEditableLanguages((prev) =>
      prev.map((lang) =>
        lang.langCode === langCode
          ? { ...lang, expanded: !lang.expanded }
          : lang,
      ),
    );
  };

  const handleUrlChange = (langCode: string, value: string) => {
    const trimmedValue = value.trim();
    const hasValue = trimmedValue !== '';

    setEditableLanguages((prev) =>
      prev.map((l) =>
        l.langCode === langCode
          ? {
              ...l,
              url: value,
              isActive: hasValue,
              expanded: hasValue ? l.expanded : false,
            }
          : l,
      ),
    );

    // Update the URL value
    onLanguageSettingChange(
      langCode,
      'url',
      trimmedValue === '' ? null : trimmedValue,
    );

    // Update the active state based on whether there's a value
    onLanguageSettingChange(langCode, 'isActive', hasValue);
  };

  const handleClearUrl = (langCode: string) => {
    handleUrlChange(langCode, '');
  };

  const getLanguageRowBackground = (lang: EditableLanguageSetting) => {
    const originalStoreSetting = tempSettings.find(
      (s) => s.langCode === lang.langCode,
    );
    const isUrlModified = lang.url !== originalStoreSetting?.url;
    const isActiveModified = lang.isActive !== originalStoreSetting?.isActive;

    if (isUrlModified || isActiveModified) return 'bg-[#EBF3F3]';
    if (lang.expanded) return 'bg-[#E0E0E0]';
    return '';
  };

  return (
    <Box className="bg-white border sticky top-16 right-1 border-gray-200 rounded-lg px-4 py-6 flex flex-col max-h-[80vh] w-[288px] overflow-auto">
      <Box className="flex-grow">
        <Box className="mb-3 px-3 border-b border-gray-200 pb-6">
          <Typography className="!text-sm">ID</Typography>
          <Typography className="!text-sm pt-1">{id}</Typography>
          {/* <TextField
            fullWidth
            multiline
            minRows={2}
            label="ID"
            variant="outlined"
            value={id}
            onChange={handleIdChange}
            placeholder="IDを入力してください"
            InputProps={{
              endAdornment: id && (
                <InputAdornment position="start">
                  <CloseIcon
                    onClick={handleClearId}
                    className="cursor-pointer mr-2 !fill-onSurface"
                  />
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiInputBase-root': {
                alignItems: 'flex-start',
                resize: 'vertical',
                overflow: 'auto',
                fontSize: '14px',
              },
            }}
          /> */}
        </Box>

        <Box className="mb-6">
          {editableLanguages.map((lang) => (
            <Box key={lang.langCode}>
              <Box
                className="flex items-center p-2 rounded hover:bg-[#E0E0E0] border-b border-gray-200 cursor-pointer"
                onClick={() => handleLanguageExpand(lang.langCode)}
                sx={{
                  backgroundColor: getLanguageRowBackground(lang),
                }}
              >
                <Box
                  className="flex items-center mr-2"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleLanguageToggleActive(lang.langCode);
                  }}
                >
                  {lang.isActive ? (
                    <CheckCircleOutlineOutlinedIcon fontSize="small" />
                  ) : (
                    <RadioButtonUncheckedOutlinedIcon fontSize="small" />
                  )}
                </Box>
                <Typography className="mr-2 !text-sm">
                  {lang.langName}
                </Typography>
                <Box className="flex-grow" />
                <Box className="flex items-center cursor-pointer">
                  {lang.expanded ? (
                    <ExpandLessIcon fontSize="small" />
                  ) : (
                    <ExpandMoreIcon fontSize="small" />
                  )}
                </Box>
              </Box>
              {lang.expanded && (
                <Box className="my-2">
                  <TextField
                    fullWidth
                    multiline
                    minRows={2}
                    value={lang.url || ''}
                    onChange={(e) =>
                      handleUrlChange(lang.langCode, e.target.value)
                    }
                    placeholder={`入力してください`}
                    InputProps={{
                      endAdornment: lang.url && (
                        <InputAdornment position="start">
                          <CloseIcon
                            onClick={() => handleClearUrl(lang.langCode)}
                            className="cursor-pointer mr-2 !fill-onSurface"
                          />
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      '& .MuiInputBase-root': {
                        alignItems: 'flex-start',
                        resize: 'vertical',
                        overflow: 'auto',
                        fontSize: '14px',
                      },
                    }}
                  />
                </Box>
              )}
            </Box>
          ))}
        </Box>

        {showConsentCheckbox && (
          <FormControlLabel
            control={<Checkbox size="small" />}
            label={consentCheckboxLabel}
            className="pl-3"
            sx={{
              '& .MuiFormControlLabel-label': {
                fontSize: '14px',
                fontWeight: 'bold',
              },
            }}
          />
        )}
      </Box>

      <Box className="mt-auto pt-4">
        <Button
          variant="contained"
          color="primary"
          fullWidth
          onClick={onSaveChanges}
          className="!h-12 !w-full"
        >
          更新
        </Button>
      </Box>
    </Box>
  );
};
