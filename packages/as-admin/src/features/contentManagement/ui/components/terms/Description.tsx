import { useTranslation } from 'react-i18next';

import { TermsType } from '@/bundles/model';
import { routes } from '@/routes/routes';

import { GenericTermPage } from './common/GenericTermPage';

export const ContentManagementTermsDescription = () => {
  const { t } = useTranslation();
  const type = TermsType.CommercialTransactionLaw;

  const breadcrumbItems = [
    { name: t('menu.home'), href: routes.home.index },
    { name: t('menu.contentManagement'), href: routes.contentManagement.index },
    {
      name: t('terms.title', '利用規約'),
      href: `${routes.contentManagement.index}${routes.contentManagement.terms.index}`,
    },
    { name: t('terms.description.title', '特定商取引法に関する表記') },
  ];

  return (
    <GenericTermPage
      pageTitleKey="terms.description.title"
      pageTitleDefault="特定商取引法に関する表記"
      breadcrumbItemsConfig={breadcrumbItems}
      termsType={type}
    />
  );
};
