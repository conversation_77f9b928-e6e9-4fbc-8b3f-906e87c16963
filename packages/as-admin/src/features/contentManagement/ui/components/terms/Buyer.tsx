import { useTranslation } from 'react-i18next';

import { TermsType } from '@/bundles/model';
import { routes } from '@/routes/routes';

import { GenericTermPage } from './common/GenericTermPage';

export const ContentManagementTermsBuyer = () => {
  const { t } = useTranslation();
  const type = TermsType.BuyerProtection;

  const breadcrumbItems = [
    { name: t('menu.home'), href: routes.home.index },
    { name: t('menu.contentManagement'), href: routes.contentManagement.index },
    {
      name: t('terms.title', '利用規約'),
      href: `${routes.contentManagement.index}${routes.contentManagement.terms.index}`,
    },
    { name: t('terms.buyer.title', '購入者保護ポリシー') },
  ];

  return (
    <GenericTermPage
      pageTitleKey="terms.buyer.title"
      pageTitleDefault="購入者保護ポリシー"
      breadcrumbItemsConfig={breadcrumbItems}
      termsType={type}
    />
  );
};
