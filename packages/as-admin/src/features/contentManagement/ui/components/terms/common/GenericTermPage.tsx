import CheckIcon from '@mui/icons-material/Check';
import {
  Box,
  Button as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Checkbox,
  Chip,
  CircularProgress,
  MenuItem,
  Paper,
  TextField,
  Typography,
} from '@mui/material';
import clsx from 'clsx';
import dayjs from 'dayjs';
import { useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';

import { MasterStatus, TermsType } from '@/bundles/model';
import { CommonDialog } from '@/components/dialog/CommonDialog';
import { Breadcrumb } from '@/components/elements';
import { LoadingIndicator } from '@/components/feedback/loading-indicator';
import { Button } from '@/components/forms';
import { Head } from '@/components/seo/head';
import { DateFormat } from '@/constants/date';
import { StatusApplicationDate, dateFormat } from '@/constants/utils';

import { useCommonTermsContainer } from '../../../containers/terms/common/use-common-terms-container';
import {
  useCreateTermsBranch,
  useDeleteTermsBranch,
  useGetTermsBranches,
  usePublishTermsBranch,
  useUnpublishTermsBranch,
  useUpdateTermsBranch,
} from '../../../hooks/terms/common/use-terms-api';
import { useCommonTermsStore } from '../../../stores/common-terms.store';

import { GenericTermApplicationDate } from './GenericTermApplicationDate';
import { GenericTermEditPanel } from './GenericTermEditPanel';
import { SUPPORTED_LANGUAGES } from './mock-data';

const COMPONENT_STYLES = {
  mainContainer: 'flex px-6 py-4',
  leftPanel: 'transition-all duration-300 ease-in-out',
  rightPanel: 'w-[350px] transition-all duration-300 ease-in-out',
  branchTitle:
    'flex items-center border-b-[2px] mb-2 pt-9 pb-5 border-onSurface',
} as const;

type BreadcrumbItem = {
  name: string;
  href?: string;
};

interface GenericTermPageProps {
  pageTitleKey: string;
  pageTitleDefault: string;
  breadcrumbItemsConfig: BreadcrumbItem[];
  termsType: TermsType;
}

export const GenericTermPage: React.FC<GenericTermPageProps> = ({
  pageTitleKey,
  pageTitleDefault,
  breadcrumbItemsConfig,
  termsType,
}) => {
  const { t } = useTranslation();

  const { data: branches, isLoading: isLoadingBranches } = useGetTermsBranches({
    type: termsType,
  });
  const { setAvailableBranches, setLoading, clearCommonTermsState } =
    useCommonTermsStore();

  useEffect(() => {
    setLoading(isLoadingBranches);
  }, [isLoadingBranches, setLoading]);

  useEffect(() => {
    if (branches) {
      setAvailableBranches(branches);
    }
  }, [branches, setAvailableBranches]);

  const {
    availableBranches,
    selectedBranch,
    originalSelectedBranch,
    isLoading,
    isSubmitting,
    isEditingPage,
    showApplicationDatePanel,
    applicationDate,
    isEditPanelOpen,
    editingRegion,
    setApplicationDate,
    openEditPanel,
    tempLanguageSettings,
    handleLanguageSettingChange,
    validateAllActiveUrls,
    confirmRegionLanguageChanges,
    handleMainEditToggle,
    handleBranchSelectorChange,
    hasChanges,
    dialogState,
    setDialogState,
    setShowApplicationDatePanel,
    toggleRegionActive,
    handleRegionIdChange,
    tempId,
  } = useCommonTermsContainer();

  const { mutate: updateTerm, mutateAsync: updateTermAsync } =
    useUpdateTermsBranch(termsType);
  const { mutate: createTerm, mutateAsync: createTermAsync } =
    useCreateTermsBranch(termsType);
  const { mutate: publishTerm } = usePublishTermsBranch(termsType);
  const { mutate: unpublishTerm } = useUnpublishTermsBranch(termsType);
  const { mutate: deleteTerm } = useDeleteTermsBranch(termsType);

  // Success handler to exit edit mode and reset state
  const handleMutationSuccess = useCallback(() => {
    // Exit edit mode if currently in edit mode
    if (isEditingPage) {
      handleMainEditToggle();
    }
    // Close application date panel
    setShowApplicationDatePanel(false);
    // The originalSelectedBranch will be updated when useGetTermsBranches refetches and setAvailableBranches is called
  }, [isEditingPage, handleMainEditToggle, setShowApplicationDatePanel]);

  useEffect(() => {
    return () => {
      clearCommonTermsState();
    };
  }, [clearCommonTermsState]);

  useEffect(() => {
    if (selectedBranch) {
      const isDraft = selectedBranch.status === MasterStatus.Draft;
      const isWaiting = selectedBranch.status === MasterStatus.WaitingToPublish;
      setShowApplicationDatePanel(isDraft || isWaiting);
    }
  }, [selectedBranch, setShowApplicationDatePanel]);

  const handleUpdateApplicationDate = useCallback(
    (dateString: string) => {
      setApplicationDate(dayjs(dateString).toDate());
    },
    [setApplicationDate],
  );

  const regionsToDisplay = useMemo(() => {
    if (!selectedBranch) {
      return [];
    }
    if (isEditingPage) {
      return selectedBranch.regions;
    }
    return selectedBranch.regions.filter((region) => region.active);
  }, [selectedBranch, isEditingPage]);

  if (isLoading && !selectedBranch) {
    return <LoadingIndicator isLoading={true} fullscreen={true} />;
  }

  if (!selectedBranch) {
    return <LoadingIndicator isLoading={true} fullscreen={true} />;
  }

  const isOverallPublished = selectedBranch.status === 'published';

  const handleSaveDraft = () => {
    if (!selectedBranch) return;
    if (selectedBranch.publishedAt === undefined) {
      updateTerm(selectedBranch as any, {
        onSuccess: () => {
          handleMutationSuccess();
        },
      });
    } else {
      createTerm(selectedBranch as any, {
        onSuccess: () => {
          handleMutationSuccess();
        },
      });
    }
  };

  const handlePublish = async () => {
    if (!selectedBranch) return;

    if (!selectedBranch.branchId) {
      toast.warn(
        'Cannot publish a new branch directly. Please save as a draft first.',
      );
      return;
    }

    try {
      if (selectedBranch.publishedAt === undefined) {
        const res = await updateTermAsync(selectedBranch as any);
        selectedBranch.branchId = res.id as string;
      } else {
        const res = await createTermAsync(selectedBranch as any);
        selectedBranch.branchId = res.id as string;
      }
      publishTerm(
        {
          id: selectedBranch.branchId as string,
          date: applicationDate
            ? dayjs(applicationDate).format('YYYY-MM-DD')
            : '',
        },
        {
          onSuccess: () => {
            handleMutationSuccess();
          },
        },
      );
    } catch (error) {
      console.error(error);
    }
  };

  const handleUnPublish = () => {
    if (!selectedBranch?.branchId) {
      toast.warn('No branch selected to unpublish.');
      return;
    }
    unpublishTerm(selectedBranch.branchId, {
      onSuccess: () => {
        handleMutationSuccess();
      },
    });
  };

  const handleDelete = () => {
    if (selectedBranch?.branchId) {
      deleteTerm(selectedBranch.branchId, {
        onSuccess: () => {
          handleMutationSuccess();
        },
      });
    }
    setDialogState(undefined);
  };

  const handleApplicationDateComplete = (status: StatusApplicationDate) => {
    if (status === StatusApplicationDate.Publish) {
      handlePublish();
    } else if (status === StatusApplicationDate.Draft) {
      handleSaveDraft();
    } else if (status === StatusApplicationDate.Unpublish) {
      handleUnPublish();
    } else if (status === StatusApplicationDate.Delete) {
      handleDelete();
    }
    setShowApplicationDatePanel(false);
  };

  const handleEditDoneClick = () => {
    if (isEditingPage) {
      if (hasChanges) {
        setShowApplicationDatePanel(true);
      }
      handleMainEditToggle();
    } else {
      handleMainEditToggle();
    }
  };

  const shouldShowApplicationDateInRed =
    selectedBranch?.status === MasterStatus.WaitingToPublish;

  return (
    <>
      <Head title={t(pageTitleKey, pageTitleDefault)} />
      <Box>
        <Breadcrumb
          title={t(pageTitleKey, pageTitleDefault)}
          items={breadcrumbItemsConfig}
        />
        <Box
          sx={{
            display: 'flex',
            px: { xs: 2, md: 3 },
            py: 2,
            gap: 2,
          }}
        >
          <Box
            sx={{
              flexGrow: 1,
              transition: 'width 0.3s ease-in-out',
              width: '100%',
            }}
          >
            {/* Filter Section ... */}
            <Box className="bg-surfaceContainer flex items-center justify-start py-2 px-4 rounded-lg gap-[10px]">
              <Box>
                <TextField
                  select
                  label="ブランチ"
                  value={selectedBranch.branchId || ''}
                  onChange={(e) =>
                    handleBranchSelectorChange(e.target.value as string)
                  }
                  sx={{
                    minWidth: 300,
                    backgroundColor: 'white',
                    height: '48px',
                    '& .MuiInputBase-root': {
                      height: '48px',
                    },
                  }}
                  disabled={isEditingPage || isLoading || isSubmitting}
                  size="small"
                  className="!h-12 !w-[300px] !rounded-lg"
                >
                  {availableBranches.map((option) => (
                    <MenuItem key={option.branchId} value={option.branchId}>
                      {option.branchId
                        ? `${dateFormat(option.createdAt, DateFormat.fullDateYYYYMMDDHHmmss)}`
                        : '#BranchName'}
                    </MenuItem>
                  ))}
                </TextField>
              </Box>
              <Button
                variant="contained"
                color="inherit"
                className="!h-12 !w-[130px] !rounded-lg"
                onClick={() => window.location.reload()}
                disabled={
                  isLoading ||
                  !selectedBranch ||
                  (availableBranches.length > 0 &&
                    selectedBranch?.branchId === availableBranches[0]?.branchId)
                }
              >
                読込
              </Button>
            </Box>

            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                borderBottom: 2,
                borderColor: 'onSurface',
                mb: 2,
                pt: { xs: 1, md: 2 },
                pb: 2,
              }}
            >
              <Typography variant="h2" component="h1">
                {selectedBranch.branchId
                  ? `${dateFormat(selectedBranch.createdAt, DateFormat.fullDateYYYYMMDDWithDotHHmm)}`
                  : '#BranchName'}
              </Typography>
              <Box
                sx={{
                  ml: 'auto',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                }}
              >
                {selectedBranch.branchId &&
                  selectedBranch.status === 'draft' &&
                  !isEditingPage && (
                    <Chip
                      label="下書き"
                      size="small"
                      sx={{
                        backgroundColor: '#283430',
                        color: 'white',
                        fontWeight: 'bold',
                        borderRadius: '16px',
                        height: '32px',
                        padding: '0 12px',
                      }}
                    />
                  )}
                <Button
                  variant="contained"
                  color="inherit"
                  onClick={handleEditDoneClick}
                  startIcon={
                    isEditingPage ? (
                      <CheckIcon
                        sx={{
                          fontSize: '18px',
                          marginRight: '4px',
                          color: 'inherit',
                        }}
                      />
                    ) : (
                      <></>
                    )
                  }
                  className={clsx(
                    '!rounded-lg !min-w-20',
                    isEditingPage
                      ? '!bg-secondaryContainer !text-onSecondaryContainer !hover:bg-secondaryContainer'
                      : '!bg-surfaceContainerLow !hover:bg-onSurfaceContainerLow',
                  )}
                  disabled={isLoading && !selectedBranch}
                >
                  {isEditingPage ? '完了' : '編集'}
                </Button>
              </Box>
            </Box>

            {/* Date info section ... */}
            <Box className="flex gap-4  pb-6">
              <Box className="flex items-center gap-1">
                <Typography variant="subtitle2" className="!font-bold">
                  作成日:&nbsp;
                </Typography>
                <Typography variant="subtitle2">
                  {selectedBranch.branchId
                    ? dateFormat(
                        selectedBranch.createdAt,
                        DateFormat.fullDateYYYYMMDDWithDotHHmm,
                      )
                    : '--'}
                </Typography>
              </Box>
              <Box className="flex items-center gap-1">
                <Typography variant="subtitle2" className="!font-bold">
                  最終更新日:&nbsp;
                </Typography>
                <Typography variant="subtitle2">
                  {selectedBranch.branchId && !isEditingPage
                    ? dateFormat(
                        selectedBranch.updatedAt,
                        DateFormat.fullDateYYYYMMDDWithDotHHmm,
                      )
                    : '--'}
                </Typography>
              </Box>
              <Box className="flex items-center gap-1">
                <Typography variant="subtitle2" className="!font-bold">
                  更新適用日:&nbsp;
                </Typography>
                <Typography
                  variant="subtitle2"
                  className={
                    shouldShowApplicationDateInRed ? '!text-error' : ''
                  }
                >
                  {isEditingPage ||
                  !selectedBranch?.publishedAt ||
                  selectedBranch?.status === MasterStatus.Draft
                    ? '--'
                    : dateFormat(
                        selectedBranch.publishedAt as string,
                        DateFormat.fullDateYYYYMMDDWithDot,
                      )}
                </Typography>
              </Box>
            </Box>

            <Paper elevation={2} sx={{ p: { xs: 1, md: 2 } }}>
              {isLoading && regionsToDisplay.length === 0 ? (
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    minHeight: 200,
                  }}
                >
                  <CircularProgress />
                </Box>
              ) : (
                <div>
                  <div className="text-black font-bold text-lg mb-3">
                    国・地域
                  </div>
                  {regionsToDisplay.map((region) => {
                    // Check if this region has any confirmed changes (after update button clicked)
                    const hasRegionChanges =
                      originalSelectedBranch &&
                      (() => {
                        const originalRegion =
                          originalSelectedBranch.regions.find(
                            (r) => r.regionCode === region.regionCode,
                          );
                        if (!originalRegion) return false;

                        // Compare current region.languages with original region.languages
                        return (
                          region.languages.some((currentLang) => {
                            const originalLang = originalRegion.languages.find(
                              (ol) => ol.langCode === currentLang.langCode,
                            );
                            if (!originalLang && currentLang)
                              return !!(
                                currentLang.url || currentLang.isActive
                              );
                            if (originalLang && !currentLang) return false;
                            return (
                              originalLang?.url !== currentLang?.url ||
                              originalLang?.isActive !== currentLang?.isActive
                            );
                          }) ||
                          originalRegion.languages.some((originalLang) => {
                            const currentLang = region.languages.find(
                              (cl) => cl.langCode === originalLang.langCode,
                            );
                            if (!currentLang) return false;
                            return (
                              originalLang?.url !== currentLang?.url ||
                              originalLang?.isActive !== currentLang?.isActive
                            );
                          })
                        );
                      })();

                    return (
                      <div
                        key={region.regionCode}
                        className={clsx(
                          'px-3 pt-1',
                          hasRegionChanges ? 'bg-surfaceTint8' : '',
                          isEditingPage
                            ? 'hover:bg-slate-100 cursor-pointer'
                            : '',
                        )}
                        onClick={() => isEditingPage && openEditPanel(region)}
                      >
                        <div className="flex justify-between items-center mb-2 border-b-2 pb-2 border-dashed">
                          <div className="text-black flex items-center gap-2">
                            {isEditingPage && (
                              <Checkbox
                                checked={region.active}
                                onChange={(e) => {
                                  e.stopPropagation();
                                  toggleRegionActive(region.regionCode);
                                }}
                                onClick={(e) => e.stopPropagation()}
                              />
                            )}
                            <div>
                              <div>{region.regionCode}</div>
                              <div className="text-sm text-gray-500">
                                {region.regionName}
                              </div>
                            </div>
                          </div>
                          <div className="text-primary">{region.id}</div>
                        </div>
                        <Box
                          sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.75 }}
                          className="border-b-2 pt-1 pb-3 mb-3"
                        >
                          {SUPPORTED_LANGUAGES.map((langDef) => {
                            const langSetting = region.languages.find(
                              (rl) => rl.langCode === langDef.code,
                            );
                            const isActive = !!langSetting?.url;

                            // Check if this language has confirmed changes (after update button clicked)
                            const hasLanguageChanges =
                              originalSelectedBranch &&
                              (() => {
                                const originalRegion =
                                  originalSelectedBranch.regions.find(
                                    (r) => r.regionCode === region.regionCode,
                                  );
                                if (!originalRegion) return false;

                                const originalLang =
                                  originalRegion.languages.find(
                                    (ol) => ol.langCode === langDef.code,
                                  );
                                const currentLang = region.languages.find(
                                  (cl) => cl.langCode === langDef.code,
                                );

                                // Compare original and current settings
                                if (!originalLang && !currentLang) return false;
                                if (!originalLang && currentLang)
                                  return !!(
                                    currentLang.url || currentLang.isActive
                                  );
                                if (originalLang && !currentLang) return false;

                                return (
                                  originalLang?.url !== currentLang?.url ||
                                  originalLang?.isActive !==
                                    currentLang?.isActive
                                );
                              })();

                            return (
                              <MuiButton
                                key={langDef.code}
                                size="large"
                                variant="outlined"
                                sx={{
                                  minWidth: '80px',
                                  borderColor: hasLanguageChanges
                                    ? 'transparent'
                                    : 'grey.400',
                                  color: 'text.secondary',
                                  bgcolor: hasLanguageChanges
                                    ? '#B2F2F1'
                                    : isActive
                                      ? 'primary.L95'
                                      : 'transparent',
                                  py: '2px',
                                  px: '8px',
                                  textTransform: 'uppercase',
                                  pointerEvents: 'none',
                                  '.MuiButton-startIcon': {
                                    mr: isActive ? '4px' : '-4px',
                                  },
                                }}
                                startIcon={
                                  isActive ? (
                                    <CheckIcon
                                      sx={{ fontSize: '1rem' }}
                                      className={
                                        hasLanguageChanges
                                          ? ''
                                          : '!fill-primary'
                                      }
                                    />
                                  ) : (
                                    <Box sx={{ width: '1rem' }} />
                                  )
                                }
                              >
                                {langDef.code}
                              </MuiButton>
                            );
                          })}
                        </Box>
                      </div>
                    );
                  })}
                </div>
              )}
            </Paper>
          </Box>

          {/* Right Panel section ... */}
          {(isEditPanelOpen || showApplicationDatePanel) && (
            <Box className={COMPONENT_STYLES.rightPanel}>
              {isEditPanelOpen && editingRegion && tempLanguageSettings && (
                <GenericTermEditPanel
                  region={editingRegion}
                  tempSettings={tempLanguageSettings}
                  onLanguageSettingChange={handleLanguageSettingChange}
                  onSaveChanges={() => {
                    if (!validateAllActiveUrls()) {
                      alert(
                        '有効な言語には正しい形式のURLを入力してください。 (http:// または https://)',
                      );
                    } else {
                      confirmRegionLanguageChanges();
                    }
                  }}
                  id={tempId}
                  onIdChange={handleRegionIdChange}
                />
              )}
              {showApplicationDatePanel && !isEditPanelOpen && (
                <GenericTermApplicationDate
                  applicationDate={applicationDate as string | null}
                  status={selectedBranch.status as MasterStatus}
                  onComplete={handleApplicationDateComplete}
                  setDialogState={setDialogState}
                  updateApplicationDate={handleUpdateApplicationDate}
                  hasChanges={hasChanges}
                  isPublished={isOverallPublished}
                />
              )}
            </Box>
          )}
        </Box>
        {dialogState && (
          <CommonDialog
            open={dialogState.open}
            type={dialogState.type}
            message={dialogState.message}
            onOk={dialogState.onOk}
            onCancel={dialogState.onCancel}
            okText={dialogState.okText || 'OK'}
            cancelText={dialogState.cancelText || '戻る'}
            okColor={dialogState.okColor || '!text-primary'}
          />
        )}
      </Box>
    </>
  );
};
