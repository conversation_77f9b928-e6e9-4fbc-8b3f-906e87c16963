type ContentSearchQuery = {
  contentCategoryCode?: string;
  page?: number;
  limit?: number;
};

interface LocalizedString {
  en: string;
  ja: string;
}

interface RichTextData {
  richText: string;
}

interface LocalizedRichText {
  en: RichTextData;
  ja: RichTextData;
  // You could add more languages here if needed
}

interface UserReference {
  id: string;
  name: string;
}

interface Content {
  id: string;
  title: LocalizedString;
  contentCategoryCode: string;
  description: string;
  content: LocalizedRichText;
  countryCode: string;
  createdBy: UserReference;
  createdAt: string;
  updatedAt: string;
}

export type { ContentSearchQuery, Content };
