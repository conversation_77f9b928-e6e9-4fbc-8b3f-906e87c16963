import { useCommonTermsActions } from '../../../hooks/terms/common/use-common-terms-actions';
import { useCommonTermsDataLoader } from '../../../hooks/terms/common/use-common-terms-data-loader';
import { useCommonTermsRegionForm } from '../../../hooks/terms/common/use-common-terms-region-form';
import { useCommonTermsStore } from '../../../stores/common-terms.store';

export const useCommonTermsContainer = () => {
  const dataLoader = useCommonTermsDataLoader();
  const regionForm = useCommonTermsRegionForm();
  const actions = useCommonTermsActions();

  const storeState = useCommonTermsStore((state) => ({
    availableBranches: state.availableBranches,
    selectedBranch: state.selectedBranch,
    originalSelectedBranch: state.originalSelectedBranch,
    isLoading: state.isLoading,
    isSubmitting: state.isSubmitting,
    isEditingPage: state.isEditingPage,
    showApplicationDatePanel: state.showApplicationDatePanel,
    dialogState: state.dialogState,
    applicationDate: state.applicationDate,
    isEditPanelOpen: state.isEditPanelOpen,
    editingRegion: state.editingRegion,
    setDialogState: state.setDialogState,
    setApplicationDate: state.setApplicationDate,
    openEditPanel: state.openEditPanel,
    closeEditPanel: state.closeEditPanel,
    revertSelectedBranchChanges: state.revertSelectedBranchChanges,
    clearCommonTermsState: state.clearCommonTermsState,
    hasChanges: state.hasChanges,
    setShowApplicationDatePanel: state.setShowApplicationDatePanel,
    toggleRegionActive: state.toggleRegionActive,
  }));

  const handleConfirmRegionChanges = () => {
    actions.confirmRegionLanguageChanges(
      regionForm.tempLanguageSettings,
      regionForm.tempId,
    );
  };

  return {
    ...storeState,
    tempLanguageSettings: regionForm.tempLanguageSettings,
    handleLanguageSettingChange: regionForm.handleLanguageSettingChange,
    tempId: regionForm.tempId,
    handleRegionIdChange: regionForm.handleIdChange,
    hasPanelChanges: regionForm.hasPanelChanges,
    resetPanelForm: regionForm.resetPanelForm,
    validateAllActiveUrls: regionForm.validateAllActiveUrls,
    handleMainEditToggle: actions.handleMainEditToggle,
    handleSaveDraft: actions.handleSaveDraft,
    handlePublish: actions.handlePublish,
    handleUnPublish: actions.handleUnPublish,
    handleApplicationDateCancel: actions.handleApplicationDateCancel,
    confirmRegionLanguageChanges: handleConfirmRegionChanges,
    hasOverallChanges: actions.hasOverallChanges,
    handleBranchSelectorChange: actions.handleBranchSelectorChange,
    loadSpecificBranch: dataLoader.loadSpecificBranch,
  };
};
