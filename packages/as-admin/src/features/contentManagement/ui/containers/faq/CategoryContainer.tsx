import { useEffect, useMemo, useState, useCallback } from 'react';

import { FaqCategoriesBranch, FaqCategory } from '@/bundles/model';
import { DateFormat } from '@/constants/date';
import { dateFormat } from '@/constants/utils';
import { buildCategoryTree } from '@/features/contentManagement/domain/faq/category/utils';
import {
  useFaqCategoriesBranches,
  useCreateFaqCategoriesBranch,
} from '@/features/contentManagement/ui/components/faq/hook/use-faq-categories-branches';
import { useFaqCategoryStore } from '@/features/contentManagement/ui/components/faq/stores/faq-category-store';

const findParentId = (
  nodes: FaqCategory[],
  categoryId: string,
): string | null => {
  for (const node of nodes) {
    if (node.subCategories?.some((c) => c.id === categoryId)) {
      return node.id ?? null;
    }
    if (node.subCategories) {
      const foundParentId = findParentId(node.subCategories, categoryId);
      if (foundParentId) return foundParentId;
    }
  }
  return null;
};

const flattenTree = (
  nodes: FaqCategory[],
  parentId: string | null = null,
): FaqCategory[] => {
  let flatList: FaqCategory[] = [];
  if (!nodes) return flatList;

  for (const node of nodes) {
    const { ...rest } = node;
    const newNode = { ...rest, parentId: parentId };
    flatList.push(newNode as FaqCategory);

    if (node.subCategories && node.subCategories.length > 0) {
      flatList = flatList.concat(flattenTree(node.subCategories, node.id));
    }
  }
  return flatList;
};

const buildApiPayloadTree = (flatList: FaqCategory[]): FaqCategory[] => {
  const categoryMap: {
    [id: string]: FaqCategory & { parentId?: string | null };
  } = {};
  const rootCategories: FaqCategory[] = [];

  flatList.forEach((cat: any) => {
    categoryMap[cat.id] = { ...cat, subCategories: [] };
  });

  flatList.forEach((cat: any) => {
    const parentId = (cat as any).parentId;
    if (parentId && categoryMap[parentId]) {
      if (!Array.isArray(categoryMap[parentId].subCategories)) {
        categoryMap[parentId].subCategories = [];
      }
      (categoryMap[parentId].subCategories as FaqCategory[]).push(
        categoryMap[cat.id],
      );
    } else {
      rootCategories.push(categoryMap[cat.id]);
    }
  });

  const cleanTree = (nodes: FaqCategory[]): FaqCategory[] => {
    return nodes.map((node) => {
      const { ...rest } = node as any;
      const subCategories = rest.subCategories
        ? cleanTree(rest.subCategories as FaqCategory[])
        : [];
      return {
        ...rest,
        subCategories: subCategories,
      };
    });
  };

  const result = cleanTree(rootCategories);
  return result;
};

/**
 * Normalize text for comparison - remove extra spaces and convert to lowercase
 */
const normalizeText = (text: string): string => {
  if (!text) return '';
  return text.trim().replace(/\s+/g, ' ').toLowerCase();
};

/**
 * Check if two displayName objects have same values for same keys
 */
const isDisplayNameEqual = (displayName1: any, displayName2: any): boolean => {
  if (!displayName1 || !displayName2) return false;

  const keys1 = Object.keys(displayName1);
  const keys2 = Object.keys(displayName2);

  // Check if they have at least one common key with same normalized value
  const hasCommonMatch = keys1.some((key) => {
    if (keys2.includes(key)) {
      const normalizedValue1 = normalizeText(displayName1[key]);
      const normalizedValue2 = normalizeText(displayName2[key]);
      return normalizedValue1 === normalizedValue2 && normalizedValue1 !== '';
    }
    return false;
  });

  return hasCommonMatch;
};

/**
 * Find existing category with same displayName in the same level (parentId)
 */
const findDuplicateCategory = (
  flatList: FaqCategory[],
  displayName: any,
  parentId: string | null,
  excludeId?: string,
): FaqCategory | undefined => {
  return flatList.find((cat: any) => {
    return (
      cat.id !== excludeId &&
      cat.parentId === parentId &&
      isDisplayNameEqual(cat.displayName, displayName)
    );
  });
};

export const useCategoryContainer = () => {
  const {
    branches,
    selectedBranch,
    categories,
    selectedCategory,
    selectedCategoryParentId,
    isEditing,
    isLoading,
    showEditor,
    isCreateMode,
    expandedCategories,
    dialogState,
    setDialogState,
    setBranches,
    setSelectedBranch,
    setCategories,
    setSelectedCategory,
    setLoading,
    toggleIsEditing,
    setShowEditor,
    setCreateMode,
    toggleExpandCategory,
    updateSelectedBranchCategories,
    clearState,
  } = useFaqCategoryStore();

  const {
    data: fetchedBranches,
    isLoading: isLoadingBranches,
    refetch: reloadBranches,
  } = useFaqCategoriesBranches();
  const updateBranchMutation = useCreateFaqCategoriesBranch();

  const [initialBranchState, setInitialBranchState] =
    useState<FaqCategoriesBranch | null>(null);

  useEffect(() => {
    if (fetchedBranches) {
      setBranches(fetchedBranches);

      const currentBranchStillExists =
        selectedBranch &&
        fetchedBranches.some((b) => b.id === selectedBranch.id);

      if (fetchedBranches.length === 0) {
        setSelectedBranch(null);
      } else if (!currentBranchStillExists) {
        const sortedBranches = [...fetchedBranches].sort(
          (a: any, b: any) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
        );
        setSelectedBranch(sortedBranches[0].id!);
      }
    }
    // eslint-disable-next-line
  }, [fetchedBranches, setBranches, setSelectedBranch]);

  useEffect(() => {
    if (selectedBranch) {
      const validCategories = (selectedBranch.faqCategories || []).filter(
        (cat) => cat.id,
      );
      const categoryTree = buildCategoryTree(validCategories as any);

      const normalizeSubCategories = (nodes: any[]): FaqCategory[] =>
        nodes.map((node) => ({
          ...node,
          subCategories: Array.isArray(node.subCategories)
            ? normalizeSubCategories(
                (node.subCategories as any[]).filter(
                  (c) => typeof c === 'object' && c !== null && 'id' in c,
                ),
              )
            : [],
        }));

      setCategories(normalizeSubCategories(categoryTree) as FaqCategory[]);
    } else {
      if (categories.length > 0 && !isEditing) {
        setCategories([]);
      }
    }
  }, [selectedBranch, setCategories, isEditing, categories.length]);

  useEffect(() => {
    setLoading(isLoadingBranches);
  }, [isLoadingBranches, setLoading]);

  useEffect(() => () => clearState(), [clearState]);

  const handleSelectBranch = (branchId: string) => {
    if (isEditing) {
      setDialogState({
        open: true,
        type: 'alert',
        message: '編集中はブランチを切り替えられません。',
        okText: 'OK',
      });
      return;
    }
    setSelectedBranch(branchId);
  };

  const handleSelectCategory = (category: FaqCategory) => {
    const parentId = findParentId(categories, category.id!);
    setSelectedCategory(category, parentId);
    setCreateMode(false);
    setShowEditor(true);
  };

  const handleNewCategory = () => {
    setSelectedCategory(null);
    setCreateMode(true);
    setShowEditor(true);
  };

  const handleSaveCategory = (
    updatedData: Partial<FaqCategory>,
    newParentId: string | null,
  ) => {
    const flatList = flattenTree(categories);

    if (isCreateMode) {
      // Check for duplicate category name in the same level when creating new
      const duplicateCategory = findDuplicateCategory(
        flatList,
        updatedData.displayName,
        newParentId,
      );

      if (duplicateCategory) {
        // Update existing category instead of creating new one
        const categoryIndex = flatList.findIndex(
          (c) => c.id === duplicateCategory.id,
        );
        if (categoryIndex > -1) {
          const originalCategory = flatList[categoryIndex];

          // Validate that we have required IDs
          if (!duplicateCategory.id || !originalCategory.id) {
            return;
          }

          // Preserve the original ID and critical fields to prevent data loss
          const updatedCategory = {
            ...originalCategory,
            ...updatedData,
            id: duplicateCategory.id, // Explicitly preserve original ID
            parentId: newParentId,
            // Preserve count and other important fields if not explicitly updated
            count:
              updatedData.count !== undefined
                ? updatedData.count
                : originalCategory.count,
          };

          flatList[categoryIndex] = updatedCategory as any;
        }

        const newTree = buildApiPayloadTree(flatList);

        if (!selectedBranch) {
          setCategories(newTree);
        } else {
          updateSelectedBranchCategories(newTree);
        }

        setShowEditor(false);
        // Set selected category to the existing updated category
        if (categoryIndex > -1) {
          setSelectedCategory(flatList[categoryIndex]);
        }
        return;
      }

      // Create new category if no duplicate found
      const newCategory = {
        active: true,
        count: 0,
        ...updatedData,
        parentId: newParentId,
        id: `new-${Date.now()}`,
      };
      flatList.push(newCategory as any);
    } else if (selectedCategory) {
      // Update existing category
      const categoryIndex = flatList.findIndex(
        (c) => c.id === selectedCategory.id,
      );
      if (categoryIndex > -1) {
        (flatList[categoryIndex] as any) = {
          ...flatList[categoryIndex],
          ...updatedData,
          parentId: newParentId,
        };
      }
    }

    const newTree = buildApiPayloadTree(flatList);

    if (!selectedBranch) {
      setCategories(newTree);
    } else {
      updateSelectedBranchCategories(newTree);
    }

    setShowEditor(false);
    setSelectedCategory(null);
  };

  const handleActiveChange = (categoryId: string, newActiveState: boolean) => {
    const flatList = flattenTree(categories);
    const categoryIndex = flatList.findIndex((c) => c.id === categoryId);
    if (categoryIndex > -1) {
      (flatList[categoryIndex] as any).active = newActiveState;
    }
    const newTree = buildApiPayloadTree(flatList);
    if (selectedBranch) {
      updateSelectedBranchCategories(newTree);
    } else {
      setCategories(newTree);
    }
  };

  const performDelete = () => {
    if (!selectedCategory) return;
    const flatList = flattenTree(categories);
    const updatedFlatList = flatList.filter(
      (cat) => cat.id !== selectedCategory.id,
    );
    const newTree = buildApiPayloadTree(updatedFlatList);
    if (selectedBranch) {
      updateSelectedBranchCategories(newTree);
    } else {
      setCategories(newTree);
    }
    // setDialogState({
    //   open: true,
    //   type: 'success',
    //   message: 'カテゴリを削除しました。',
    //   okText: 'OK',
    // });
    setDialogState(undefined);
    setShowEditor(false);
    setSelectedCategory(null);
  };

  const handleDeleteCategory = () => {
    if (!selectedCategory) return;
    if (
      (selectedCategory.count ?? 0) > 0 ||
      (selectedCategory.subCategories &&
        selectedCategory.subCategories.length > 0)
    ) {
      // setDialogState({
      //   open: true,
      //   type: 'alert',
      //   message:
      //     '配下に記事またはサブカテゴリが存在するカテゴリは削除できません。先に配下の項目を移動または削除してください。',
      //   okText: 'OK',
      // });
      return;
    }

    setDialogState({
      open: true,
      type: 'confirm',
      message: 'このカテゴリを削除してもよろしいですか？',
      okText: '削除',
      cancelText: 'キャンセル',
      onOk: performDelete,
      onCancel: () => setDialogState(undefined),
    });
  };

  const cleanCategoriesForApi = (categoriesToClean: FaqCategory[]): any[] => {
    return categoriesToClean.map((category) => {
      // eslint-disable-next-line
      const { id, parentId, ...restOfCategory } = category as any;
      const newCategory = { ...restOfCategory };
      if (
        Array.isArray(newCategory.subCategories) &&
        newCategory.subCategories.length > 0
      ) {
        newCategory.subCategories = cleanCategoriesForApi(
          newCategory.subCategories,
        );
      }
      return newCategory;
    });
  };

  const handleToggleEdit = () => {
    if (!isEditing) {
      setInitialBranchState(JSON.parse(JSON.stringify(selectedBranch)));
      toggleIsEditing();
    } else {
      let payload: Partial<FaqCategoriesBranch> = {};
      let hasChanges = false;

      if (!selectedBranch) {
        if (categories.length > 0) {
          payload = { faqCategories: categories };
          hasChanges = true;
        }
      } else {
        hasChanges =
          JSON.stringify(selectedBranch?.faqCategories) !==
          JSON.stringify(initialBranchState?.faqCategories);

        if (hasChanges) {
          const { ...branchData } = selectedBranch;
          payload = { ...branchData, faqCategories: categories };
        }
      }

      if (hasChanges) {
        const finalPayload = { ...payload };
        if (finalPayload.faqCategories) {
          finalPayload.faqCategories = cleanCategoriesForApi(
            finalPayload.faqCategories,
          );
        }
        delete finalPayload.id;

        updateBranchMutation.mutate(finalPayload as any, {
          onSuccess: () => {
            setDialogState({
              open: true,
              type: 'success',
              message: 'ブランチが正常に更新されました。',
              okText: 'OK',
            });
            setSelectedBranch(null);
            reloadBranches();
          },
          onError: () => {
            setDialogState({
              open: true,
              type: 'alert',
              message: 'ブランチの更新に失敗しました。',
              okText: 'OK',
            });
          },
        });
      } else {
        setDialogState({
          open: true,
          type: 'info',
          message: '保存する変更はありません。',
          okText: 'OK',
        });
      }
      toggleIsEditing();
      setShowEditor(false);
      setSelectedCategory(null);
      setInitialBranchState(null);
    }
  };

  const handleCategoriesReorder = useCallback(
    (newCategories: FaqCategory[]) => {
      if (selectedBranch) {
        updateSelectedBranchCategories(newCategories);
      } else {
        setCategories(newCategories);
      }
    },
    [selectedBranch, updateSelectedBranchCategories, setCategories],
  );

  const branchOptions = useMemo(() => {
    return (branches as FaqCategoriesBranch[]).map((branch) => ({
      key: `${dateFormat(branch.createdAt, DateFormat.fullDateYYYYMMDDHHmmss)}`,
      value: branch.id!,
    }));
  }, [branches]);

  return {
    branches,
    categories,
    selectedCategory,
    selectedCategoryParentId,
    selectedBranch,
    isEditing,
    expandedCategories,
    isLoading: isLoading || updateBranchMutation.isPending,
    showEditor,
    isCreateMode,
    dialogState,
    setDialogState,
    handleSelectBranch,
    toggleExpand: toggleExpandCategory,
    handleToggleEdit,
    handleSelectCategory,
    handleNewCategory,
    handleSaveCategory,
    handleActiveChange,
    handleDeleteCategory,
    handleCategoriesReorder,
    reloadBranches,
    branchOptions,
  };
};
