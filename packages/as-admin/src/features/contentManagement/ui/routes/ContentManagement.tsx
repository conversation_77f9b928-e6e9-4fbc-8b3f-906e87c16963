import { Typography } from '@mui/material';
import { useNavigate } from 'react-router-dom';

import { Breadcrumb } from '@/components/elements';
import { Button } from '@/components/forms/button';
import { Head } from '@/components/seo/head';
import { routes } from '@/routes/routes';

export const ContentManagement = () => {
  const navigate = useNavigate();
  const router = `${routes.contentManagement.index}`;

  const breadcrumbItems = [
    { name: 'ホーム', href: '/' },
    { name: 'コンテンツ管理' },
  ];

  const buttonItems = [
    {
      title: 'FAQ',
      onClick: () => navigate(`${router}${routes.contentManagement.faq.index}`),
    },
    {
      title: 'ご利用規約',
      onClick: () =>
        navigate(`${router}${routes.contentManagement.terms.index}`),
    },
    {
      title: 'お知らせ',
      onClick: () =>
        navigate(`${router}${routes.contentManagement.announcement.index}`),
    },
    {
      title: '特集',
      onClick: () =>
        navigate(`${router}${routes.contentManagement.specialFeature.index}`),
    },
    {
      title: 'メールニュース',
      onClick: () =>
        navigate(`${router}${routes.contentManagement.email.index}`),
    },
  ];

  return (
    <>
      <Head title="コンテンツ管理ツール" />
      <Breadcrumb title="コンテンツ管理" items={breadcrumbItems} />
      <div className="p-4">
        <div className="grid grid-cols-3 gap-4">
          {buttonItems.map((item, index) => (
            <Button
              key={`item-${index}-title`}
              onClick={item.onClick}
              className="p-6 bg-primary text-center hover:opacity-90 transition-opacity !w-full !h-12"
            >
              <Typography variant="h3" className="!text-onPrimary">
                {item.title}
              </Typography>
            </Button>
          ))}
        </div>
      </div>
    </>
  );
};
