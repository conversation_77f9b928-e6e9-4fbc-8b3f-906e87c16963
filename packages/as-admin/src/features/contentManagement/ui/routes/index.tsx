import { Navigate, Route, Routes } from 'react-router-dom';

import { routes } from '@/routes/routes';

import {
  ContentManagementEmail,
  ContentManagementDistribution,
  ContentManagementSpecialFeature,
  ContentManagementSpecialFeatureAddEdit,
  ContentManagementSpecialFeatureDelete,
  ContentManagementSpecialFeatureList,
  ContentManagementAnnouncement,
  ContentManagementAnnouncementDelete,
  ContentManagementAnnouncementImage,
  ContentManagementFAQ,
  ContentManagementFaqArticle,
  ContentManagementFaqCategory,
  // ContentManagementFaqPreview,
  ContentManagementTerms,
  ContentManagementTermsAdd,
  ContentManagementTermsBuyer,
  ContentManagementTermsDescription,
  ContentManagementTermsPayment,
  ContentManagementTermsPolicy,
  ContentManagementTermsUse,
  ContentManagementGuidelines,
  FaqArticleCreate,
  FaqArticleEdit,
  FaqArticleDetails,
  ContentManagementAnnouncementCreate,
  ContentManagementAnnouncementDetails,
  ContentManagementSpecialFeatureDetails,
  ContentManagementSpecialFeatureNew,
} from '../components';

import { ContentManagement } from './ContentManagement';

export const ContentManagementRoutes = () => {
  return (
    <Routes>
      <Route index element={<ContentManagement />} />
      <Route
        path={routes.contentManagement.faq.wildcard}
        element={<ContentManagementFAQ />}
      />
      <Route
        path={`${routes.contentManagement.faq.index}${routes.contentManagement.faq.article.wildcard}`}
        element={<ContentManagementFaqArticle />}
      />
      <Route
        path={`${routes.contentManagement.faq.index}${routes.contentManagement.faq.article.create.index}`}
        element={<FaqArticleCreate />}
      />
      <Route
        path={`${routes.contentManagement.faq.index}${routes.contentManagement.faq.article.detail}`}
        element={<FaqArticleDetails />}
      />
      <Route
        path={`${routes.contentManagement.faq.index}${routes.contentManagement.faq.article.edit}`}
        element={<FaqArticleEdit />}
      />
      <Route
        path={`${routes.contentManagement.faq.index}${routes.contentManagement.faq.category.wildcard}`}
        element={<ContentManagementFaqCategory />}
      />
      {/* <Route
        path={`${routes.contentManagement.faq.index}${routes.contentManagement.faq.article.preview}`}
        element={<ContentManagementFaqPreview />}
      /> */}

      <Route
        path={routes.contentManagement.terms.wildcard}
        element={<ContentManagementTerms />}
      />
      <Route
        path={`${routes.contentManagement.terms.index}${routes.contentManagement.terms.policy.wildcard}`}
        element={<ContentManagementTermsPolicy />}
      />
      <Route
        path={`${routes.contentManagement.terms.index}${routes.contentManagement.terms.use.wildcard}`}
        element={<ContentManagementTermsUse />}
      />
      <Route
        path={`${routes.contentManagement.terms.index}${routes.contentManagement.terms.guidelines.wildcard}`}
        element={<ContentManagementGuidelines />}
      />
      <Route
        path={`${routes.contentManagement.terms.index}${routes.contentManagement.terms.payment.wildcard}`}
        element={<ContentManagementTermsPayment />}
      />
      <Route
        path={`${routes.contentManagement.terms.index}${routes.contentManagement.terms.buyer.wildcard}`}
        element={<ContentManagementTermsBuyer />}
      />
      <Route
        path={`${routes.contentManagement.terms.index}${routes.contentManagement.terms.description.wildcard}`}
        element={<ContentManagementTermsDescription />}
      />
      <Route
        path={`${routes.contentManagement.terms.index}${routes.contentManagement.terms.add.wildcard}`}
        element={<ContentManagementTermsAdd />}
      />

      <Route
        path={routes.contentManagement.announcement.wildcard}
        element={<ContentManagementAnnouncement />}
      />
      <Route
        path={`${routes.contentManagement.announcement.index}${routes.contentManagement.announcement.create.wildcard}`}
        element={<ContentManagementAnnouncementCreate />}
      />
      <Route
        path={`${routes.contentManagement.announcement.index}${routes.contentManagement.announcement.detail}`}
        element={<ContentManagementAnnouncementDetails />}
      />
      <Route
        path={`${routes.contentManagement.announcement.index}${routes.contentManagement.announcement.image.wildcard}`}
        element={<ContentManagementAnnouncementImage />}
      />
      <Route
        path={`${routes.contentManagement.announcement.index}${routes.contentManagement.announcement.delete.index}`}
        element={<ContentManagementAnnouncementDelete />}
      />
      <Route
        path={routes.contentManagement.specialFeature.wildcard}
        element={<ContentManagementSpecialFeature />}
      />
      <Route
        path={`${routes.contentManagement.specialFeature.index}${routes.contentManagement.specialFeature.create.index}`}
        element={<ContentManagementSpecialFeatureNew />}
      />
      <Route
        path={`${routes.contentManagement.specialFeature.index}${routes.contentManagement.specialFeature.addOrEdit.wildcard}`}
        element={<ContentManagementSpecialFeatureAddEdit />}
      />
      <Route
        path={`${routes.contentManagement.specialFeature.index}${routes.contentManagement.specialFeature.addOrEdit.index}${routes.contentManagement.specialFeature.addOrEdit.list.wildcard}`}
        element={<ContentManagementSpecialFeatureList />}
      />
      <Route
        path={`${routes.contentManagement.specialFeature.index}${routes.contentManagement.specialFeature.addOrEdit.index}${routes.contentManagement.specialFeature.addOrEdit.delete.wildcard}`}
        element={<ContentManagementSpecialFeatureDelete />}
      />
      <Route
        path={`${routes.contentManagement.specialFeature.index}${routes.contentManagement.specialFeature.detail}`}
        element={<ContentManagementSpecialFeatureDetails />}
      />
      <Route
        path={routes.contentManagement.email.wildcard}
        element={<ContentManagementEmail />}
      />
      <Route
        path={`${routes.contentManagement.email.index}${routes.contentManagement.email.distribution.wildcard}`}
        element={<ContentManagementDistribution />}
      />
      <Route path="*" element={<Navigate to="." />} />
    </Routes>
  );
};
