import { useMutation, useQueryClient } from '@tanstack/react-query';

import { Notification } from '@/bundles/model';
import { updateAnnouncement as updateAnnouncementUseCase } from '@/features/contentManagement/application/announcement/update';
import { announcementRepository } from '@/features/contentManagement/infrastructure/repositories/announcement-repository';
import { MutationConfig } from '@/lib/react-query';

const repository = announcementRepository();

type UseUpdateAnnouncementOptions = {
  config?: MutationConfig<
    (variables: {
      id: string;
      notification: Notification;
    }) => Promise<Notification>
  >;
};

export const useUpdateAnnouncement = ({
  config,
}: UseUpdateAnnouncementOptions = {}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      notification,
    }: {
      id: string;
      notification: Notification;
    }) => updateAnnouncementUseCase(repository)(id, notification),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['announcements', id] });
      queryClient.invalidateQueries({ queryKey: ['announcements'] });
    },
    ...config,
  });
};
