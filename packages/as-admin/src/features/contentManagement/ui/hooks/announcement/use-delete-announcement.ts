import { useMutation, useQueryClient } from '@tanstack/react-query';

import { deleteAnnouncement as deleteAnnouncementUseCase } from '@/features/contentManagement/application/announcement/delete';
import { announcementRepository } from '@/features/contentManagement/infrastructure/repositories/announcement-repository';
import { MutationConfig } from '@/lib/react-query';

const repository = announcementRepository();

type UseDeleteAnnouncementOptions = {
  config?: MutationConfig<(id: string) => Promise<void>>;
};

export const useDeleteAnnouncement = ({
  config,
}: UseDeleteAnnouncementOptions = {}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteAnnouncementUseCase(repository)(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['announcements'] });
    },
    ...config,
  });
};
