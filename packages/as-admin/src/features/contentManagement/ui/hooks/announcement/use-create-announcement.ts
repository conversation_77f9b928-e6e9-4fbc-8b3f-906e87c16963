import { useMutation, useQueryClient } from '@tanstack/react-query';

import { Notification } from '@/bundles/model';
import { createAnnouncement as createAnnouncementUseCase } from '@/features/contentManagement/application/announcement/create';
import { announcementRepository } from '@/features/contentManagement/infrastructure/repositories/announcement-repository';
import { MutationConfig } from '@/lib/react-query';

const repository = announcementRepository();

type UseCreateAnnouncementOptions = {
  config?: MutationConfig<
    (notification: Notification) => Promise<Notification>
  >;
};

export const useCreateAnnouncement = ({
  config,
}: UseCreateAnnouncementOptions = {}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (notification: Notification) =>
      createAnnouncementUseCase(repository)(notification),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['announcements'] });
    },
    ...config,
  });
};
