import { useQuery } from '@tanstack/react-query';

import { getAnnouncements as getAnnouncementsUseCase } from '@/features/contentManagement/application/announcement/get-all';
import { announcementRepository } from '@/features/contentManagement/infrastructure/repositories/announcement-repository';
import { QueryConfig } from '@/lib/react-query';

const repository = announcementRepository();

type UseGetAnnouncementsOptions = QueryConfig<
  ReturnType<typeof getAnnouncementsUseCase>
>;

export const useGetAnnouncements = (
  page?: number,
  limit?: number,
  options: UseGetAnnouncementsOptions = {},
) => {
  return useQuery({
    queryKey: ['announcements', page, limit],
    queryFn: () => getAnnouncementsUseCase(repository)(page, limit),
    ...options,
  });
};
