import { useQuery } from '@tanstack/react-query';

import type { Notification as AnnouncementNotification } from '@/bundles/model/models/notification';
import { getAnnouncementById as getAnnouncementByIdUseCase } from '@/features/contentManagement/application/announcement/get-by-id';
import { announcementRepository } from '@/features/contentManagement/infrastructure/repositories/announcement-repository';
import { QueryConfig } from '@/lib/react-query';

const repository = announcementRepository();

type UseGetAnnouncementByIdOptions = QueryConfig<
  (id: string) => Promise<AnnouncementNotification>
>;
export const useGetAnnouncementById = (
  id: string,
  options: UseGetAnnouncementByIdOptions = {},
) => {
  return useQuery<AnnouncementNotification>({
    queryKey: ['announcements', id],
    queryFn: () => getAnnouncementByIdUseCase(repository)(id),
    enabled: !!id,
    ...options,
  });
};
