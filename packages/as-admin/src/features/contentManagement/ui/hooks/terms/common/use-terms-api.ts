import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-toastify';

import { TermsBranch, TermsType } from '@/bundles/model';
import {
  createTermsBranchUseCase,
  deleteTermsBranchUseCase,
  getTermsBranchesUseCase,
  publishTermsBranchUseCase,
  unpublishTermsBranchUseCase, // Import the new use case
  updateTermsBranchUseCase,
} from '@/features/contentManagement/application/terms';
import { GetTermsBranchesParams } from '@/features/contentManagement/domain/terms/types';
import { termsBranchRepository } from '@/features/contentManagement/infrastructure/repositories/terms-branch-repository';

import { useCommonTermsStore } from '../../../stores/common-terms.store';

const repo = termsBranchRepository();

const transformUiDataToApiPayload = (
  uiData: any,
  type: TermsType,
): TermsBranch => {
  const terms = uiData.regions.map((region: any) => {
    const content = region.languages.reduce((acc: any, lang: any) => {
      if (lang.isActive) {
        acc[lang.langCode] = {
          url: lang.url || '',
        };
      }
      return acc;
    }, {});

    return {
      countryCode: region.regionCode,
      active: region.active,
      isRequired: true,
      content,
    };
  });

  return {
    id: uiData.branchId ? uiData.branchId : undefined,
    status: uiData.status,
    type: type,
    terms: terms,
  };
};

// Modified to not reload the page
const showSuccessDialog = (
  setDialogState: (dialogState?: any) => void,
  title: string,
  message: string,
) => {
  setDialogState({
    open: true,
    type: 'success',
    title: title,
    message,
    onOk: () => setDialogState(undefined),
    onCancel: () => setDialogState(undefined),
  });
};

/**
 * Hook to fetch terms branches by type.
 */
export const useGetTermsBranches = (params: GetTermsBranchesParams) => {
  return useQuery({
    queryKey: ['termsBranches', params.type],
    queryFn: () => getTermsBranchesUseCase(repo)(params),
  });
};

/**
 * Hook to update a terms branch.
 */
export const useUpdateTermsBranch = (type: GetTermsBranchesParams['type']) => {
  const queryClient = useQueryClient();
  const { setDialogState, setSubmitting } = useCommonTermsStore();
  return useMutation({
    mutationFn: (data: TermsBranch) => {
      setSubmitting(true);
      const apiPayload = transformUiDataToApiPayload(data, type);
      if (!apiPayload.id) {
        return Promise.reject(new Error('Branch ID is required for update.'));
      }
      return updateTermsBranchUseCase(repo)(apiPayload.id, apiPayload);
    },
    onSuccess: () => {
      showSuccessDialog(
        setDialogState,
        '下書き保存完了',
        '下書きの保存が完了しました。',
      );
      queryClient.invalidateQueries({ queryKey: ['termsBranches', type] });
    },
    onError: (error: any) => {
      toast.error(`更新に失敗しました: ${error.message}`);
    },
    onSettled: () => {
      setSubmitting(false);
    },
  });
};

/**
 * Hook to publish a terms branch.
 */
export const usePublishTermsBranch = (type: GetTermsBranchesParams['type']) => {
  const queryClient = useQueryClient();
  const { setDialogState, setSubmitting } = useCommonTermsStore();
  return useMutation({
    mutationFn: ({ id, date }: { id: string; date: string }) => {
      setSubmitting(true);
      return publishTermsBranchUseCase(repo)(id, date);
    },
    onSuccess: () => {
      showSuccessDialog(
        setDialogState,
        '公開完了',
        '規約が正常に公開されました。',
      );
      queryClient.invalidateQueries({ queryKey: ['termsBranches', type] });
    },
    onError: (error: any) => {
      console.error(`公開に失敗しました: ${error.message}`);
      queryClient.invalidateQueries({ queryKey: ['termsBranches', type] });
    },
    onSettled: () => {
      setSubmitting(false);
    },
  });
};

/**
 * Hook to unpublish a terms branch.
 */
export const useUnpublishTermsBranch = (
  type: GetTermsBranchesParams['type'],
) => {
  const queryClient = useQueryClient();
  const { setDialogState, setSubmitting } = useCommonTermsStore();
  return useMutation({
    mutationFn: (id: string) => {
      setSubmitting(true);
      return unpublishTermsBranchUseCase(repo)(id);
    },
    onSuccess: () => {
      showSuccessDialog(
        setDialogState,
        '公開完了',
        '規約が正常に非公開されました。',
      );
      queryClient.invalidateQueries({ queryKey: ['termsBranches', type] });
    },
    onError: (error: any) => {
      toast.error(`非公開に失敗しました: ${error.message}`);
    },
    onSettled: () => {
      setSubmitting(false);
    },
  });
};

export const useCreateTermsBranch = (type: GetTermsBranchesParams['type']) => {
  const queryClient = useQueryClient();
  const { setDialogState, setSubmitting } = useCommonTermsStore();
  return useMutation({
    mutationFn: (data: Partial<TermsBranch>) => {
      setSubmitting(true);
      const apiPayload = transformUiDataToApiPayload(data, type);
      apiPayload.status = 'draft';
      apiPayload.id = undefined;
      return createTermsBranchUseCase(repo)(apiPayload);
    },
    onSuccess: () => {
      showSuccessDialog(
        setDialogState,
        '下書き保存完了',
        '下書きの保存が完了しました。',
      );
      queryClient.invalidateQueries({
        queryKey: ['termsBranches', type],
      });
    },
    onError: (error: any) => {
      toast.error(`ブランチの作成に失敗しました: ${error.message}`);
    },
    onSettled: () => {
      setSubmitting(false);
    },
  });
};

export const useDeleteTermsBranch = (type: GetTermsBranchesParams['type']) => {
  const queryClient = useQueryClient();
  const { setDialogState, setSubmitting } = useCommonTermsStore();
  return useMutation({
    mutationFn: (id: string) => {
      setSubmitting(true);
      return deleteTermsBranchUseCase(repo)(id);
    },
    onSuccess: () => {
      showSuccessDialog(
        setDialogState,
        'ブランチ削除完了',
        'ブランチが正常に削除されました。',
      );
      queryClient.invalidateQueries({ queryKey: ['termsBranches', type] });
    },
    onError: (error: any) => {
      toast.error(`削除に失敗しました: ${error.message}`);
    },
    onSettled: () => {
      setSubmitting(false);
    },
  });
};
