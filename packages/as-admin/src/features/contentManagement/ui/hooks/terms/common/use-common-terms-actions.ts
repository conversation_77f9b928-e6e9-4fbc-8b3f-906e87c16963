import { useCallback } from 'react';

import { TermsType } from '@/bundles/model';

import { TermsBranch } from '../../../../domain/terms/types';
import { termsBranchRepository } from '../../../../infrastructure/repositories/terms-branch-repository';
import {
  CommonTermBranchData,
  LanguageSetting,
} from '../../../components/terms/common/mock-data';
import { useCommonTermsStore } from '../../../stores/common-terms.store';

const repository = termsBranchRepository();

const transformUiDataToApi = (
  uiData: CommonTermBranchData,
  type: TermsType,
): Partial<TermsBranch> => {
  const payload: Partial<TermsBranch> = {
    type: type,
    status: 'draft',
    terms: uiData.regions
      .map((region) => {
        console.log(region);
        const activeLanguages = region.languages.filter(
          (l) => l.isActive && l.url,
        );
        if (activeLanguages.length === 0) return null;

        return {
          countryCode: region.regionCode,
          active: region.active ? true : false,
          versionId: region.id,
          isRequired: true,
          content: activeLanguages.reduce(
            (acc, lang) => {
              acc[lang.langCode] = {
                active: true,
                title: 'Title Placeholder',
                body: 'Body Placeholder',
                url: lang.url || '',
                attachments: [],
              };
              return acc;
            },
            {} as { [key: string]: any },
          ),
        };
      })
      .filter((t): t is NonNullable<typeof t> => t !== null),
  };

  if (uiData.branchId) {
    payload.id = uiData.branchId;
  }

  return payload;
};

export const useCommonTermsActions = () => {
  const {
    setSubmitting,
    selectedBranch,
    setShowApplicationDatePanel,
    closeEditPanel,
    applyRegionChanges,
    updateEditingRegionId,
  } = useCommonTermsStore();

  const handleSaveDraft = useCallback(
    async (type: TermsType) => {
      if (!selectedBranch) return;

      setSubmitting(true);
      try {
        const apiData = transformUiDataToApi(selectedBranch, type);
        if (selectedBranch.branchId) {
          await repository.updateTermsBranch(
            selectedBranch.branchId,
            apiData as any,
          );
        } else {
          await repository.createTermsBranch(apiData as any);
        }
      } catch (error) {
        console.error('Failed to save draft:', error);
      } finally {
        setSubmitting(false);
      }
    },
    [selectedBranch, setSubmitting],
  );

  const handlePublish = useCallback(
    async (termsType: TermsType) => {
      if (!selectedBranch?.branchId) {
        console.warn(
          'Cannot publish a new branch directly. Please save as a draft first.',
        );
        return;
      }

      setSubmitting(true);
      try {
        await handleSaveDraft(termsType);
        await repository.publishTermsBranch(selectedBranch.branchId, '');
      } catch (error) {
        console.error('Failed to publish:', error);
      } finally {
        setSubmitting(false);
      }
    },
    [selectedBranch, setSubmitting, handleSaveDraft],
  );

  const handleUnPublish = useCallback(async () => {
    if (!selectedBranch?.branchId) {
      console.warn('Cannot unpublish a branch.');
      return;
    }

    setSubmitting(true);
    try {
      await repository.unpublishTermsBranch(selectedBranch.branchId);
    } catch (error) {
      console.error('Failed to unPublish:', error);
    } finally {
      setSubmitting(false);
    }
  }, [selectedBranch, setSubmitting]);

  const handleMainEditToggle = () =>
    useCommonTermsStore.setState((state) => ({
      isEditingPage: !state.isEditingPage,
    }));

  const handleApplicationDateCancel = () =>
    useCommonTermsStore.setState({ showApplicationDatePanel: false });

  const confirmRegionLanguageChanges = (
    languageSettings: LanguageSetting[],
    newId: string,
  ) => {
    updateEditingRegionId(newId);
    applyRegionChanges(languageSettings);
    setShowApplicationDatePanel(true);
    closeEditPanel();
  };

  const hasOverallChanges = () => {
    return false;
  };

  const handleBranchSelectorChange = (id: string) => {
    const branch = useCommonTermsStore
      .getState()
      .availableBranches.find((b) => b.branchId === id);
    if (branch) {
      useCommonTermsStore.setState({ selectedBranch: branch });
    }
  };

  return {
    handleSaveDraft,
    handlePublish,
    handleUnPublish,
    handleMainEditToggle,
    handleApplicationDateCancel,
    confirmRegionLanguageChanges,
    hasOverallChanges,
    handleBranchSelectorChange,
  };
};
