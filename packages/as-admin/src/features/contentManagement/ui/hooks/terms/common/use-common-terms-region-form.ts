import { isEqual } from 'lodash';
import { useEffect, useState } from 'react';

import {
  LanguageSetting,
  SUPPORTED_LANGUAGES,
} from '../../../components/terms/common/mock-data';
import { useCommonTermsStore } from '../../../stores/common-terms.store';

export const useCommonTermsRegionForm = () => {
  const { editingRegion } = useCommonTermsStore();
  const [tempId, setTempId] = useState('');
  const [tempLanguageSettings, setTempLanguageSettings] = useState<
    LanguageSetting[]
  >([]);
  const [originalLanguageSettings, setOriginalLanguageSettings] = useState<
    LanguageSetting[]
  >([]);

  useEffect(() => {
    if (editingRegion) {
      const settings = editingRegion.languages || [];
      setTempId(String(editingRegion.id));
      setTempLanguageSettings(settings);
      setOriginalLanguageSettings(settings);
    } else {
      // Clear form when no region is being edited
      setTempId('');
      setTempLanguageSettings([]);
      setOriginalLanguageSettings([]);
    }
  }, [editingRegion]);

  const handleLanguageSettingChange = (
    langCode: string,
    field: keyof LanguageSetting,
    value: any,
  ) => {
    setTempLanguageSettings((prevSettings) => {
      const newSettings = [...prevSettings];
      let langFound = false;

      // Find and update the existing language setting
      for (let i = 0; i < newSettings.length; i++) {
        if (newSettings[i].langCode === langCode) {
          newSettings[i] = { ...newSettings[i], [field]: value };
          langFound = true;
          break;
        }
      }

      // If the language setting doesn't exist for this region yet, create it
      if (!langFound) {
        const supportedLang = SUPPORTED_LANGUAGES.find(
          (l) => l.code === langCode,
        );
        if (supportedLang) {
          const newLangSetting: LanguageSetting = {
            langCode: langCode,
            langName: supportedLang.name,
            isActive: false,
            url: null,
            [field]: value,
          };
          newSettings.push(newLangSetting);
        }
      }

      return newSettings;
    });
  };

  const validateAllActiveUrls = () => {
    return tempLanguageSettings.every(
      (lang) =>
        !lang.isActive ||
        (lang.isActive &&
          lang.url &&
          (lang.url.startsWith('http://') || lang.url.startsWith('https://'))),
    );
  };

  const hasPanelChanges = !isEqual(
    tempLanguageSettings,
    originalLanguageSettings,
  );

  const resetPanelForm = () => {
    if (editingRegion) {
      setTempLanguageSettings(editingRegion.languages || []);
      setOriginalLanguageSettings(editingRegion.languages || []);
    }
  };

  const handleIdChange = (newId: string) => {
    setTempId(newId);
  };

  return {
    tempId, // Return the temporary ID
    handleIdChange,
    tempLanguageSettings,
    handleLanguageSettingChange,
    dirty: hasPanelChanges,
    hasPanelChanges,
    resetPanelForm,
    validateAllActiveUrls,
  };
};
