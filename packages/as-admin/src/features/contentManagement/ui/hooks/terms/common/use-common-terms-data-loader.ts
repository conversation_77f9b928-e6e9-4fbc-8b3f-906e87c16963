import { useCallback } from 'react';

import { TermsType } from '@/bundles/model';

import { termsBranchRepository } from '../../../../infrastructure/repositories/terms-branch-repository';
import { useCommonTermsStore } from '../../../stores/common-terms.store';

const repository = termsBranchRepository();

export const useCommonTermsDataLoader = () => {
  const { setLoading, setAvailableBranches, setSelectedBranch } =
    useCommonTermsStore();

  const loadTermsBranches = useCallback(
    async (type: TermsType) => {
      setLoading(true);
      try {
        const branches = await repository.getTermsBranches({ type });
        setAvailableBranches(branches as any);
      } catch (error) {
        console.error('Failed to load terms branches:', error);
      } finally {
        setLoading(false);
      }
    },
    [setLoading, setAvailableBranches],
  );

  const loadSpecificBranch = useCallback(
    async (id: string) => {
      const branch = useCommonTermsStore
        .getState()
        .availableBranches.find((b) => b.branchId === id);
      if (branch) {
        setSelectedBranch(branch);
      }
    },
    [setSelectedBranch],
  );

  return { loadTermsBranches, loadSpecificBranch };
};
