import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { toast } from 'react-toastify';

import { GetListEmailResponse } from '@/bundles/model';
import {
  emailRepository,
  GetEmailsRequest,
} from '@/features/contentManagement/infrastructure/repositories/email-repository';

// Get an instance of the repository.
const repository = emailRepository();

/**
 * A React Query hook to fetch the list of emails.
 * We use `useMutation` here as it's for a manually triggered fetch,
 * which is common for filtered data exports.
 */
export const useGetEmails = () => {
  return useMutation<GetListEmailResponse, AxiosError, GetEmailsRequest>({
    mutationFn: (params: GetEmailsRequest) => repository.getEmails(params),
    onSuccess: () => {},
    onError: (error) => {
      toast.error(error.message || 'Failed to fetch emails.');
    },
  });
};
