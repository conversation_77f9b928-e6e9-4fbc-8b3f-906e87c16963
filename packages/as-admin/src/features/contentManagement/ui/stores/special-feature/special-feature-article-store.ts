import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import { SpecialFeature } from '@/features/contentManagement/domain/specialFeature/types';

interface SpecialFeatureArticleState {
  articles: SpecialFeature[];
  isLoading: boolean;
  error: string | null;
  currentPage: number;
  totalPages: number;
  itemsPerPage: number;

  // Actions
  setArticles: (articles: SpecialFeature[]) => void;
  setLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  setCurrentPage: (page: number) => void;
  setTotalPages: (totalPages: number) => void;

  // Async actions will now accept the mutate functions as arguments
  fetchArticles: (
    getSpecialFeatureMutate: (params: {
      filters: any;
      options?: any;
    }) => Promise<SpecialFeature[]>,
  ) => Promise<void>;
  deleteArticle: (
    id: string,
    deleteContentMutate: (id: string) => Promise<void>,
  ) => Promise<void>;
}

export const useSpecialFeatureArticleStore =
  create<SpecialFeatureArticleState>()(
    persist(
      (set, get) => ({
        articles: [],
        isLoading: false,
        error: null,
        currentPage: 1,
        totalPages: 1,
        itemsPerPage: 10,

        setArticles: (articles) => set({ articles }),
        setLoading: (isLoading) => set({ isLoading }),
        setError: (error) => set({ error }),
        setCurrentPage: (page) => set({ currentPage: page }),
        setTotalPages: (totalPages) => set({ totalPages }),

        fetchArticles: async (getSpecialFeatureMutate) => {
          set({ isLoading: true, error: null });
          try {
            const contents = await getSpecialFeatureMutate({
              filters: {},
            });

            set({
              articles: contents,
              isLoading: false,
            });
          } catch (err: any) {
            console.error('Failed to fetch articles:', err);
            set({
              error: err.message || 'Failed to load articles.',
              isLoading: false,
            });
          }
        },

        deleteArticle: async (
          id: string,
          deleteContentMutate: (id: string) => Promise<void>,
        ) => {
          // Now accepts deleteContentMutate
          set({ isLoading: true, error: null });
          try {
            await deleteContentMutate(id);
            // After deletion, refetch the list to update the UI
            await get().fetchArticles; // Pass getContentsMutate again
          } catch (err: any) {
            console.error('Failed to delete article:', err);
            set({
              error: err.message || 'Failed to delete article.',
              isLoading: false,
            });
          }
        },
      }),
      {
        name: 'special-feature-article-storage',
      },
    ),
  );
