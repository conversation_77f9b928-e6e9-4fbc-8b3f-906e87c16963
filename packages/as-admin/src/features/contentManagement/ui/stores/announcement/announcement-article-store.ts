import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import { GetNotificationsResponse, Notification } from '@/bundles/model';
import { DialogState } from '@/components/dialog/CommonDialog';

interface AnnouncementArticleState {
  articles: Notification[];
  isLoading: boolean;
  error: string | null;
  currentPage: number;
  totalPages: number;
  itemsPerPage: number;
  dialogState: DialogState | null;

  // Actions
  setArticles: (articles: Notification[]) => void;
  setLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  setCurrentPage: (page: number) => void;
  setTotalPages: (totalPages: number) => void;
  setDialogState: (state: DialogState | null) => void;

  // Async actions will now accept the mutate functions as arguments
  fetchArticles: (
    getAnnouncementsMutate: (params: {
      filters: any;
      options?: any;
    }) => Promise<GetNotificationsResponse>,
  ) => Promise<void>;
  deleteArticle: (
    id: string,
    deleteContentMutate: (id: string) => Promise<void>,
  ) => Promise<void>;
}

export const useAnnouncementArticleStore = create<AnnouncementArticleState>()(
  persist(
    (set, get) => ({
      articles: [],
      isLoading: false,
      error: null,
      currentPage: 1,
      totalPages: 1,
      itemsPerPage: 10,
      dialogState: null,

      setArticles: (articles) => set({ articles }),
      setLoading: (isLoading) => set({ isLoading }),
      setError: (error) => set({ error }),
      setCurrentPage: (page) => set({ currentPage: page }),
      setTotalPages: (totalPages) => set({ totalPages }),
      setDialogState: (state) => set({ dialogState: state }),

      fetchArticles: async (getAnnouncementsMutate) => {
        set({ isLoading: true, error: null });
        try {
          const { currentPage, itemsPerPage } = get();

          const content = await getAnnouncementsMutate({
            filters: {
              page: currentPage,
              limit: itemsPerPage,
            },
          });

          set({
            articles: content.data,
            totalPages:
              content.pagination?.totalCount && itemsPerPage
                ? Math.ceil(content.pagination?.totalCount / itemsPerPage)
                : 1,
            currentPage: currentPage,
            itemsPerPage: itemsPerPage,
            isLoading: false,
          });
        } catch (err: any) {
          set({
            error: err.message || 'Failed to load articles.',
            isLoading: false,
          });
        }
      },

      deleteArticle: async (
        id: string,
        deleteContentMutate: (id: string) => Promise<void>,
      ) => {
        // Now accepts deleteContentMutate
        set({ isLoading: true, error: null });
        try {
          await deleteContentMutate(id);
          // After deletion, refetch the list to update the UI
          await get().fetchArticles; // Pass getContentsMutate again
        } catch (err: any) {
          console.error('Failed to delete article:', err);
          set({
            error: err.message || 'Failed to delete article.',
            isLoading: false,
          });
        }
      },
    }),
    {
      name: 'announcement-article-storage',
      partialize: (state) => ({
        articles: state.articles,
        currentPage: state.currentPage,
        totalPages: state.totalPages,
        itemsPerPage: state.itemsPerPage,
        // Exclude dialogState, isLoading, and error from persistence
      }),
    },
  ),
);
