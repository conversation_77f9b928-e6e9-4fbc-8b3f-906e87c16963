import { create } from 'zustand';

import { TermsBranch } from '../../domain/terms/types';
import {
  CommonTermBranchData,
  LanguageSetting,
  RegionLegalSetting,
  REGIONS_LIST,
  SUPPORTED_LANGUAGES,
} from '../../ui/components/terms/common/mock-data';

const createNewBranchStructure = (): CommonTermBranchData => {
  const defaultRegions: RegionLegalSetting[] = REGIONS_LIST.map(
    (regionInfo, index) => ({
      id: index + 1,
      regionCode: regionInfo.code,
      regionName: regionInfo.name,
      lastUpdated: new Date().toISOString(),
      active: false,
      languages: SUPPORTED_LANGUAGES.map((lang) => ({
        langCode: lang.code,
        langName: lang.name,
        isActive: false,
        url: null,
      })),
    }),
  );

  return {
    branchId: '',
    branchName: 'New Branch',
    regions: defaultRegions,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    publishedAt: null,
    status: null,
  };
};

const transformApiDataToUi = async (
  apiData: TermsBranch,
): Promise<CommonTermBranchData> => {
  const { REGIONS_LIST, SUPPORTED_LANGUAGES } = await import(
    '../../ui/components/terms/common/mock-data'
  );

  const regionsMap = new Map(apiData.terms?.map((t) => [t.countryCode, t]));

  const allRegions: RegionLegalSetting[] = REGIONS_LIST.map(
    (regionInfo, index) => {
      const term = regionsMap.get(regionInfo.code);
      const languages = SUPPORTED_LANGUAGES.map((lang) => {
        const contentUrl = term?.content?.[lang.code]?.url;
        return {
          langCode: lang.code,
          langName: lang.name,
          isActive: !!contentUrl,
          url: contentUrl || null,
        };
      });

      const matchingTerm = apiData.terms?.find(
        (t) => t.countryCode === regionInfo.code && t.versionId,
      );
      return {
        id: matchingTerm?.versionId ?? index + 1, // Ensure unique ID for each region
        regionCode: regionInfo.code,
        regionName: regionInfo.name,
        lastUpdated: apiData.updatedAt,
        active:
          apiData.terms?.some(
            (t) => t.countryCode === regionInfo.code && t.active,
          ) || false,
        languages,
      };
    },
  );

  return {
    branchId: apiData.id,
    branchName: apiData.id,
    regions: allRegions,
    createdAt: apiData.createdAt,
    updatedAt: apiData.updatedAt,
    publishedAt: apiData.publishedAt,
    status: apiData.status,
  };
};

interface CommonTermsState {
  revertSelectedBranchChanges: any;
  availableBranches: CommonTermBranchData[];
  selectedBranch: CommonTermBranchData | null;
  originalSelectedBranch: CommonTermBranchData | null; // Track original state for changes
  isLoading: boolean;
  isSubmitting: boolean;
  isEditingPage: boolean;
  showApplicationDatePanel: boolean;
  dialogState?: any;
  applicationDate: Date | null;
  isEditPanelOpen: boolean;
  editingRegion: RegionLegalSetting | null;
  editingRegionInit: RegionLegalSetting | null;
  tempLanguageSettings: LanguageSetting[];
  hasChanges: boolean;

  setAvailableBranches: (branches: any) => void;
  setSelectedBranch: (branch: CommonTermBranchData | null) => void;
  setLoading: (isLoading: boolean) => void;
  setSubmitting: (isSubmitting: boolean) => void;
  setEditingPage: (isEditing: boolean) => void;
  setShowApplicationDatePanel: (show: boolean) => void;
  setDialogState: (dialogState?: any) => void;
  setApplicationDate: (date: Date | null) => void;
  openEditPanel: (region: RegionLegalSetting) => void;
  closeEditPanel: () => void;
  clearCommonTermsState: () => void;
  setTempLanguageSettings: (settings: LanguageSetting[]) => void;
  updateLanguageSettingInPanel: (
    langCode: string,
    field: keyof LanguageSetting,
    value: any,
  ) => void;
  applyRegionChanges: (languageSettings: LanguageSetting[]) => void;
  setHasChanges: (hasChanges: boolean) => void;
  toggleRegionActive: (regionId: string) => void;
  updateEditingRegionId: (newId: string) => void;
}

export const useCommonTermsStore = create<CommonTermsState>((set) => ({
  revertSelectedBranchChanges: null,
  availableBranches: [],
  selectedBranch: null,
  originalSelectedBranch: null,
  isLoading: false,
  isSubmitting: false,
  isEditingPage: false,
  showApplicationDatePanel: false,
  dialogState: undefined,
  applicationDate: new Date(),
  isEditPanelOpen: false,
  editingRegion: null,
  editingRegionInit: null,
  tempLanguageSettings: [],
  hasChanges: false,

  setAvailableBranches: async (branches) => {
    if (branches.length > 0) {
      const uiBranches = await Promise.all(branches.map(transformApiDataToUi));
      const selectedBranch = uiBranches[0];
      set({
        availableBranches: uiBranches,
        selectedBranch: selectedBranch,
        originalSelectedBranch: JSON.parse(JSON.stringify(selectedBranch)), // Deep copy for original state
      });
    } else {
      const newBranch = createNewBranchStructure();
      set({
        availableBranches: [newBranch],
        selectedBranch: newBranch,
        originalSelectedBranch: JSON.parse(JSON.stringify(newBranch)), // Deep copy for original state
      });
    }
  },
  setSelectedBranch: (branch) =>
    set({
      selectedBranch: branch,
      originalSelectedBranch: branch
        ? JSON.parse(JSON.stringify(branch))
        : null, // Deep copy for original state
    }),
  setLoading: (isLoading) => set({ isLoading }),
  setSubmitting: (isSubmitting) => set({ isSubmitting }),
  setEditingPage: (isEditing) => set({ isEditingPage: isEditing }),
  setShowApplicationDatePanel: (show) =>
    set({ showApplicationDatePanel: show }),
  setDialogState: (dialogState) => set({ dialogState }),
  setApplicationDate: (date) => set({ applicationDate: date }),
  openEditPanel: (region) =>
    set({
      isEditPanelOpen: true,
      editingRegion: region,
      tempLanguageSettings: JSON.parse(JSON.stringify(region.languages)),
    }),
  closeEditPanel: () => set({ isEditPanelOpen: false, editingRegion: null }),
  setTempLanguageSettings: (settings) =>
    set({ tempLanguageSettings: settings }),

  updateLanguageSettingInPanel: (langCode, field, value) => {
    set((state) => {
      if (!state.tempLanguageSettings) return {};
      const newSettings = state.tempLanguageSettings.map((lang) => {
        if (lang.langCode === langCode) {
          const updatedLang = { ...lang, [field]: value };

          if (field === 'isActive') {
            updatedLang.isActive = value as boolean;
            if (value === false) {
              updatedLang.url = null;
            }
          }

          if (field === 'url') {
            updatedLang.url = value as string | null;
            updatedLang.isActive = !!value;
          }

          return updatedLang;
        }
        return lang;
      });
      return { tempLanguageSettings: newSettings, hasChanges: true };
    });
  },

  applyRegionChanges: (languageSettings) => {
    set((state) => {
      if (!state.selectedBranch || !state.editingRegion) {
        return {};
      }
      const updatedRegions = state.selectedBranch.regions.map((region) => {
        if (region.id === state.editingRegion?.id) {
          return { ...region, languages: languageSettings };
        }
        return region;
      });
      return {
        selectedBranch: { ...state.selectedBranch, regions: updatedRegions },
        hasChanges: true,
      };
    });
  },
  clearCommonTermsState: () =>
    set({
      isEditPanelOpen: false,
      editingRegion: null,
      availableBranches: [],
      selectedBranch: null,
      originalSelectedBranch: null,
      isLoading: false,
      isSubmitting: false,
      isEditingPage: false,
      showApplicationDatePanel: false,
      dialogState: undefined,
      hasChanges: false,
    }),
  setHasChanges: (hasChanges) => set({ hasChanges }),
  updateEditingRegionId: (newId) => {
    set((state) => {
      if (state.editingRegion) {
        const numericId = typeof newId === 'number' ? newId : Number(newId);
        const updatedAvailableBranches = state.availableBranches.map(
          (branch) => {
            if (branch.branchId === state.selectedBranch?.branchId) {
              const newRegions = branch.regions.map((region) =>
                region.id === state.editingRegion!.id
                  ? { ...region, id: numericId }
                  : region,
              );
              return { ...branch, regions: newRegions };
            }
            return branch;
          },
        );

        return {
          editingRegion: { ...state.editingRegion, id: numericId },
          availableBranches: updatedAvailableBranches,
        };
      }
      return {};
    });
  },
  toggleRegionActive: (regionCode: string) =>
    set((state) => {
      if (!state.selectedBranch) return {};
      const updatedRegions = state.selectedBranch.regions.map((region) => {
        if (region.regionCode === regionCode) {
          return { ...region, active: !region.active };
        }
        return region;
      });
      return {
        selectedBranch: { ...state.selectedBranch, regions: updatedRegions },
        hasChanges: true,
      };
    }),
}));
