import { ThemeProvider } from '@mui/material/styles';
import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import * as React from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { HelmetProvider } from 'react-helmet-async';
import { I18nextProvider } from 'react-i18next';
import { BrowserRouter as Router } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';

import { ErrorFallBack } from '@/components/errors/error-fallback';
import { Spinner } from '@/components/feedback/spinner';
import { env } from '@/config';
import i18n from '@/lib/i18n/config';
import { theme } from '@/lib/mui-theme';
import { queryClient } from '@/lib/react-query';
import { AuthProvider } from '@/providers/auth';

// * Remove this if not using global axios
import '@/lib/api-client';

export const AppProvider = ({ children }: { children: React.ReactNode }) => (
  <React.Suspense
    fallback={
      <div className="flex h-screen w-screen items-center justify-center">
        <Spinner size="xl" />
      </div>
    }
  >
    <ErrorBoundary FallbackComponent={ErrorFallBack}>
      <ThemeProvider theme={theme}>
        <ToastContainer
          autoClose={3000}
          limit={3}
          position="top-right"
          theme="colored"
          hideProgressBar
        />
        <QueryClientProvider client={queryClient}>
          {env.DEV && <ReactQueryDevtools />}
          <I18nextProvider i18n={i18n}>
            <HelmetProvider>
              <AuthProvider>
                <Router>{children}</Router>
              </AuthProvider>
            </HelmetProvider>
          </I18nextProvider>
        </QueryClientProvider>
      </ThemeProvider>
    </ErrorBoundary>
  </React.Suspense>
);
