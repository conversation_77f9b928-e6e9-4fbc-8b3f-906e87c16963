import { test, expect } from '@playwright/test';

test('todos', async ({ page }) => {
  await page.goto('/');
  await expect(page).toHaveTitle(/AS | Hello World/);
  await expect(page.getByText(/Hello World!/)).toBeVisible();
  await page.getByRole('button', { name: 'Get started' }).click();

  // Navigate Todo page
  await page.goto('/todos');
  await expect(page).toHaveTitle(/AS | Todo/);

  // Create Todo
  await page.getByPlaceholder('Enter your todo...').fill('Todo 1');
  await page.getByRole('button', { name: 'Submit' }).click();
  await expect(page.getByText(/Todo 1/)).toBeVisible();

  // Delete Todo
  await page.getByTestId('delete-btn').click();
  await expect(page.getByText(/Todo 1/)).toBeHidden();
});
